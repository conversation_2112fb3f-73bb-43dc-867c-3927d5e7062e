import {
  updateOrderStatusRequestSchema,
  UpdateOrderStatusRequestType,
} from "../_schema/saleOrder.schema";
import { fetchData } from "./api";

const URL = `${process.env.NEXT_PUBLIC_API_BASE}/api/admin/v1/order/order-status`;

export const updateOrderStatus = async (data: any) => {
  const requestData = updateOrderStatusRequestSchema.parse(data);

  await fetchData<UpdateOrderStatusRequestType>(URL, {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(requestData),
  });

  return true;
};
