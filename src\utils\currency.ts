const CURRENCY_SYMBOLS: Record<string, string> = {
  // 主要货币
  USD: "$", // 美元 (United States Dollar)
  EUR: "€", // 欧元 (Euro)
  JPY: "¥", // 日元 (Japanese Yen)
  GBP: "£", // 英镑 (British Pound Sterling)
  CNY: "¥", // 人民币 (Chinese Yuan)
  AUD: "A$", // 澳元 (Australian Dollar)
  CAD: "C$", // 加元 (Canadian Dollar)
  CHF: "CHF", // 瑞士法郎 (Swiss Franc)
  HKD: "HK$", // 港币 (Hong Kong Dollar)

  // 亚洲货币
  INR: "₹", // 印度卢比 (Indian Rupee)
  KRW: "₩", // 韩元 (South Korean Won)
  SGD: "S$", // 新加坡元 (Singapore Dollar)
  THB: "฿", // 泰铢 (Thai Baht)
  TWD: "NT$", // 新台币 (New Taiwan Dollar)
  IDR: "Rp", // 印尼盾 (Indonesian Rupiah)
  MYR: "RM", // 马来西亚林吉特 (Malaysian Ringgit)
  PHP: "₱", // 菲律宾比索 (Philippine Peso)
  VND: "₫", // 越南盾 (Vietnamese Đồng)
  PKR: "₨", // 巴基斯坦卢比 (Pakistani Rupee)
  BDT: "৳", // 孟加拉塔卡 (Bangladeshi Taka)
  LKR: "Rs", // 斯里兰卡卢比 (Sri Lankan Rupee)
  NPR: "Rs", // 尼泊尔卢比 (Nepalese Rupee)
  KZT: "₸", // 哈萨克斯坦坚戈 (Kazakhstani Tenge)
  MNT: "₮", // 蒙古图格里克 (Mongolian Tögrög)
  MMK: "K", // 缅甸缅元 (Myanmar Kyat)
  KHR: "៛", // 柬埔寨瑞尔 (Cambodian Riel)
  LAK: "₭", // 老挝基普 (Lao Kip)

  // 欧洲货币
  RUB: "₽", // 俄罗斯卢布 (Russian Ruble)
  TRY: "₺", // 土耳其里拉 (Turkish Lira)
  PLN: "zł", // 波兰兹罗提 (Polish Złoty)
  SEK: "kr", // 瑞典克朗 (Swedish Krona)
  NOK: "kr", // 挪威克朗 (Norwegian Krone)
  DKK: "kr", // 丹麦克朗 (Danish Krone)
  HUF: "Ft", // 匈牙利福林 (Hungarian Forint)
  CZK: "Kč", // 捷克克朗 (Czech Koruna)
  RON: "lei", // 罗马尼亚列伊 (Romanian Leu)
  BGN: "лв", // 保加利亚列弗 (Bulgarian Lev)
  HRK: "kn", // 克罗地亚库纳 (Croatian Kuna)
  RSD: "дин", // 塞尔维亚第纳尔 (Serbian Dinar)
  ALL: "L", // 阿尔巴尼亚列克 (Albanian Lek)
  MKD: "ден", // 马其顿第纳尔 (Macedonian Denar)
  BYN: "Br", // 白俄罗斯卢布 (Belarusian Ruble)
  UAH: "₴", // 乌克兰格里夫纳 (Ukrainian Hryvnia)
  GEL: "₾", // 格鲁吉亚拉里 (Georgian Lari)
  AMD: "֏", // 亚美尼亚德拉姆 (Armenian Dram)
  AZN: "₼", // 阿塞拜疆马纳特 (Azerbaijani Manat)

  // 美洲货币
  MXN: "$", // 墨西哥比索 (Mexican Peso)
  BRL: "R$", // 巴西雷亚尔 (Brazilian Real)
  ARS: "$", // 阿根廷比索 (Argentine Peso)
  CLP: "$", // 智利比索 (Chilean Peso)
  COP: "$", // 哥伦比亚比索 (Colombian Peso)
  PEN: "S/", // 秘鲁索尔 (Peruvian Sol)
  VES: "Bs.S", // 委内瑞拉玻利瓦尔 (Venezuelan Bolívar)
  PYG: "₲", // 巴拉圭瓜拉尼 (Paraguayan Guaraní)
  UYU: "$U", // 乌拉圭比索 (Uruguayan Peso)
  BOB: "Bs.", // 玻利维亚诺 (Bolivian Boliviano)
  CRC: "₡", // 哥斯达黎加科朗 (Costa Rican Colón)
  DOP: "RD$", // 多米尼加比索 (Dominican Peso)
  GTQ: "Q", // 危地马拉格查尔 (Guatemalan Quetzal)
  HNL: "L", // 洪都拉斯伦皮拉 (Honduran Lempira)
  JMD: "J$", // 牙买加元 (Jamaican Dollar)
  NIO: "C$", // 尼加拉瓜科多巴 (Nicaraguan Córdoba)
  PAB: "B/.", // 巴拿马巴波亚 (Panamanian Balboa)

  // 中东货币
  SAR: "﷼", // 沙特里亚尔 (Saudi Riyal)
  AED: "د.إ", // 阿联酋迪拉姆 (UAE Dirham)
  ILS: "₪", // 以色列新谢克尔 (Israeli New Shekel)
  QAR: "﷼", // 卡塔尔里亚尔 (Qatari Riyal)
  KWD: "د.ك", // 科威特第纳尔 (Kuwaiti Dinar)
  OMR: "﷼", // 阿曼里亚尔 (Omani Rial)
  BHD: ".د.ب", // 巴林第纳尔 (Bahraini Dinar)
  JOD: "د.ا", // 约旦第纳尔 (Jordanian Dinar)
  LBP: "ل.ل", // 黎巴嫩镑 (Lebanese Pound)
  SYP: "£", // 叙利亚镑 (Syrian Pound)
  YER: "﷼", // 也门里亚尔 (Yemeni Rial)
  IQD: "ع.د", // 伊拉克第纳尔 (Iraqi Dinar)

  // 非洲货币
  ZAR: "R", // 南非兰特 (South African Rand)
  EGP: "£", // 埃及镑 (Egyptian Pound)
  NGN: "₦", // 尼日利亚奈拉 (Nigerian Naira)
  KES: "KSh", // 肯尼亚先令 (Kenyan Shilling)
  ETB: "Br", // 埃塞俄比亚比尔 (Ethiopian Birr)
  GHS: "GH₵", // 加纳塞地 (Ghanaian Cedi)
  MAD: "د.م.", // 摩洛哥迪拉姆 (Moroccan Dirham)
  DZD: "د.ج", // 阿尔及利亚第纳尔 (Algerian Dinar)
  TND: "د.ت", // 突尼斯第纳尔 (Tunisian Dinar)
  UGX: "USh", // 乌干达先令 (Ugandan Shilling)
  MZN: "MT", // 莫桑比克梅蒂卡尔 (Mozambican Metical)
  ZMW: "ZK", // 赞比亚克瓦查 (Zambian Kwacha)
  XOF: "CFA", // 西非法郎 (West African CFA Franc)
  XAF: "FCFA", // 中非法郎 (Central African CFA Franc)
  RWF: "FRw", // 卢旺达法郎 (Rwandan Franc)
  MWK: "MK", // 马拉维克瓦查 (Malawian Kwacha)
  AOA: "Kz", // 安哥拉宽扎 (Angolan Kwanza)
  MUR: "₨", // 毛里求斯卢比 (Mauritian Rupee)
  SCR: "₨", // 塞舌尔卢比 (Seychellois Rupee)
  SDG: "ج.س.", // 苏丹镑 (Sudanese Pound)
  LYD: "ل.د", // 利比亚第纳尔 (Libyan Dinar)
  CDF: "FC", // 刚果法郎 (Congolese Franc)
  GMD: "D", // 冈比亚达拉西 (Gambian Dalasi)
  SLL: "Le", // 塞拉利昂利昂 (Sierra Leonean Leone)

  // 太平洋地区货币
  NZD: "NZ$", // 新西兰元 (New Zealand Dollar)
  FJD: "FJ$", // 斐济元 (Fiji Dollar)
  PGK: "K", // 巴布亚新几内亚基那 (Papua New Guinean Kina)
  WST: "T", // 萨摩亚塔拉 (Samoan Tālā)
  TOP: "T$", // 汤加潘加 (Tongan Paʻanga)
  SBD: "SI$", // 所罗门群岛元 (Solomon Islands Dollar)

  // 加勒比地区货币
  BBD: "Bds$", // 巴巴多斯元 (Barbadian Dollar)
  BZD: "BZ$", // 伯利兹元 (Belize Dollar)
  TTD: "TT$", // 特立尼达和多巴哥元 (Trinidad and Tobago Dollar)
  XCD: "EC$", // 东加勒比元 (East Caribbean Dollar)

  // 加密货币
  BTC: "₿", // 比特币 (Bitcoin)
  ETH: "Ξ", // 以太坊 (Ethereum)
  LTC: "Ł", // 莱特币 (Litecoin)
  XRP: "Ʀ", // 瑞波币 (Ripple)
  BCH: "Ƀ", // 比特币现金 (Bitcoin Cash)
  DOGE: "Ð", // 狗狗币 (Dogecoin)

  // 特殊货币
  XAU: "XAU", // 黄金 (Gold Troy Ounce)
  XAG: "XAG", // 白银 (Silver Troy Ounce)
  XPT: "XPT", // 铂金 (Platinum Troy Ounce)
  XPD: "XPD", // 钯金 (Palladium Troy Ounce)

  // 其他货币
  ISK: "kr", // 冰岛克朗 (Icelandic Króna)
  MGA: "Ar", // 马达加斯加阿里亚里 (Malagasy Ariary)
  AFN: "؋", // 阿富汗尼 (Afghan Afghani)
  IRR: "﷼", // 伊朗里亚尔 (Iranian Rial)
  TJS: "ЅМ", // 塔吉克斯坦索莫尼 (Tajikistani Somoni)
  TMT: "m", // 土库曼斯坦马纳特 (Turkmenistan Manat)
  UZS: "so'm", // 乌兹别克斯坦苏姆 (Uzbekistani Som)
  KGS: "с", // 吉尔吉斯斯坦索姆 (Kyrgyzstani Som)

  // 补充货币
  BND: "B$", // 文莱元 (Brunei Dollar)
  KYD: "CI$", // 开曼群岛元 (Cayman Islands Dollar)
  BWP: "P", // 博茨瓦纳普拉 (Botswana Pula)
  ZWL: "Z$", // 津巴布韦元 (Zimbabwean Dollar)
  STN: "Db", // 圣多美和普林西比多布拉 (São Tomé and Príncipe Dobra)
  ERN: "Nfk", // 厄立特里亚纳克法 (Eritrean Nakfa)
  SHP: "£", // 圣赫勒拿镑 (Saint Helena Pound)
  FKP: "£", // 福克兰群岛镑 (Falkland Islands Pound)
  GIP: "£", // 直布罗陀镑 (Gibraltar Pound)
  IMP: "£", // 马恩岛镑 (Manx Pound)
  JEP: "£", // 泽西岛镑 (Jersey Pound)
  GGP: "£", // 根西岛镑 (Guernsey Pound)

  // 历史货币 (仍在使用)
  VEF: "Bs", // 委内瑞拉玻利瓦尔 (Venezuelan Bolívar)
  ZMK: "ZK", // 旧赞比亚克瓦查 (Zambian Kwacha - pre-2013)
} as const;

export const getCurrencySymbol = (currency: string) => {
  return CURRENCY_SYMBOLS[currency] ?? "";
};

export function formatCurrency(amount: number, decimalCount = 2, decimal = ".", thousands = ",") {
  const fixedAmount = amount.toFixed(decimalCount);
  const [integer, fraction] = fixedAmount.toString().split('.');
  
  const formattedInteger = integer.replace(
    /\B(?=(\d{3})+(?!\d))/g, 
    thousands
  );
  
  return `${formattedInteger}${decimal}${fraction}`;
}