import { NextRequest } from "next/server";
import { NoteIDResult } from "@/interface/ISalesOrderNoteId";
import {
    authorizedPut,
    getAuthHeaders,
    handleApiError,
} from "@/utils/api";

async function PUT(req: NextRequest, { params }: { params: { selectedDeliveryNoteId: string } }) {
    try {
   
        const status = await req.json().catch(() => ({})); 

        const data = await authorizedPut(
            `/sales-orders/updateDeliveryStatus/${params.selectedDeliveryNoteId}`,
            await getAuthHeaders(req),
            status 
        );

        return Response.json(data, { status: 200 });
    } catch (error) {
        console.error(error);
        return handleApiError(error);
    }
}

export { PUT };