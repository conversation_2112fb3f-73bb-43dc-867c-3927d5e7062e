"use client";
import React from "react";
import { Box, CircularProgress } from "@mui/material";
import { useTranslations } from "next-intl";
import { useQuery } from "@tanstack/react-query";
import PageHeader from "@/components/PageHeader";
import EditButton from "@/components/buttons/EditButton";
import { IRoom } from "@/interface/IRoom";
import xior from "xior";
import LabelValue from "@/components/LabelValue";
import { useRouter } from "next/navigation";
import { ROUTES, UNITY_ENDPOINT } from "@/utils/constants";

interface Props {
  params: { id: string };
}

const RoomDetail = ({ params: { id } }: Props) => {
  const router = useRouter();
  const t = useTranslations("room_information");
  const { data, isLoading, isLoadingError } = useQuery({
    queryKey: ["rooms", id],
    queryFn: () => xior.get<IRoom>(`/api/rooms/${id}`).then((res) => res.data),
  });

  React.useEffect(() => {
    if (isLoadingError) router.push(ROUTES.ROOMS);
  }, [isLoadingError, router]);

  if (isLoading) {
    return (
      <Box
        sx={{ height: "100%" }}
        display={"flex"}
        justifyContent="center"
        alignItems={"center"}
      >
        <CircularProgress color="primary" />
      </Box>
    );
  }
  const splitBgMusicPath = data?.bgMusicUrl?.split("/");

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={"Room Information"}>
        <EditButton
          onClick={() => {
            router.push(`${ROUTES.ROOMS}/${id}/edit`);
          }}
        />
      </PageHeader>
      <Box flex={1} padding="8px 34px">
        <LabelValue label={t("label_room_id")} value={data?.roomId} />
        <LabelValue label={t("label_room_name")} value={data?.roomName} />
        <LabelValue
          label={t("label_room_url")}
          value={UNITY_ENDPOINT + data?.roomUrl}
        />
        <LabelValue label={t("label_description")} value={data?.description} />
        <LabelValue
          label={t("label_tags")}
          value={data?.tags?.map((item) => item.name)}
        />
        <LabelValue
          type={"image"}
          label={t("label_logo")}
          src={data?.logoUrl}
          height={165}
          width={165}
        />
        <LabelValue
          type={"image"}
          label={t("label_thumbnail_image_video")}
          src={data?.thumbnailUrl}
          height={218}
          width={454}
        />
        <LabelValue
          type={"switch"}
          label={t("label_visible_to_public")}
          value={data?.visibleToPublic}
        />
        <LabelValue
          label={t("label_room_bg_music")}
          value={
            splitBgMusicPath && splitBgMusicPath[splitBgMusicPath.length - 1]
          }
        />
        <LabelValue
          label={t("label_room_exclusive_to")}
          value={data?.memberGroups?.map((item) => item.name).join(", ")}
        />
      </Box>
    </Box>
  );
};

export default RoomDetail;
