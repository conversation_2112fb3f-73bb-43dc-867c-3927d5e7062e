import type { NextPage } from "next";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import AddNewButton from "@/components/buttons/AddNewButton";
import ImportButton from "@/components/buttons/ImportButton";
import ExportButton from "@/components/buttons/ExportButton";
import ProductTable from "./components/ProductTable";
import { ROUTES } from "@/utils/constants";

const ProductInformation: NextPage = () => {
  const t = useTranslations("product");

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={t("label_title")}>
        <>
          <ExportButton  />
          <ImportButton  />
          <AddNewButton href={ROUTES.CREATE_PRODUCT} />
        </>
      </PageHeader>
      <Box flex={1} padding="26px 34px">
        <ProductTable />
      </Box>
    </Box>
  );
};

export default ProductInformation;
