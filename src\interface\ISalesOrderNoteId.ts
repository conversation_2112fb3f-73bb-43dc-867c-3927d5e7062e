export interface SalesOrderNoteId {
    noteIdResult: NoteIDResult[];
}

export interface NoteIDResult {
    id?:                          number;
    orderNo?:                     string;
    paymentPlatform?:             number;
    paymentType?:                 number;
    paymentValid?:                number;
    amount?:                      string;
    currency?:                    string;
    type?:                        number;
    description?:                 string;
    createdAt?:                   number;
    updatedAt?:                   number;
    status?:                      number;
    clientName:                  string;
    clientEmail:                 string;
    clientContactN:              number;
    transactionStatus?:           string;
    total?:                       number;
    deliveryStatus:              string;
    deliveryNoteId?:              string;
    handlingStaff:               string;
    remarks:                     string;
    billingAddressCountry?:       string;
    billingAddressAddressline1?:  string;
    billingAddressAddressline2?:  string;
    billingAddressPostalCode?:    number;
    deliveryAddressCountry:      string;
    deliveryAddressAddressline1: string;
    deliveryAddressAddressline2: string;
    deliveryAddressPostalCode?:   number;
    grossTotal?:                  number;
    promoCode?:                   string;
    promoCodeDiscount?:           number;
    taxVat?:                      string;
    grandTotal?:                  number;
    paymentMethod?:               string;
}