export interface ISalesOrderDto {
    orderId:                     string;
    clientName:                  string;
    clientEmail:                 string;
    clientContactNo:             string;
    status:                      string;
    Total:                       number;
    deliveryStatus:              string;
    deliveryNoteId:              string;
    handlingStaff:               string;
    remarks:                     string;
    updateAt:                    string;
    createdAt:                   string;
    billingAddressCountry:       string;
    billingAddressAddressline1:  string;
    billingAddressAddressline2:  string;
    billingAddressAddressline3:  string;
    billingAddressPostalCode:    number;
    deliveryAddressCountry:      string;
    deliveryAddressAddressline1: string;
    deliveryAddressAddressline2: string;
    deliveryAddressAddressline3: string;
    deliveryAddressPostalCode:   number;
    transactionItems:            transactionItem[];
    grossTotal:                  number;
    promoCode:                   string;
    promoCodeDiscount:           number;
    taxVat:                      string;
    grandTotal:                  number;
    paymentMethod:               string;
}

export interface transactionItem {
    tpid:     number;
    product:  Product;
    quantity: number;
    subtotal: number;
}

export interface Product {
    pid:         number;
    name:        string;
    productType: string;
    description: string;
    imageUrl:    string;
    price:       number;
    stock:       number;
    category:    null;
}