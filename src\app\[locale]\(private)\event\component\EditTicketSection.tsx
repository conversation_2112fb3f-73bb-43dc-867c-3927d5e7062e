"use client";
import * as React from "react";
import { Box } from "@mui/material";
import { useRouter } from "next/navigation";
import EditButton from "@/components/buttons/EditButton";
import { useTranslations } from "next-intl";
import { TicketSection } from "../[id]/section/page";

const EditTicketSection = ({ id }: { id: number }) => {
  const router = useRouter();
  const t = useTranslations("ticket")

  const handleNavigation = () => {
    router.push(`/event/${id}/section`)
  }

  return (
    <Box>
      <EditButton onClick={handleNavigation} isPrimay={false} isSmall={true} label={t("edit_section")}/>
    </Box>
  );
};

export default EditTicketSection;
