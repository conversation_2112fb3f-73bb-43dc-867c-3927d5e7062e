"use client";
import { useRef } from "react";
import { Box, Stack } from "@mui/material";
import Grid from "@mui/material/Unstable_Grid2";
import SalesOrderCustomerDetail from "./detail/SalesOrderCustomerDetail";
import SalesOrderShippingDetail from "./detail/SalesOrderShippingDetail";
import SalesOrderOrderSummary from "./detail/SalesOrderOrderSummary";
import SalesOrderRemark from "./detail/SalesOrderRemark";
import SalesOrderPaymentStatus from "./detail/SalesOrderPaymentStatus";
import SalesOrderItemSummary from "./detail/SalesOrderItemSummary";
import { SaleOrderDetailType } from "../../_schema/saleOrder.schema";
import SalesOrderTitle from "./SalesOrderTitle";

type Props = {
  saleOrderDetail?: SaleOrderDetailType;
  country?: string;
};

const SalesOrderDetailContainer = ({ saleOrderDetail, country }: Props) => {
  const contentRef = useRef<HTMLDivElement>(null);

  return (
    <Box ref={contentRef}>
      {/*  Additional Box is for PDF export */}
      <Box marginLeft={4}>
        <Box marginBottom={3}>
          <SalesOrderTitle orderNo={saleOrderDetail?.orderNo ?? ""} contentRef={contentRef} />
        </Box>
        <Grid container rowSpacing={1} columnSpacing={{ xs: 1 }} sx={{ paddingRight: "32px" }}>
          <Grid container xs={4} rowSpacing={3} sx={{ maxWidth: "400px" }}>
            <Grid xs={12}>
              <Stack spacing={3}>
                <SalesOrderCustomerDetail saleOrderDetail={saleOrderDetail} country={country} />
                <SalesOrderShippingDetail saleOrderDetail={saleOrderDetail} />
                <SalesOrderOrderSummary saleOrderDetail={saleOrderDetail} />
                <SalesOrderRemark saleOrderDetail={saleOrderDetail} />
              </Stack>
            </Grid>
          </Grid>
          <Grid container xs rowSpacing={1}>
            <Grid xs={12}>
              <Stack spacing={3}>
                <SalesOrderPaymentStatus saleOrderDetail={saleOrderDetail} />
                <SalesOrderItemSummary saleOrderDetail={saleOrderDetail} />
              </Stack>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default SalesOrderDetailContainer;
