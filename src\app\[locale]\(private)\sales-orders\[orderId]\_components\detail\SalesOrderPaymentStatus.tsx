"use client";
import {
  <PERSON>,
  But<PERSON>,
  Chip,
  CircularProgress,
  MenuItem,
  Paper,
  Select,
  SelectChangeEvent,
  Stack,
  styled,
} from "@mui/material";
import { useTranslations } from "next-intl";
import {
  orderShippingStatus,
  OrderShippingStatusType,
  OrderShippingType,
  orderShippingType,
  orderStatus,
} from "@/app/[locale]/(private)/sales-orders/_enums/order.enum";
import OrderStatusTag from "../OrderStatusTag";
import DeliveryStatusTag from "../DeliveryStatusTag";
import { useEffect, useState } from "react";
import CheckIcon from "@/components/icons/CheckIcon";
import { SaleOrderDetailType } from "../../../_schema/saleOrder.schema";
import { updateShippingStatus } from "../../../_actions/updateShippingStatus";
import { revalidateRetrieveOrderById } from "../../../_actions/retrieveOrderById.action";
import { updateOrderStatus } from "../../../_actions/updateOrderStatus";

type Props = {
  saleOrderDetail?: SaleOrderDetailType;
};
interface ChangeStatusContainerProps {
  orderId: number;
  type: OrderShippingType;
  status: OrderShippingStatusType;
}

const StyledBox = styled(Paper)(({ theme }) => ({
  borderRadius: 16,
  border: `1px solid ${theme.palette.incutix.grey[400]}`,
  backgroundColor: theme.palette.incutix.white,
}));
const DetailItemLabel = styled(Box)(() => ({
  minWidth: "150px",
  display: "flex",
  justifyContent: "left",
  alignItems: "center",
  fontSize: 16,
  fontWeight: 700,
  color: "#000000",
  textWrap: "nowrap",
}));
const DetailItemValue = styled(Box)(({ theme }) => ({
  //   height: 20,
  display: "flex",
  width: "100%",
  justifyContent: "right",
  alignItems: "center",
  fontSize: 14,
  fontWeight: 700,
  color: theme.palette.incutix.grey[600],
}));
const StatusBox = styled(Box)(({ theme }) => ({
  display: "flex",
  flexDirection: "row",
  borderBottom: `1px solid ${theme.palette.incutix.grey[400]}`,
  padding: "20px",
}));
const ChangeStatusButton = styled(Button)(({ theme }) => ({
  fontSize: 14,
  fontWeight: 700,
  color: theme.palette.incutix.grey[600],
  backgroundColor: theme.palette.incutix.white,
  "&:hover": {
    backgroundColor: theme.palette.incutix.white,
  },
  padding: 0,
  "& span.MuiCircularProgress-root": {
    color: theme.palette.incutix.primary[200],
  },
}));
const ChangeStatusCheckButton = styled(Button)(({ theme }) => ({
  maxWidth: 48,
  minWidth: 48,
  width: 48,
  height: 48,
  backgroundColor: theme.palette.incutix.primary[200],
  borderRadius: 999,
  "&:hover": {
    backgroundColor: theme.palette.incutix.primary[100],
  },
  "& span.MuiCircularProgress-root": {
    color: theme.palette.incutix.white,
  },
}));
const ChangeStatusSelect = styled(Select)(({ theme }) => ({
  "& fieldset": {
    border: 0,
  },
  "&.MuiInputBase-root": {
    width: "240px",
    borderRadius: 999,
    border: `1px solid ${theme.palette.incutix.grey[400]} !important`,
    "&:hover": {
      border: `1px solid ${theme.palette.incutix.grey[600]} !important`,
    },
    "&.Mui-focused": {
      border: `1px solid ${theme.palette.incutix.grey[600]} !important`,
    },
  },
  "& div.MuiSelect-select": {
    border: 0,
    padding: "12px 20px",
    fontSize: 14,
    fontWeight: 700,
    color: "#000000",
  },
}));
const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  color: "#000000",
  "&.Mui-selected": {
    backgroundColor: theme.palette.incutix.primary[200],
    color: theme.palette.incutix.white,
    "&:hover": {
      backgroundColor: theme.palette.incutix.primary[200],
      color: theme.palette.incutix.white,
    },
  },
  "&:hover": {
    backgroundColor: theme.palette.incutix.primary[400],
  },
}));

const ChangeStatusContainer = ({ orderId, type, status }: ChangeStatusContainerProps) => {
  const t = useTranslations("sales_orders.detail.status");
  const tTag = useTranslations("sales_orders.table.tag.delivery_status");

  const [isChangeStatusPanelOpen, setIsChangeStatusPanelOpen] = useState<boolean>(false);
  const [shippingStatus, setShippingStatus] = useState<OrderShippingStatusType>();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (status === orderShippingStatus.delivered) {
      setShippingStatus(orderShippingStatus.pending);
    } else if (status === orderShippingStatus.pending) {
      setShippingStatus(orderShippingStatus.delivered);
    }
  }, [status]);
  useEffect(() => {
    // Close the change status after update status.
    setIsChangeStatusPanelOpen(false);
    setIsLoading(false);
  }, [status]);

  const handleChangeStatusClick = () => {
    setIsChangeStatusPanelOpen(true);
  };
  const handleCloseChangeStatusClick = () => {
    setIsChangeStatusPanelOpen(false);
  };
  const handleChange = (event: SelectChangeEvent<unknown>) => {
    const value: string = event.target.value as string;
    setShippingStatus(value);
  };
  const handleChangeShippingStatusClick = () => {
    setIsLoading(true);
    updateShippingStatus({ orderId: orderId, shippingStatus: shippingStatus }).finally(() => {
      revalidateRetrieveOrderById();
    });
  };

  if (!isChangeStatusPanelOpen) {
    return (
      <ChangeStatusButton
        disableElevation
        disableFocusRipple
        disableRipple
        variant="text"
        onClick={handleChangeStatusClick}
      >
        {t("change_status")}
      </ChangeStatusButton>
    );
  }
  return (
    <Stack direction={"row"} spacing={2}>
      <ChangeStatusSelect value={shippingStatus} onChange={handleChange}>
        {status === orderShippingStatus.delivered && (
          <StyledMenuItem value={orderShippingStatus.pending}>{tTag("pending")}</StyledMenuItem>
        )}
        {type === orderShippingType.express && (
          <StyledMenuItem value={orderShippingStatus.delivered}>
            {tTag("express_delivered")}
          </StyledMenuItem>
        )}
        {type === orderShippingType.storePickup && (
          <StyledMenuItem value={orderShippingStatus.delivered}>
            {tTag("store_delivered")}
          </StyledMenuItem>
        )}
      </ChangeStatusSelect>

      <Box sx={{ display: "flex", alignItems: "center" }}>
        <ChangeStatusCheckButton onClick={handleChangeShippingStatusClick} disabled={isLoading}>
          {!isLoading ? <CheckIcon /> : <CircularProgress size={24} color={"primary"} />}
        </ChangeStatusCheckButton>
      </Box>
      <ChangeStatusButton
        disableElevation
        disableFocusRipple
        disableRipple
        variant="text"
        onClick={handleCloseChangeStatusClick}
      >
        {t("cancel")}
      </ChangeStatusButton>
    </Stack>
  );
};

const SalesOrderPaymentStatus = ({ saleOrderDetail }: Props) => {
  const t = useTranslations("sales_orders.detail.status");

  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    setIsLoading(false);
  }, [saleOrderDetail]);

  const data = {
    orderStatus: saleOrderDetail?.orderStatus ?? "",
    shippingType: saleOrderDetail?.orderShipping.shippingType ?? "",
    shippingStatus: saleOrderDetail?.orderShipping.shippingStatus ?? "",
  };

  const handleMarkAsComplete = () => {
    setIsLoading(true);
    updateOrderStatus({
      orderId: saleOrderDetail?.orderId ?? 0,
      orderStatus: orderStatus.complete,
    }).finally(() => {
      revalidateRetrieveOrderById();
    });
  };

  return (
    <StyledBox elevation={0}>
      <Stack>
        <StatusBox>
          <DetailItemLabel>{t("payment_status")}</DetailItemLabel>
          <OrderStatusTag status={data.orderStatus} />
          <DetailItemValue>
            {saleOrderDetail?.orderStatus !== orderStatus.complete && (
              <ChangeStatusButton
                disableElevation
                disableFocusRipple
                disableRipple
                variant="text"
                onClick={handleMarkAsComplete}
              >
                {!isLoading ? t("mark_as_complete") : <CircularProgress size={24} />}
              </ChangeStatusButton>
            )}
          </DetailItemValue>
        </StatusBox>
        <StatusBox sx={{ border: 0 }}>
          <DetailItemLabel>{t("delivery_status")}</DetailItemLabel>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <DeliveryStatusTag status={data.shippingStatus} shippingType={data.shippingType} />
          </Box>
          <DetailItemValue>
            <ChangeStatusContainer
              orderId={saleOrderDetail?.orderId ?? 0}
              type={data.shippingType}
              status={data.shippingStatus}
            />
          </DetailItemValue>
        </StatusBox>
      </Stack>
    </StyledBox>
  );
};

export default SalesOrderPaymentStatus;
