"use client";
import dayjs from "dayjs";
import { stringify } from "csv-stringify";
import { useTranslations } from "next-intl";
import { Readable } from "stream";

import { styled, Button } from "@mui/material";
import ExportToExcelIcon from "@/components/icons/ExportToExcelIcon";
import { getCurrencySymbol } from "@/utils/currency";

import { OrderListType } from "../../../_schema/saleOrder.schema";
import { useSelectedOrder } from "../../_hooks";

interface CSVExporterProps {
  data: OrderListType[];
}

function streamToBlob(readableStream: any): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const chunks: any[] = [];
    readableStream
      .on("data", (chunk: any) => chunks.push(chunk))
      .on("end", () => {
        const buffer = Buffer.concat(chunks);
        resolve(
          new Blob(["\uFEFF" + buffer], {
            type: "text/csv;charset=utf-8",
          })
        );
      })
      .on("error", reject);
  });
}

const ExportButton = styled(Button)(({ theme }) => {
  return {
    borderRadius: "999px",
    padding: "12px 24px",
    backgroundColor: theme.palette.incutix.primary[200],
    ":hover": {
      backgroundColor: theme.palette.incutix.primary[100],
    },
  };
});

const CSVExporter = ({ data }: CSVExporterProps) => {
  const t = useTranslations("sales_orders");
  const [selectedOrders] = useSelectedOrder();

  const handleExport = () => {
    const result = data
      .filter((x) => (selectedOrders.length === 0 ? true : selectedOrders.includes(x.orderId)))
      .map((row) => ({
        ...row,
        orderAmount: `${getCurrencySymbol(row.currency)} ${row.orderAmount}`,
        create_at: dayjs.unix(row.create_at).format("YYYY/MM/DD HH:mm:ss"),
      }));

    const csvOptions = {
      header: true,
      columns: {
        orderStatus: "Payment Status",
        orderNo: "Order Number",
        userName: "Customer",
        userEmail: "Customer Email",
        orderAmount: "Total",
        shippingStatus: "Delivery Status",
        create_at: "Create At",
      },
    };

    try {
      const dataStream = Readable.from(result);
      const csvStream = stringify(csvOptions);
      const transformStream = dataStream.pipe(csvStream);

      streamToBlob(transformStream).then((blob) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "sales-order.csv";
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(link);
      });
    } catch (error) {
      alert("Download failed");
    }
  };

  return (
    <ExportButton
      variant="contained"
      size="large"
      startIcon={<ExportToExcelIcon />}
      onClick={handleExport}
    >
      {t("export_to_excel")}
    </ExportButton>
  );
};

export default CSVExporter;
