"use client";

import ModalContainer from "@/components/ModalContainer";
import CancelButton from "@/components/buttons/CancelButton";
import EditButton from "@/components/buttons/EditButton";
import SaveButton from "@/components/buttons/SaveButton";
import TextField from "@/components/input/TextField";
import { IMemberGroup } from "@/interface/IMemberGroup";
import { MemberGroupSchema } from "@/schemas/MemberGroupSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Modal, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import * as React from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";

type FormValue = z.infer<typeof MemberGroupSchema>;

interface Props {
  group: IMemberGroup;
}

const EditMemberGroup = ({ group }: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors, isDirty },
    setError,
    clearErrors,
    reset,
  } = useForm<FormValue>({
    defaultValues: { name: group.name },
    resolver: zodResolver(MemberGroupSchema),
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (isSubmitting) return;
    setOpen(false);
    clearErrors();
    if (isDirty) {
      reset();
    }
  }, [clearErrors, isDirty, isSubmitting, reset]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      await xior.put(`/api/member-groups/${group.id}`, data);
      queryClient.invalidateQueries({ queryKey: ["member_groups"] });
      setOpen(false);
      reset({ name: data.name });
    } catch (e) {
      setError("name", { type: "custom", message: "duplicated_member_group" });
    }
  };

  return (
    <Box>
      <EditButton sx={{ marginRight: 1.5 }} onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("member_group.title_edit_member_group")}
            </Typography>
          </Box>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                value={field.value}
                onChange={field.onChange}
                required
                disabled={isSubmitting}
                label={t("member_group.label_group_name")}
                error={errors?.name?.message}
              />
            )}
          />

          <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
            <CancelButton disabled={isSubmitting} onAction={handleClose} />
            <SaveButton disabled={isSubmitting || !isDirty} />
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default EditMemberGroup;
