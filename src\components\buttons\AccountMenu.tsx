"use client";
import * as React from "react";
import Box from "@mui/material/Box";
import Avatar from "@mui/material/Avatar";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Tooltip from "@mui/material/Tooltip";
import { COLORS } from "@/styles/colors";
import { Button, Typography } from "@mui/material";
import ExpandIcon from "../icons/ExpandIcon";
import { signOut, useSession  } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IProfile } from "@/interface/IProfile";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import LanguageSwitcher from "../LanguageSwitcher";
import CustomButton from "@/components/buttons/CustomButton"

export default function AccountMenu() {
  const { data: session , status } = useSession();
  const t = useTranslations("navbar");
  const router = useRouter();
  const { data } = useQuery({
    // queryKey: ["profile"],
    // queryFn: () => xior.get<IProfile>("/api/profile").then((res) => res.data),
    queryKey: ["profile"],
    queryFn: () => session ? xior.get<IProfile>("/api/profile").then((res) => res.data) : null,
    enabled: !!session
  });
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const currentUrl = window.location.href;
  const url = new URL(currentUrl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const getCurrentLocale = () => {
    return window.location.pathname.startsWith('/zh') ? 'zh' : 'en';
  };
  const getLocalizedRoute = (route:any) => {
    const locale = getCurrentLocale();
    return locale === 'zh' ? `/zh${route}` : route;
  };

  const onClickResetPassword = () => {
    router.push(getLocalizedRoute(ROUTES.CHANGE_PASSWORD));
    handleClose();
  }

  const onClickHelpCenter = () => {
    handleClose();
  };

  const onClickLogout = async () => {
    // signOut();
    if(url.pathname.includes("account-settings")){
      await signOut({ callbackUrl: "/" });
    }else{
      signOut();
    }

    handleClose();
  };

  const renderLogin = () =>{
    router.push(`/login`)
  }
  // console.log("profile",data)
  const renderProfileUrl = () =>{
    //router.push(ROUTES.ACCOUNT_SETTINGS)
    window.location.href = (ROUTES.ACCOUNT_SETTINGS)
  }

  const renderMyTicketUrl = () =>{
    //router.push(`${ROUTES.ACCOUNT_SETTINGS}/#my_ticket`)
    window.location.href = (`${ROUTES.ACCOUNT_SETTINGS}/#my_ticket`)
  }

  const renderPurchaseRecord = () =>{
    window.location.href = (`${ROUTES.ACCOUNT_SETTINGS}/#purchase_record`)
  }

  const renderAvatar = () =>{
    if(data?.photoUrl){
      return(
        <>
          <Avatar
          sx={{
            width: 38,
            height: 38,
            background: COLORS.BLUE,
            fontSize: 16,
            fontWeight: 700,
          }}
          src={data?.photoUrl}
          >
          </Avatar>
        </>
      )
    }else{
      return(
        <>
      <Avatar
      src={"/images/DefaultProfilePic.png"}
      sx={{
        width: 38,
        height: 38,
        background: COLORS.BLUE,
        fontSize: 16,
        fontWeight: 700,
      }}
      >
      </Avatar>
        </>
      )
    }
  }

  return (
    <>
     {/* <LanguageSwitcher /> */}
      {/* {status === "authenticated" ? (  */}
        <React.Fragment>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              textAlign: "center",
            }}
          >
            <Tooltip title="Account settings">
              <Button
                onClick={handleClick}
                size="small"
                sx={{ ml: 2 }}
                aria-controls={open ? "account-menu" : undefined}
                aria-haspopup="true"
                aria-expanded={open ? "true" : undefined}
                startIcon={renderAvatar()}
                endIcon={
                  <ExpandIcon
                    sx={{
                      width: 15,
                      transform: open ? "rotate(180deg)" : undefined,
                    }}
                  />
                }
              >
                {/* <Typography
                  fontSize={15}
                  color={COLORS.BLACK}
                  textTransform={"none"}
                  sx={{
                    marginLeft: "2px",
                    marginRight: 0.5,
                  }}
                >
                  {data?.name || ""}
                </Typography> */}
              </Button>
            </Tooltip>
          </Box>
          <Menu
            anchorEl={anchorEl}
            id="account-menu"
            open={open}
            onClose={handleClose}
            onClick={handleClose}
            PaperProps={{
              elevation: 0,
              sx: {
                width: 200,
                overflow: "visible",
                wordWrap: "break-word",
                mt: 2,
                background: "rgba(255, 255, 255, 1)",
                borderRadius: "0 0 10px 10px",
                border:"rgba(244, 245, 246, 1)",
                right: 0,
                fontSize: 14,
                fontWeight: 400,
              },
            }}
            MenuListProps={{
              sx: {
                padding: 0,
                ["& .MuiMenuItem-root"]: {
                  minHeight: 36,
                  height: 36,
                  px: 2.75,
                  mb: 1.5,
                },
              },
            }}
            transformOrigin={{ horizontal: "left", vertical: "top" }}
            anchorOrigin={{ horizontal: "left", vertical: "bottom" }}
          >
            <MenuItem onClick={renderProfileUrl} sx={{ paddingLeft: 1.5 }}>
              {t("edit")}
            </MenuItem>
            {/* <MenuItem onClick={onClickResetPassword} sx={{ paddingLeft: 1.5 }}>
              {t("reset_password")}
            </MenuItem> */}
            <MenuItem onClick={renderMyTicketUrl} sx={{ paddingLeft: 1.5 }}>
              {t("my_ticket")}
            </MenuItem>
            <MenuItem onClick={renderPurchaseRecord} sx={{ paddingLeft: 1.5 }}>
              {t("purchase_record")}
            </MenuItem>
            {/* <MenuItem onClick={onClickHelpCenter} sx={{ paddingLeft: 1.5 }}>
              {t("help_center")}
            </MenuItem> */}
            <MenuItem onClick={onClickLogout} sx={{ paddingLeft: 1.5 }}>
              {t("logout")}
            </MenuItem>
          </Menu>
        </React.Fragment>
      {/* ) : (
        // 只有在狀態為 "loading" 時才顯示一個加載狀態
        status === "loading" ? null : (

          // <CustomButton label="修改密碼" icon={<LockIcon />} onClick={handleOpen}/>
          <> 
           <Button onClick={renderLogin}>
          {t("login")}
         </Button>
          </>
        )
      )} */}
    </>
  );
}
