import { handleApiError } from "@/utils/api";
import { NextRequest } from "next/server";
import xior from "xior";

async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    await xior.post(
      `/auth/verify-otp`,
      { ...data, otp: parseInt(data.otp) },
      {
        baseURL: process.env.NEXT_PUBLIC_API_BASE,
      }
    );
    return Response.json({ status: "success" }, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { POST };
