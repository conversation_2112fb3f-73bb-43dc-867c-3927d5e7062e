import { Box, Button, FormControl, InputAdornment, InputLabel, MenuItem, Select, SelectChangeEvent, Stack, styled, TextField, Typography } from "@mui/material";
import {PeronDetialsDto} from "@/interface/IPersonDetails"
import { ChangeEvent, useState } from "react";
import { IPaymentCreateDto } from '@/interface/IPaymentGateway';
import { ICheckOutDto } from "@/interface/ICheckOutDto";

type Props = {
    peronDetialsDto:PeronDetialsDto
    handleAddPersonDetialsChange:(peronDetialsDto:PeronDetialsDto)=>void
    paymentCreateDto: IPaymentCreateDto;
    handlePaymentDto:(paymentCreateDto:IPaymentCreateDto)=> void
    checkoutDto:ICheckOutDto
    handlesetActiveStep:() => void
}

const PersonDetails = ({peronDetialsDto,handleAddPersonDetialsChange,paymentCreateDto,handlePaymentDto,checkoutDto,handlesetActiveStep}:Props) =>{
  const [errors, setErrors] = useState<{ clientName?: string; clientContactNumber?: string }>({});
    const handleTextField = (event: ChangeEvent <HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent) =>{
        const { name, value } = event.target;

      if (name === "areaCode") {

        const areaCode = value;
        const phoneNumber = paymentCreateDto.clientContactNumber.split(" ")[1] || '';
        

        handlePaymentDto({
          ...paymentCreateDto,
          clientContactNumber: `${areaCode} ${phoneNumber}`,
        });
      } else if (name === "clientEmail") {

        handlePaymentDto({
            ...paymentCreateDto,
            clientEmail: checkoutDto.order.user.email,
        });}
        else {

        handlePaymentDto({
          ...paymentCreateDto,
          [name]: value,
        });
      }
        
    }
    const Item = styled('div')(({ theme }) => ({
      backgroundColor: '#fff',
      ...theme.typography.body2,
      padding: theme.spacing(1),
      textAlign: 'center',
      color: theme.palette.text.secondary,
      ...theme.applyStyles('dark', {
        backgroundColor: '#1A2027',
      }),
    }));

    const handleSubmit = () => {
      const newErrors: any = {};
      if (!paymentCreateDto.clientName) {
          newErrors.clientName = "Client Name is required.";
      }
      if (!paymentCreateDto.clientContactNumber) {
          newErrors.clientContactNumber = "Client Contact Number is required.";
      }
      setErrors(newErrors);

      // 如果沒有錯誤，可以進一步處理表單提交
      if (Object.keys(newErrors).length === 0) {
          // 這裡可以調用提交的處理函數
          handlesetActiveStep()
          console.log("Form submitted successfully");
      }
  };

    return(
        <>
            <Box sx={{ 
            width: '100%' ,
            mt:"40px"
            }}>
                <Typography variant="h3">Personal Details</Typography><br/>
                <Typography sx={{ textAlign: 'left' }}>Name</Typography>
                <TextField 
                id="outlined-basic" variant="outlined"  fullWidth
                name="clientName"
                value={paymentCreateDto.clientName || ''}
                onChange={handleTextField}
                error={!!errors.clientName}
                helperText={errors.clientName}
                /><br/><br/>
                <Typography sx={{ textAlign: 'left' }}>Phone Number</Typography>
                <FormControl fullWidth variant="outlined" sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                <TextField
                  id="outlined-basic"
                  variant="outlined"
                  name="clientContactNumber"
                  fullWidth
                  value={paymentCreateDto.clientContactNumber || ''}
                  onChange={handleTextField}
                  error={!!errors.clientContactNumber}
                  helperText={errors.clientContactNumber}
                  // InputProps={{
                  //   startAdornment: <InputAdornment position="start">Phone</InputAdornment>,
                  // }}
                />
              </FormControl><br/>
                
                <Typography sx={{ textAlign: 'left' }}>Email Address</Typography>
              <TextField id="outlined-basic" variant="outlined" fullWidth
               name="clientEmail" 
              value={checkoutDto.order.user.email}
              onChange={handleTextField}
              disabled
              />
               <Button variant="contained" onClick={handleSubmit} sx={{ mt: 4 }}>Next</Button>
          </Box>
        </>
    )
}

export default PersonDetails;