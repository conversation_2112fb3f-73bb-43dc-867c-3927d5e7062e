"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const ExportToExcelIcon = createSvgIcon(
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_6050_45990)">
      <path
        d="M19 19V19C19 20.1046 18.1046 21 17 21H7C5.89543 21 5 20.1046 5 19V3H14L19 8V10.5"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M13 3V9H19" stroke="white" strokeWidth="1.5" strokeLinejoin="round" />
      <path
        d="M16 17L18 15M18 15L16 13M18 15L9 15"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6050_45990">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>,
  "ExportToExcel"
);

export default ExportToExcelIcon;
