import {
    authorizedGet,
    getAuthHeaders,
    handleApiError,
  } from "@/utils/api";
  import { NextRequest,NextResponse } from "next/server";

  async function GET(req: NextRequest) {
    try{
    const region = req.nextUrl.searchParams.get('region');
    const category = req.nextUrl.searchParams.get('category');
    const sortOrder = req.nextUrl.searchParams.get('sortOrder');
    const page = req.nextUrl.searchParams.get('page');
    
      const data = await authorizedGet(
      `/featured-event?region=${region}&category=${category}&sortOrder=${sortOrder}&page=${page}`,
        {}
      );
        
        return Response.json(data, { status: 200 });
    }catch(error){

        return handleApiError(error);

    }
  }

  export { GET }