import * as React from 'react';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {OrderDetail} from "@/interface/IClientOrderDetails"
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import { Chip, Stack } from '@mui/material';
import Image from "next/image";
import { format } from 'date-fns';
import RecordOrderDetailsModal from './record-orderDetailsModal';

const bull = (
    <Box
      component="span"
      sx={{ display: 'inline-block', mx: '2px', transform: 'scale(0.8)' }}
    >
      •
    </Box>
  );

const Item = styled(Paper)(({ theme }) => ({
    backgroundColor: '#fff',
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: 'center',
    color: 'dark',
    boxShadow: 'none',
    ...theme.applyStyles('dark', {
      backgroundColor: '#1A2027',
    }),
  }));  

type Props ={
    orderDetails:OrderDetail
}  

const RecordRow = ({orderDetails}:Props) =>{

    const paidTime = orderDetails.deliveryDetails.paidTime;

    const paidTimeformattedDate = paidTime ?
    format(new Date(paidTime * 1000), "yyyy-MM-dd HH:mm:ss") : 
    "N/A";

    const renderDeliveryStatus = () =>{
        if(orderDetails.deliveryStatus?.includes("pending")){
            return(
                <>
                <Stack direction="row" spacing={1}>
                <Chip label="處理中"  
                    sx={{
                        backgroundColor: 'rgba(255, 240, 228, 1)', 
                        color: 'rgba(244, 128, 31, 1)', 
                        borderColor:"rgba(255, 240, 228, 1)"
                      }}
                />
                </Stack>
                </>
            )
        }else if(orderDetails.deliveryStatus?.includes("delivered")){
            return(
                <>
                <Stack direction="row" spacing={1}>
                <Chip label="已派發"  
                    sx={{
                        backgroundColor: 'rgba(244, 245, 246, 1)', 
                        color: 'rgba(119, 126, 144, 1)', 
                        borderColor:"rgba(244, 245, 246, 1)"
                      }}
                />
                </Stack>
                </>
            )
        }
    }

    const textStyle = {
        color:"rgba(119, 126, 144, 1)"
    }

    const boldBlackText = {
        color:"black",
        fontWeight: 'bold'
    }

    const renderOrderBalance = () =>{

        const totalPrice = Number(orderDetails?.totalPrice);
        const discount = Number(orderDetails?.promoCode.discount);
        
        if(orderDetails?.promoCode.code){
            const finalPrice = totalPrice - discount;
            return(
                <>
                {finalPrice.toFixed(2)}
                </>
            )
        }else{
            return(
                <>
                {totalPrice.toFixed(2)}
                </>
            )
        }
    }

    return(
        <>
            <Card sx={{ minWidth: 1000, mt:2 }}>
                <CardContent>
                    <Box sx={{ flexGrow: 1 }}>
                        <Grid container spacing={2}>
                            <Grid item xs={6} md={4}>
                            <Item>
                            <Stack spacing={2}>
                                <Item>
                                    <Box>
                                    <Typography variant="subtitle1">
                                        <span style={textStyle}>訂單號碼</span>: <span style={boldBlackText}>{orderDetails.orderNumber}</span>
                                    </Typography>
                                    </Box>
                                </Item>
                                <Item>
                                <Typography variant="subtitle1">
                                <span style={textStyle}>共{orderDetails.quantity}張門票</span>&nbsp;,&nbsp;<span style={textStyle}>訂單總額</span>: <span style={boldBlackText}>{orderDetails.currency}:{renderOrderBalance()}</span>
                                </Typography>
                                </Item>
                                <Item>
                                    <Image
                                        src={orderDetails.thumbnailUrl}
                                        alt="Product Image"
                                        width={100}
                                        height={100}
                                        layout="fixed"
                                    />
                                </Item>
                            </Stack>
                            </Item>
                            </Grid>
                            <Grid item xs={6} md={4}>
                            <Item>
                                <Stack spacing={2}>
                                    <Item>
                                    <Typography variant="subtitle1">
                                        <span style={textStyle}>下單時間</span> : <span style={boldBlackText}>{paidTimeformattedDate}</span>
                                    </Typography>

                                    </Item>
                                </Stack>              
                            </Item>
                            </Grid>
                            <Grid item xs={6} md={4}>
                            <Item>
                                <Box sx={{
                                    display:"flex",
                                    alignItems:"center"
                                }}>
                                {renderDeliveryStatus()}
                                <RecordOrderDetailsModal orderId={orderDetails.orderId}/>
                                </Box>
                            </Item>
                            </Grid>
                    </Grid>
                    </Box>
                </CardContent>
            </Card>
        </>
    )
}

export default RecordRow;