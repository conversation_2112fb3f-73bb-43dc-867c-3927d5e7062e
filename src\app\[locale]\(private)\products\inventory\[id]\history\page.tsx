"use client"
import PageHeader from "@/components/PageHeader";
import { Box, Table, TableBody, TableContainer, TableHead, Typography } from "@mui/material"
import { useTranslations } from "next-intl";
import { IInventory } from "@/interface/IInventory";
import { useEffect, useState } from "react";
import xior from "xior";
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import AdjustmentHistoryTable from "./AdjustmentHistoryTable";

type Props = {
    params: { id: string };
}

const History = ({ params }: Props) => {

    const [getInventoryDto, setGetInventoryDto] = useState<IInventory | undefined>(undefined);
    const t = useTranslations("inventory");

    const getInventoryDtoApi = async () => {

        try {
            const response = await xior.get(`/api/inventory/${params.id}`);
            setGetInventoryDto(response.data);
        } catch (error) {
            console.error('Error fetching products:', error);
        }
    };

    useEffect(() => {
        getInventoryDtoApi();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
                <PageHeader title={`${t("label_title")} > ${getInventoryDto?.name} > adjustment history`}>
                    <>

                    </>
                </PageHeader>
                <Box flex={1} padding="26px 34px">

        <TableContainer >
            <Typography variant="h6" >Current Inventory Level</Typography>
            <Table sx={{ minWidth: 650, mt:"10px",mb:"10px" }} aria-label="simple table">
                <TableHead>
                <TableRow>
                    <TableCell>Unavailable</TableCell>
                    <TableCell>Committed</TableCell>
                    <TableCell>Available</TableCell>
                </TableRow>
                </TableHead>
                <TableBody>
           
                    <TableRow>
                    <TableCell>{getInventoryDto?.unavailable ? getInventoryDto?.unavailable : "-" }</TableCell>
                    <TableCell>{getInventoryDto?.currentCommitted? getInventoryDto?.currentCommitted: "-"}</TableCell>
                    <TableCell>{getInventoryDto?.currentQuantity? getInventoryDto?.currentQuantity:"-"}</TableCell>
                    </TableRow>
                </TableBody>
            </Table>
            <Typography variant="h6" >Adjustment History</Typography>
            <AdjustmentHistoryTable params={params}/>
        </TableContainer>
        </Box>
        </Box>
        </>
    )
}

export default History