"use client";
import * as React from "react";
import { Box, Button, Modal, TextField, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { ICollection } from "@/interface/ICollection";
import { useRouter } from "next/navigation";
import { CURRENCY, LANGUAGE_CODE, ROUTES, SALES_THRESHOLD, SUPPORT_CURRENCY } from "@/utils/constants";
import EditButton from "@/components/buttons/EditButton";
import TextEditor from "@/components/RichEditor";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";
import { DatePicker, DateTimePicker } from "@mui/x-date-pickers";
import Select from "@/components/input/Select";
import SelectWithAdd from "@/components/input/SelectWithAdd";
import Image from "next/image";
import { extractMultiTranslationBody, extractTranslationBody, TicketTranslation } from "@/utils/translation";

interface Props {
    id?: number;
    name?: string;
    ticket_variation_option: Variation[];
    language: LANGUAGE_CODE
}

type Variation = {
    id: number;
    value: string;
    label?: string;
    extra?: { id: number };
    translation: {
      translation: {
          content: string,
          fields: TicketTranslation,
          language_code: LANGUAGE_CODE
      }
    }[]
}

const EditTicketVariation = ({ 
    id,
    name,
    ticket_variation_option,
    language
}: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const t = useTranslations("ticket");
  const tCommon = useTranslations("common");
  const [open, setOpen] = React.useState(false);
  const [ticketVariation, setTicketVariation] = React.useState<{
    id?: number,
    name?: string,
    ticketVariationOption?: Variation[],
  }>({});

  React.useEffect(() => {
    setTicketVariation({
        id,
        name,
        ticketVariationOption: ticket_variation_option
    })
  }, [
    id,
    name,
    ticket_variation_option,
  ])

  const mutation = useMutation({
    mutationFn: () => {
        let item = ticket_variation_option.map((option) => {
            const {
                id
            } = option

            const found = ticketVariation?.ticketVariationOption?.find((item) => item.extra && item.extra.id === id)
            if (!found) {
                return {
                    ...option,
                    deleteVariation: true
                }
            }
            return {
              ...option,
              value: found.label
            }
        })
        let itemToAdd = (ticketVariation?.ticketVariationOption ?? []).map(item => {
            if (item.extra) return;
            return {
                value: item.value
            }
        }).filter(item => item)
        const clone = { 
            name: ticketVariation.name,
            variations: [...item, ...itemToAdd],
            language
        }
        return xior.put(`/api/ticket/variation/${id}`, clone)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["event"]});
      setOpen(false);
    },
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (mutation.isPending) return;
    setOpen(false);
  }, [mutation.isPending]);

  const handleUpdate = () => {
    mutation.mutate()
  }

  const handleOnChange = (key: 'name'|'ticketVariationOption', value: any) => {
    setTicketVariation({
        ...ticketVariation,
        [key]: value
    })
  }

  const options = ticket_variation_option.map((option) => {
    const clone = option
    const found = extractTranslationBody(language, TicketTranslation.VALUE, option.translation)
    if (found) clone.value = found
    return clone
  })

  return (
    <Box>
      <Box onClick={handleOpen}>
        <Image
            src={'/images/icon-edit.svg'}
            width={48}
            height={48}
            alt={'edit'}
        />
      </Box>
      <Modal open={open} onClose={handleClose}>
        <ModalContainer sx={{ padding: '40px', gap: '32px' }}>
            <Typography variant="h3">{t("edit_variation")}</Typography>

            <TextField
                label={t("variation_label")}
                value={ticketVariation.name}
                onChange={(event) => handleOnChange("name", event.target.value)}
            />

            <SelectWithAdd
                data={options.flat().map((item) => ({ extra: { id: item.id }, value: item.id, label: item.value }))}
                label={t("values_with_remind")}
                defaultValue={options.flat().map((item) => ({ extra: { id: item.id }, value: item.id, label: item.value }))}
                placeholder={tCommon("input_placeholder")}
                customChange={(value) => handleOnChange("ticketVariationOption", value)}
            />

            <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginTop: "30px" }}>
                <CancelButton onAction={handleClose} />
                <UpdateButton onAction={handleUpdate} />
            </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default EditTicketVariation;
