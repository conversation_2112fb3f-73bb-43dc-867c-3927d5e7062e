"use client";
import React, { useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  PaginationState,
  SortingState,
  ColumnDef,
} from "@tanstack/react-table";
import { Box, IconButton, InputAdornment } from "@mui/material";
import { useTranslations } from "next-intl";
import PaginationTable from "@/components/PaginationTable";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import xior from "xior";
import { ITag } from "@/interface/ITag";
import DeleteTag from "./DeleteTag";
import EditTag from "./EditTag";
import TextField from "@/components/input/TextField";
import debounce from "lodash.debounce";
import ClearIcon from "@mui/icons-material/Clear";
import PageSelector from "@/components/PageSelector";
import { IListResponse } from "@/interface/IListResponse";

const TagsTable = () => {
  const t = useTranslations("");
  const searchRef = React.useRef<any>();
  const [keyword, setKeyword] = React.useState("");
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const handleKeywordChange = debounce((value) => {
    setKeyword(value);
    setPagination((state) => ({ ...state, pageIndex: 0 }));
  }, 1000);

  const {
    isLoading,
    data
  } = useQuery({
    queryKey: ["tags", { pagination, sorting, keyword }],
    queryFn: async () =>
      xior
        .get<IListResponse<ITag>>("/api/tags", {
          params: {
            page: pagination.pageIndex + 1,
            size: pagination.pageSize,
            keyword,
          },
        })
        .then((res) => res.data),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  const columns = useMemo<ColumnDef<ITag>[]>(
    () => [
      {
        accessorKey: "id",
        header: "",
        cell: (data) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"center"}
            mx={1}
            key={`tag-${data.row.original.id}-actions`}
          >
            <EditTag tag={data.row.original} />
            <Box />
            <DeleteTag tag={data.row.original} />
          </Box>
        ),
      },
      {
        accessorKey: "name",
        header: t("tags.label_tag_name"),
      },
    ],
    [t]
  );

  const defaultData = React.useMemo<ITag[]>(() => [], []);

  const table = useReactTable({
    data: data?.items ?? defaultData,
    columns,
    rowCount: data?.count,
    state: { pagination, sorting },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
  });

  return (
    <Box display={"flex"} flexDirection={"column"} height={"100%"}>
      <Box display={"flex"} flexDirection={"row"} mb={3}>
        <Box flex={1}>
          <TextField
            inputRef={searchRef}
            sx={{ maxWidth: 450, mb: -1.5 }}
            placeholder="Search"
            onChange={(event) => {
              handleKeywordChange(event.target.value);
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => {
                      if (searchRef.current) {
                        searchRef.current.value = "";
                        setKeyword("");
                      }
                    }}
                    edge="end"
                  >
                    <ClearIcon htmlColor="#000" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>
        <PageSelector
          value={pagination.pageSize}
          onChange={(newPageSize) => {
            table.setPageSize(newPageSize);
          }}
        />
      </Box>
      <Box flex={1}>
        <PaginationTable table={table} isLoading={isLoading} msg={t('tags.title_empty')}/>
      </Box>
    </Box>
  );
};

export default TagsTable;
