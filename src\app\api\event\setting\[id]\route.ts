import { IOwner } from "@/interface/IOwner";
import {
  authorizedDelete,
  authorizedGet,
  authorizedPut,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { SOCIAL_MEDIAS } from "@/utils/constants";
import { NextRequest } from "next/server";

async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const body = await req.json();
    const data = await authorizedPut<any>(
        `/api/admin/v1/event/setting/${params.id}`,
        //   await getAuthHeaders(req)
        {},
        body
    );
    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { PUT };
