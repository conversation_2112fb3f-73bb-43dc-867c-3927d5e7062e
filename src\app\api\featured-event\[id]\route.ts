import {
    authorizedGet,
    handleApiError,
} from "@/utils/api";
import { NextRequest,NextResponse } from "next/server";

async function GET(req: NextRequest, { params }: { params: { id: string } }) {
    try{

        const data = await authorizedGet(
            `/featured-event/${params.id}`,
            {}
        )
        
        return Response.json(data, { status: 200 });
    }catch(error){

        return handleApiError(error);

    }
}

export { GET }