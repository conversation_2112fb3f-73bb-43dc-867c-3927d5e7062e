'use client'
import * as React from "react";
import { Box, Typography, Link } from "@mui/material";
import { COLORS } from "@/styles/colors";
import Stack from '@mui/material/Stack';
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import FacebookOutlinedIcon from '@mui/icons-material/FacebookOutlined';
import InstagramIcon from '@mui/icons-material/Instagram';
import YouTubeIcon from '@mui/icons-material/YouTube';
import { useTranslations } from "next-intl";

export default function Footer() {
  const t = useTranslations("menu");
  const ft = useTranslations("footer");
  const textStyles = {
    textDecoration: "none",
    fontSize: { xs: '0.875rem', sm: '1rem' } 
  }

  return (
    <Box
      sx={{
        backgroundColor: "rgba(244, 245, 246, 1)",
        padding: { xs: 2, md: 4 }, 
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' }, 
          gap: { xs: 4, md: 0 },
          paddingBottom: 4,
          borderBottom: `1px solid ${COLORS.BLACK}`,
        }}
      >
        <Stack spacing={2} sx={{ width: { xs: '100%', md: '25%' } }}>
          <Link href="#" color="inherit" sx={textStyles}>{t("featured_events")}</Link>
          <Link href="#" color="inherit" sx={textStyles}>{t("virtual_room")}</Link>
          <Link href="#" color="inherit" sx={textStyles}>{t("about_incutix")}</Link>
          <Link href="#" color="inherit" sx={textStyles}>{t("faq")}</Link>
        </Stack>

        <Stack spacing={2} sx={{ width: { xs: '100%', md: '25%' } }}>
        </Stack>

        <Stack spacing={2} sx={{ width: { xs: '100%', md: '25%' } }}>
          <Link href="#" color="inherit" sx={textStyles}>{ft('contact_us')}</Link>
          <Box sx={{ display: "flex", alignItems: 'center', gap: 1 }}>
            <MailOutlineIcon fontSize="small"/>
            <Link href="#" color="inherit" sx={{ ...textStyles, whiteSpace: 'nowrap' }}>
              <EMAIL><br/>({ft('business_negotiations')})
            </Link>
          </Box>
          <Box sx={{ display: "flex", alignItems: 'center', gap: 1 }}>
            <WhatsAppIcon fontSize="small"/>
            <Link href="#" color="inherit" sx={textStyles}>
              +852 9319 0184 <br/>({ft('only_text_messages_are_supported')})
            </Link>
          </Box>
        </Stack>

        <Stack spacing={2} sx={{ 
          width: { xs: '100%', md: '25%' },
          mt: { xs: 2, md: 0 } 
        }}>
          <Link href="#" color="inherit" sx={textStyles}>{ft('follow_us')}</Link>
          <Box sx={{ 
            display: 'flex',
            gap: 2,
            justifyContent: { xs: 'flex-start', md: 'flex-start' }
          }}>
            <FacebookOutlinedIcon fontSize="medium"/>
            <InstagramIcon fontSize="medium"/>
            <YouTubeIcon fontSize="medium"/>
          </Box>
        </Stack>
      </Box>

      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: { xs: 3, md: 0 },
          paddingTop: 4,
        }}
      >
        <Box sx={{ 
          width: { xs: '100%', md: '50%' },
          order: { xs: 2, md: 1 } 
        }}>
          <Typography variant="body2" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
          {ft('terms_of_service')} | {ft('privacy_policy')}
          </Typography>
          <Typography variant="body2" sx={{
            mt: 2,
            color: "rgba(119, 126, 144, 1)",
            fontSize: { xs: '0.75rem', sm: '0.875rem' }
          }}>
            © 2025 INCUTIX. All rights reserved.
          </Typography>
        </Box>

        <Box sx={{ 
          width: { xs: '100%', md: '50%' },
          order: { xs: 1, md: 2 },
          textAlign: { xs: 'left', md: 'right' }
        }}>
          <Typography variant="body2" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
            Powered by EasyLive Show Limited
          </Typography>
          <Typography variant="body2" sx={{
            mt: 2,
            color: "rgba(119, 126, 144, 1)",
            fontSize: { xs: '0.75rem', sm: '0.875rem' },
            lineHeight: 1.4
          }}>
            Address:B6, 25/F.,TML Tower,3 Hoi Shing Road, Tsuen Wan, Hong Kong
          </Typography>
        </Box>
      </Box>
    </Box>
  );
}