"use client";
import * as React from "react";
import { useTranslations } from "next-intl";
import { Typography, Button, Modal, Box, IconButton, Select, MenuItem, TextFieldProps } from "@mui/material";
import { styled } from "@mui/material/styles";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import "react-international-phone/style.css";
import { ACCEPTED_IMAGE_TYPES } from "@/utils/constants";
import AddNewButton from "@/components/buttons/AddNewButton";
import InputContainer, { InputContainerProps } from "@/components/input/InputContainer";
import ModalContainer from "@/components/ModalContainer";
import { <PERSON>, SubmitHand<PERSON>, useForm } from "react-hook-form";
import TextField from "@/components/input/TextField";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import FileInput from "@/components/input/FileInput";
import CloseRoundedIcon from "@mui/icons-material/CloseRounded";
import Image from "next/image";

interface Props extends InputContainerProps {
    fullWidth?: boolean;
    defaultCountry?: string;
    value?: any;
    // onChange?: (file: File | undefined) => void | any;
    onChange?: any;
}

const headers = [{
    label: "label_picture"
}, {
    label: "label_filename",
}, {
    label: "label_sorting"
}, {
    label: false
}]

type Column = {
    image?: Object,
    name?: string,
    file?: File | any,
    sorting?: number,
    action?: any
}

interface FormValue {
    name?: string,
    productImg?: File | any
}

export default function ProductImageTable({
    label,
    description,
    required,
    defaultCountry = "hk",
    value,
    onChange,
    width,
    fullWidth,
    error,
    ...otherProps
}: Props & Omit<TextFieldProps, "error">) {
    const [rows, setRows] = React.useState<Array<Column>>([])
    // const [rows, setRows] = React.useState<any[]>([]);
    const t = useTranslations()
    const [open, setOpen] = React.useState(false);

    const {
        handleSubmit,
        control,
        formState: { isSubmitting, errors },
        setError,
        reset,
    } = useForm({
        defaultValues: {
            name: "",
            productImg: "",
            thumbnailUrl: ""
        },
    });

    const handleOpen = () => setOpen(true);

    const handleClose = React.useCallback(() => {
        if (isSubmitting) return;
        setOpen(false);
    }, [isSubmitting]);

    React.useEffect(() => {
        setRows(value?.map((item: any) => {
            return {
                ...item,
                // image: (<img src={item.url} width={80} height="auto" />),
                image: (<Image
                    src={item.url}
                    alt="Product Image"
                    width={80}
                    height={80}
                    layout="fixed"
                />),
            }
        }))
        /* eslint-disable react-hooks/exhaustive-deps */
    }, [])

    React.useEffect(() => {
        onChange({
            target: {
                value: rows
            }
        })
        /* eslint-disable react-hooks/exhaustive-deps */
    }, [rows])

    const onSubmit: SubmitHandler<FormValue> = async (data) => {
        try {
            const productObject = [
                ...rows,
                {
                    name: data?.name,
                    // image: (<img src={URL.createObjectURL(data.productImg)} width={80} height="auto" />),
                    image: (<Image
                        src={URL.createObjectURL(data?.productImg)}
                        alt="Product Image"
                        width={80}
                        height={80}
                        layout="fixed"
                    />),
                    file: data.productImg,
                    sorting: rows.length + 1
                }
            ]
            setRows(productObject)
            reset();
            setOpen(false);
        } catch (e) {
            setError("name", { type: "custom", message: "duplicated_member_group" });
        }
    };

    const removeItem = (idx: any) => {
        setRows(rows.filter((item, index) => idx !== index))
    }

    return (
        <InputContainer>
            <Typography fontSize={14}>
                {t("product.label_product_asset")}
            </Typography>
            <Typography fontSize={12} color={'grey'}>
                <i>
                    {
                        t("file_upload.image_video_metadata").split("\n").map((item: string) => (
                            <>{item}<br /></>
                        ))
                    }
                </i>
            </Typography>
            <TableContainer component={Paper}>
                <Table sx={{ width: 450 }} aria-label="product table">
                    <TableHead>
                        <TableRow>
                            {
                                headers.map((header) => (
                                    // <TableCell key={header.id}>{header?.label && t(`product.${header.label}`)}</TableCell>
                                    <TableCell key={header?.label.toString()}>{header?.label && t(`product.${header.label}`)}</TableCell>
                                ))
                            }
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {rows.map((row, idx) => (
                            <TableRow
                                key={row.name}
                                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                            >
                                <TableCell component="th" scope="row">
                                    {/* {row.image} */}
                                    {JSON.stringify(row?.image)}
                                </TableCell>
                                <TableCell align="right">{row.name}</TableCell>
                                <TableCell align="right">
                                    <Select
                                        value={row.sorting}
                                        onChange={(event) => {
                                            let clone = [...rows]
                                            const index = Number(event.target.value) - 1
                                            const element = clone.splice(idx, 1)[0]; // Remove the element from the original position
                                            clone.splice(index, 0, element); // Insert the element at the new position
                                            setRows(clone.map((item, newIdx) => ({
                                                ...item,
                                                sorting: newIdx + 1
                                            })))
                                        }}
                                    >
                                        {
                                            Array.from(Array(rows.length), (_, i) => i + 1).map((order) => (
                                                <MenuItem key={order} value={order}>{order}</MenuItem>
                                            ))
                                        }
                                    </Select>
                                </TableCell>
                                <TableCell align="right">
                                    <IconButton
                                        aria-label="delete"
                                        size="small"
                                        sx={{
                                            background: "#ff3b36",
                                            height: 18,
                                            width: 18
                                        }}
                                        onClick={() => removeItem(idx)}
                                    >
                                        <CloseRoundedIcon sx={{ fill: "white", height: 16, width: 16 }} />
                                    </IconButton>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
            <br />
            <AddNewButton onClick={handleOpen} />
            <Modal open={open} onClose={handleClose}>
                <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        borderBottom={"1px solid #777777"}
                        alignSelf={"flex-start"}
                        paddingBottom={1}
                        px={1}
                        marginBottom={2}
                        marginLeft={-1}
                    >
                        <Typography fontSize={15} fontWeight={700}>
                            {t("product.add_product_image")}
                        </Typography>
                    </Box>
                    <Controller
                        name="name"
                        control={control}
                        render={({ field }) => (
                            <TextField
                                required
                                value={field.value}
                                onChange={field.onChange}
                                disabled={isSubmitting}
                                label={t("product.label_name")}
                                error={errors?.name?.message}
                            />
                        )}
                    />
                    <Controller
                        name="productImg"
                        control={control}
                        render={({ field }) => (
                            <>
                                <Typography fontSize={14}>
                                    {t("product.label_product_asset")}
                                </Typography>
                                <Typography fontSize={12} color={'grey'}>
                                    <i>
                                        {
                                            t("file_upload.image_video_metadata").split("\n").map((item: string) => (
                                                <>{item}<br /></>
                                            ))
                                        }
                                    </i>
                                </Typography>
                                <FileInput
                                    value={field.value}
                                    onChange={field.onChange}
                                    metadata={t("file_upload.thumbnail_multimedia_upload")}
                                    disabled={isSubmitting}
                                    error={errors.thumbnailUrl?.message}
                                    type="image_video"
                                />
                            </>
                        )}
                    />
                    <br />
                    <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
                        <CancelButton
                            disabled={isSubmitting}
                            onAction={handleClose}
                        />
                        <SaveButton disabled={isSubmitting} />
                    </Box>
                </ModalContainer>
            </Modal>
        </InputContainer>
    );
}
