import {
    Box, Checkbox,
    FormControl,
    FormControlLabel,
    Radio,
    RadioGroup,
    TextField,
    Typography
  } from "@mui/material";
  import { useTranslations } from "next-intl";
  import React, { useState } from "react";
  import { ROUTES, SUPPORT_CURRENCY, SUPPORT_WEIGTH, UNITY_ENDPOINT } from "@/utils/constants";
  import Select from "@/components/input/Select";
  import SelectMemberGroup from "@/components/input/SelectMemberGroup";
  import {IMemberGroup} from "@/interface/IMemberGroup";
  import {ProductDetailDto} from '@/interface/IProductDetailDto'

  type Props = {
    updateProductDto: ProductDetailDto
    handleUpdateProductDtoChange:(updateProductDto:ProductDetailDto)=> void
}
  
  const EditSelling = ({updateProductDto,handleUpdateProductDtoChange}:Props) =>{
  
      const [inventory, setInventory] = useState<number>(updateProductDto.quantity); 
      const t = useTranslations();
      const [currency, setCurrency] = useState<string>(updateProductDto.currency);
      const [price, setPrice] = useState<number>(updateProductDto.price);
      const [compareAtPrice, setCompareAtPrice] = useState<string>(updateProductDto.compareAt);
      const [weight,setWeight]= useState<number>(updateProductDto.weight);
      const [supportWeight,setSupportWeight]= useState<string>(updateProductDto.weightUnit);
      const [includeTax, setIncludeTax] = useState<boolean>(updateProductDto.taxIncluded);
      const [tax,setTax] = useState<number>(updateProductDto.tax);
      const [isPublic, setIsPublic] = useState<boolean>(updateProductDto.openToAllMembers);
      const [memberGroup, setMemberGroup] = useState<IMemberGroup[]>(updateProductDto.memberGroup);
      const [status,setStatus] = useState<number>(updateProductDto.status);
      const [type,setType] = useState<number>(updateProductDto.type)
  
      const handleMemberGroupChange = (memberGroup: IMemberGroup[]) => {
        setMemberGroup(memberGroup);
        handleUpdateProductDtoChange({
          ...updateProductDto,
          memberGroup: memberGroup
      })
      };

      console.log("status",status)
      const handleStatusChange = (event: any) =>{
        const newValue = event.target.value
        setStatus(newValue);
        handleUpdateProductDtoChange({
          ...updateProductDto,
          status: newValue,
      })
      }
      console.log("type",type)
      const handleTypeChange = (event: React.ChangeEvent<HTMLInputElement>) =>{
        setType(type)
      }
  
      console.log(memberGroup)
      const handleIsPublicChange = (event: React.ChangeEvent<HTMLInputElement>) =>{
        const newValue = event.target.checked
        setIsPublic(newValue)
        handleUpdateProductDtoChange({
          ...updateProductDto,
          openToAllMembers: newValue
      })
      }
  
      const handleTaxChange = (event: any) =>{
        const newValue = event.target.value
          setTax(parseFloat(newValue))
          handleUpdateProductDtoChange({
            ...updateProductDto,
            tax: newValue
        })
        }
  
      const handleIncludeTaxChange = (event: React.ChangeEvent<HTMLInputElement>) =>{
        const newValue = event.target.checked
        setIncludeTax(newValue)
        handleUpdateProductDtoChange({
          ...updateProductDto,
          taxIncluded: newValue
      })
      }
  
      const handleSupportWeightChange = (event:any) =>{
        const newValue = event.target.value
        setSupportWeight(newValue)
        handleUpdateProductDtoChange({
          ...updateProductDto,
          weightUnit: newValue
      })
      }
  
      const handleWeightChange = (event:any) =>{
        const newValue = event.target.value
        setWeight(newValue)
        handleUpdateProductDtoChange({
          ...updateProductDto,
          weight: newValue
      })
      }
      const handleCurrencyChange = (event:any) =>{
        const newValue = event.target.value
        setCurrency(newValue)
        handleUpdateProductDtoChange({
          ...updateProductDto,
          currency: newValue
      })
      }
  
      const handlePriceChange = (event:any) =>{
        const newValue = event.target.value
        setPrice(newValue)
        handleUpdateProductDtoChange({
          ...updateProductDto,
          price: newValue
      })
      }
  
      const handleCompareAtPrice = (event:any) =>{
        const newValue = event.target.value
        setCompareAtPrice(newValue)
        handleUpdateProductDtoChange({
          ...updateProductDto,
          compareAt: newValue
      })
      }

      const handleInventoryChange = (event: any) => {
          const newValue = event.target.value
          setInventory(newValue);
          handleUpdateProductDtoChange({
            ...updateProductDto,
            quantity: newValue
        })
        }
      return(
          <>
              <Typography variant="subtitle1" component="h2">
              {t("product.label_status")}
              </Typography>
              <FormControl>
                  <RadioGroup
                      aria-labelledby="demo-radio-buttons-group-label"
                      value={status}
                      name="status"
                      onChange={handleStatusChange}
                      sx={{ display: "block" }}
                  >
                      <FormControlLabel value={1} control={<Radio />} label={t('product.label_draft')} />
                      <FormControlLabel value={2} control={<Radio />} label={t('product.label_listed')} />
                      <FormControlLabel value={3} control={<Radio />} label={t('product.label_unlisted')} />
                  </RadioGroup>
              </FormControl>
              <Typography variant="subtitle1" component="h2">
              {t("product.label_selling_type")}
              </Typography>
              <Typography fontSize={12} color={"grey"}>
                          {t("product.selling_type_description")}
              </Typography>
              <FormControl>
                  <RadioGroup
                      aria-labelledby="demo-radio-buttons-group-label"
                      value={type}
                      onChange={handleTypeChange}
                      name="status"
                      sx={{ display: "block" }}
                  >
                      <FormControlLabel value={1} control={<Radio />} label={t('product.label_retail')} />
                  </RadioGroup>
              </FormControl>
              <Typography variant="subtitle1" component="h2">
              {t("product.label_inventory")}
              </Typography>
              <TextField
                  value={inventory} 
                  onChange={handleInventoryChange} 
                  name="quantity"
                  // placeholder={"0"}
              />
  
            <Typography variant="subtitle1" component="h2">
              {t("product.label_price")}
            </Typography>
  
            <Box display={'flex'} flexDirection={"row"} gap={1}>
              <Select
                sx={{ width: 100 }}
                data={SUPPORT_CURRENCY}
                value={currency}
                onChange={handleCurrencyChange}
              />
              <TextField
                value={price}
                onChange={handlePriceChange}
                required
                placeholder={"0.00"}
              />
             </Box>
            <Typography fontSize={14}>
              {t("product.label_compare_at_price")}
            </Typography>
            <Typography fontSize={12} color={"grey"}>
              {t("product.compare_at_price_description")}
            </Typography>
            <TextField
              value={compareAtPrice}
              onChange={handleCompareAtPrice}
              placeholder={"0.00"}
            />
            <Typography fontSize={14}>
              {t("product.label_weight")}
            </Typography>
            <Typography fontSize={12} color={"grey"}>
              {t("product.weight_description")}
            </Typography>
            <Box display={'flex'} flexDirection={"row"} gap={1}>
              <TextField
                value={weight}
                onChange={handleWeightChange}
                placeholder={"0.0"}
              />
              <Select
                sx={{ width: 80 }}
                data={SUPPORT_WEIGTH}
                value={supportWeight}
                onChange={handleSupportWeightChange}
              />
            </Box>
            <FormControlLabel
              control={
                <Checkbox
                  checked={includeTax}
                  onChange={handleIncludeTaxChange}
                />
              }
              label={t("product.label_charge_tax")}
            />
            {
              includeTax && (
                <>
                <Typography>{t("product.label_tax_rate")}</Typography>
                <TextField
                  value={tax}
                  onChange={handleTaxChange}
                />
                </>
              )
            }<br/>
            <FormControlLabel
              control={
                <Checkbox
                  checked={isPublic}
                  onChange={handleIsPublicChange}
                />
              }
              label={t("product.label_open_to_public")}
            />
            {
              !isPublic && (
                <>
                  <Typography fontSize={14}>
                    {t("product.label_room_exclusive_to")}
                  </Typography>
                  <SelectMemberGroup
                    value={memberGroup}
                    onChange={handleMemberGroupChange}
                  />
                </>
              )
            }
          </>
      )
  }
  
  export default EditSelling;