"use client";
import AddNewButton from "@/components/buttons/AddNewButton";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import SelectParentCategory from "@/components/input/SelectParentCategory";
import TextField from "@/components/input/TextField";
import ModalContainer from "@/components/ModalContainer";
import { ProductCategorySchema } from "@/schemas/ProductCategorySchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Modal, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import * as React from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";
import CloseIcon from '@mui/icons-material/Close';

type FormValue = z.infer<typeof ProductCategorySchema>;

const CreateProductCategory = () => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const [category, setCategory] = React.useState('');
  const [categories, setCategories] = React.useState<any[]>([]);

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
    reset,
  } = useForm<FormValue>();

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (isSubmitting) return;
    setOpen(false);
  }, [isSubmitting]);


  const handleOnChange = (event: any) => {
    const {
      value
    } = event.target
    setCategory(value)

    if (value[value.length - 1] === ',') {
      setCategories([... new Set([
        ...categories,
        value.slice(0, -1)
      ])])
      setCategory('')
    }
  }

  const onSubmit: SubmitHandler<FormValue> = async (data: any) => {
    try {
      let uploadCategories = categories
      if (uploadCategories.length === 0) {
        if (!data || !data.name) {
          setError("name", { type: "custom", message: "at least enter one tag" })
          return;
        }
        uploadCategories = [data.name]
      }
      const parentId = data.parent?.id ?? null
      uploadCategories = uploadCategories.map((category) => {
        if (parentId) return { name: category, parentId }
        return { name: category }
      })
      const res = await xior.post("/api/product-category", uploadCategories);
      queryClient.invalidateQueries({ queryKey: ["productCategory"] });
      const {
        added = []
      } = res.data

      if (added.length !== uploadCategories.length) {
        const addedCategories = added.map((addedItem: { name: any; }) => addedItem.name)
        setCategories(categories.filter(category => !addedCategories.includes(category)))
        setError("name", {
          type: "custom",
          message: "duplicated_product_category",
        });
      } else {
        reset();
        setOpen(false);
        setCategory('')
        setCategories([])
      }
    } catch (e) {
      setError("name", {
        type: "custom",
        message: "duplicated_product_category",
      });
    }
  };

  return (
    <Box>
      <AddNewButton onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("product_category.title_create_product_category")}
            </Typography>
          </Box>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                value={category}
                onChange={(event) => {
                  handleOnChange(event)
                  field.onChange(event)
                }}
                disabled={isSubmitting}
                label={t("product_category.label_new_product_category_name")}
                placeholder={t("product_category.label_split")}
                error={errors?.name?.message}
              />
            )}
          />
          <Controller
            name="parent"
            control={control}
            render={({ field: { onChange, value } }) => (
              <SelectParentCategory
                value={value}
                onChange={onChange}
                label={t("product_category.label_parent_category")}
                helperText={t(
                  "product_category.label_parent_category_helper_text"
                )}
                placeholder="Start typing name to search"
                disabled={isSubmitting}
                error={errors?.parent?.message}
              />
            )}
          />
          <div>
            {
              categories.map((category) => 
                <span
                  style={{
                    width: 'auto',
                    padding: 8,
                    border: '1px solid black',
                    borderRadius: 20
                  }}
                  key={`category_${category}`}
                >
                  {category}
                  <CloseIcon fontSize="small" onClick={() => {
                    setCategories([
                      ...categories.filter((categoryItem) => categoryItem !== category)
                    ])
                  }}/>
                </span>
              )
            }
          </div>
          <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
            <CancelButton disabled={isSubmitting} onAction={handleClose} />
            <SaveButton disabled={isSubmitting} />
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default CreateProductCategory;
