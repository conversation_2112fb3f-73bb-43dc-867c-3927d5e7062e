"use client";
import * as React from "react";
import { Box, InputAdornment, Modal, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { useRouter } from "next/navigation";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";
import AddNewButton from "@/components/buttons/AddNewButton";
import MyButton from '@/components/buttons/CustomButton';
import { EVENT_STATUS } from "./eventTab";
import Select from "@/components/input/Select";
import { DatePicker, DateTimePicker, TimePicker } from "@mui/x-date-pickers";
import TextField from "@/components/input/TextField";
import dayjs from "dayjs";
import EditButton from "@/components/buttons/EditButton";
import { LANGUAGE_CODE } from "@/utils/constants";

type Props = {
    id: number,
    info: EventBase,
    language: LANGUAGE_CODE
}

export enum RegionCode {
    MY = 'MY',
    HK = 'HK'
}

export enum Timezone { 
    MST = 'MST',
    HKT = 'HKT'
}

export enum TimeUnit {
    MINUTES = 'M',
    HOURS = 'H'
}

export type EventBase = {
    name: string,
    description: string,
    regionCode: RegionCode,
    timezone: Timezone,
    startDate: number,
    endDate: number,
    startTime?: number,
    endTime?: number,
    duration?: number,
    timeUnit?: TimeUnit,
    venue: string,
    parentId?: number
}

export const regionOption = [{
    label: 'Hong Kong',
    value: RegionCode.HK
}, {
    label: 'Malaysia',
    value: RegionCode.MY
}]

export const timezoneOption = [{
    label: 'Malaysia Time',
    value: 'MST'
}, {
    label: 'Hong Kong Time',
    value: 'HKT'
}]

export const timeUnitOption = [{
    label: 'Minutes',
    value: TimeUnit.MINUTES
}, {
    label: 'Hours',
    value: TimeUnit.HOURS
}]

type Fields = 'name'|'regionCode'|'timezone'|'startDate'|'endDate'|'duration'|'venue'|'timeUnit'

const EditEvent = ({id, info, language}: Props) => {
    const queryClient = useQueryClient();
    const router = useRouter();
    const t = useTranslations("event");
    const tCommon = useTranslations("common");
    const [open, setOpen] = React.useState(false);
    const [introduction, setIntroduction] = React.useState('');
    const [event, setEvent] = React.useState<Partial<EventBase>>(info);
    
    React.useEffect(() => {
        setEvent(info)
    }, [open, info])

    const mutation = useMutation({
        mutationFn: () => {
            const {
                startDate,
                startTime,
                endDate,
                endTime,
                timeUnit = TimeUnit.MINUTES,
                duration
            } = event
            let parsedStartTime = 0, parsedEndTime = 0, parsedDuration = 0
            if (startTime) {
                parsedStartTime += (dayjs(startTime).get('h')) * 60 * 60
                parsedStartTime += (dayjs(startTime).get('m')) * 60
            }
            if (endTime) {
                parsedEndTime += (dayjs(endTime).get('h')) * 60 * 60
                parsedEndTime += (dayjs(endTime).get('m')) * 60
            }
            if (duration) {
                switch(timeUnit) {
                    case TimeUnit.HOURS:
                        parsedDuration = duration * 60 * 60
                        break;
                    default:
                        parsedDuration = duration * 60
                        break;
                }
            }
            const body = {
                ...event,
                startDate: startDate? dayjs(startDate).startOf('day').unix(): undefined,
                endDate: endDate? dayjs(endDate).startOf('day').unix(): undefined,
                startTime: parsedStartTime > 0? parsedStartTime : undefined,
                endTime: parsedEndTime > 0? parsedEndTime : undefined,
                duration: parsedDuration > 0? parsedDuration : undefined
            }
            return xior.put(`/api/event/${id}`, body)
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["event"]});
            setOpen(false);
        },
    });

    const handleOpen = () => setOpen(true);

    const handleClose = React.useCallback(() => {
        if (mutation.isPending) return;
        setOpen(false);
    }, [mutation.isPending]);

    const handleUpdate = () => {
        mutation.mutate()
    }

    const handleDateChange = (target: 'startDate'|'endDate'|'startTime'|'endTime', date: Date | null) => {
        if (!date) return;
        setEvent({
            ...event,
            [target]: date.valueOf()
        })
    }

    const handleChange = (target: Fields, value: string|number) => {
        setEvent({
            ...event,
            [target]: value
        })
    }

    return (
        <Box>
            <EditButton onClick={handleOpen} isSmall={true} isPrimay={false} />
            <Modal open={open} onClose={handleClose}>
                <ModalContainer sx={{ padding: '40px', gap: '32px' }}>
                    <Typography variant="h3">{t("create_button")}</Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                        <Typography sx={{ fontSize: '18px', fontWeight: '700', lineHeight: '130%' }}>
                            {t("input_event_detail")}
                        </Typography>
                        <TextField
                            disabled={mutation.isPending}
                            label={t("event_name")}
                            value={event.name}
                            onChange={(event) => handleChange('name', event.target.value)}
                            placeholder={t("event_name")}
                        />

                        <Select 
                            placeholder={t("region")}
                            data={regionOption}
                            value={event.regionCode}
                            handleOnChange={(val) => handleChange('regionCode', val)}
                        />

                        <Select 
                            placeholder={t("timezone")}
                            data={timezoneOption}
                            value={event.timezone}
                            handleOnChange={(val) => handleChange('timezone', val)}
                        />

                        <Box
                            sx={{
                                display: 'flex',
                                gap: '16px'
                            }}
                        >
                            <DatePicker
                                sx={{ flex: 1 }}
                                label={t("event_start")}
                                value={
                                    event.startDate ?
                                    new Date(event.startDate)
                                    : null
                                }
                                onChange={(date) => handleDateChange("startDate", date)} 
                            />

                            <DatePicker
                                sx={{ flex: 1 }}
                                label={t("event_end")}
                                value={
                                    event.endDate ?
                                    new Date(event.endDate)
                                    : null
                                }
                                onChange={(date) => handleDateChange("endDate", date)} 
                            />
                        </Box>

                        <Box
                            sx={{
                                display: 'flex',
                                gap: '16px'
                            }}
                        >
                            <TimePicker
                                sx={{ flex: 1 }}
                                label={t("event_time_start")}
                                value={
                                    event.startTime ?
                                    new Date(event.startTime)
                                    : null
                                }
                                onChange={(date) => handleDateChange("startTime", date)} 
                            />

                            <TimePicker
                                sx={{ flex: 1 }}
                                label={t("event_time_end")}
                                value={
                                    event.endTime ?
                                    new Date(event.endTime)
                                    : null
                                }
                                onChange={(date) => handleDateChange("endTime", date)} 
                            />
                        </Box>

                        <Box
                            sx={{
                                display: 'flex',
                                gap: '16px'
                            }}
                        >
                            <TextField
                                sx={{ flex: 1, paddingRight: '0px' }}
                                label={t("about_time")}
                                value={event.duration}
                                onChange={(event) => handleChange('duration', event.target.value)}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <Select 
                                                data={timeUnitOption}
                                                defaultValue={TimeUnit.MINUTES}
                                                handleOnChange={(val) => handleChange('timeUnit', val)}
                                                sx={{ '.MuiOutlinedInput-notchedOutline': { borderStyle: 'none' } }}
                                            />
                                        </InputAdornment>
                                    )
                                }}
                            />

                            <TextField
                                sx={{ flex: 1 }}
                                label={t("venue")}
                                value={event.venue}
                                onChange={(event) => handleChange('venue', event.target.value)}
                            />
                        </Box>
                    </Box>
                    

                    <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginTop: "30px" }}>
                        <CancelButton onAction={handleClose} />
                        <UpdateButton onAction={handleUpdate} />
                    </Box>
                </ModalContainer>
            </Modal>
        </Box>
    );
};

export default EditEvent;
