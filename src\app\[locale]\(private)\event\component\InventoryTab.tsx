"use client"
import { useTranslations } from 'next-intl';
import { Box, Card, Checkbox, Icon, IconButton, Skeleton, styled, Tab, Tabs, Typography } from '@mui/material';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { generalPadding } from '@/styles/theme';
import { COLORS } from '@/styles/colors';
import { generateDisplayableDate, generateDisplayableDateTime, generateDisplayableDuration, generateDisplayableTime, getDay } from '@/utils/date';
import Image from 'next/image';
import { CellContext, ColumnDef, getCoreRowModel, getExpandedRowModel, useReactTable } from '@tanstack/react-table';
import PaginationTable from '@/components/PaginationTable';
import EditTicketSetting from './EditTicketSetting';
import EditTicketVariation from './EditTicketVariation';
import AddTicketVariation from './AddTicketVariation';
import EditTicketSection from './EditTicketSection';
import _ from 'lodash';
import AddTicketType, { SELLING_PLATFORM } from './AddTicketType';
import EditTicketType from './EditTicketType';
import BasicDateCalendar from '@/components/Calendar';
import Select from '@/components/input/Select';
import { ThirdParty, TicketSection, TicketType } from '../[id]/section/page';
import dayjs from 'dayjs';
import { AVAILABLE_DAY, LANGUAGE_CODE } from '@/utils/constants';
import { useRouter } from 'next/navigation';

interface Props {
  eventInfo?: any,
  isLoading: boolean;
  isEdit: boolean;
  id: number;
  language: LANGUAGE_CODE;
}

const InventoryTab = ({ id, eventInfo, isLoading }: Props) =>{
    const t = useTranslations('event');
    const tTicket = useTranslations('ticket');
    const tCommon = useTranslations('common');
    const [value, setValue] = useState(1);
    const [sectionData, setSectionData] = useState<{
        [x: number]: {
            [x: number]: TicketSection[]
        }
    }>();
    const [selectedTime, setSelectedTime] = useState<{
        [x: number]: number[]
    }>({})
    const [thirdParty, setThirdParty] = useState<ThirdParty[]>([])
    const [exportedToThirdParty, setExportedToThirdParty] = useState<{
        [thirdPartyId: number]: {
            [date: number]: {
                [section: number]: number,
            }
        }
    }>({})

    const router = useRouter();

    const {
        ticket_setting: ticketSetting = [],
        ...baseInfo
    } = (eventInfo?.data?.data?.event ?? {})

    const {
        ticket_section: ticketSection = [],
        ticket_type: ticketType = []
    } = (ticketSetting ?? {})

    const FilterOption = useCallback(() => {
        const filterOption: { label: string, value: number }[] = []

        ticketSection.map((section: TicketSection) => {
            const {
                day,
                date
            } = section

            if (day) {
                const found = AVAILABLE_DAY.find((available) => available.value === day)
                if (found) filterOption.push(found)
            }
            if (date) {
                filterOption.push({ label: dayjs.unix(Number(date)).format('YYYY/M/D'), value: Number(date)})
            }
        })
        return _.uniqBy(filterOption, 'value').sort((a, b) => a.value - b.value)
    }, [ticketSection])

    const generateCalendarData = useCallback(() => {
        const {
            start_date: eventStartDate,
            end_date: eventEndDate
        } = baseInfo

        const startDate = dayjs.unix(Number(eventStartDate)).startOf('day')
        const endDate = dayjs.unix(Number(eventEndDate)).startOf('day')

        const diffInDays = endDate.diff(startDate, 'days')

        const parsedDayObject: { dateExcemption: number[] } & { [x: string]: TicketSection[] } = {
            dateExcemption: []
        }
        ticketSection.map((section: TicketSection) => {
            const {
                day,
                date,
            } = section

            if (day) {
                if (parsedDayObject[day]) {
                    parsedDayObject[day].push(section)
                } else {
                    parsedDayObject[day] = [section]
                }
            }
            if (date) {
                parsedDayObject.dateExcemption.push(Number(date))
                if (parsedDayObject[date]) {
                    parsedDayObject[date].push(section)
                } else {
                    parsedDayObject[date] = [section]
                }
            }
        })
        const parsedSection: {[x: number]: { [x: number]: TicketSection[] } } = {}
        const tabs = FilterOption()

        tabs.forEach(({ value }, index) => {
            parsedSection[index] = {}
        })

        Array(diffInDays).fill(0).map((_, idx) => {
            const currentDate = startDate.add(idx, 'day')
            const nextDate = startDate.add(idx+1, 'day')
            
            const found = parsedDayObject.dateExcemption.find((item) => {
                return nextDate.unix() >= item && currentDate.unix() <= item
            })

            if (found) {
                const foundTab = tabs.findIndex(({ value }) => value === found)
                if (!foundTab || !tabs[foundTab]?.value) return
                parsedSection[tabs[foundTab].value] = { [found]: parsedDayObject[found].map((item) => {
                    return {
                        ...item,
                        available: item.ticket_date_inventory?.reduce((acc: number, cur) => acc + cur.available , 0),
                        total: item.ticket_date_inventory?.reduce((acc: number, cur) => acc + cur.total , 0),
                    }
                }) }
            } else {
                const weekday = currentDate.day()
                const foundTab = tabs.findIndex(({ value }) => value === weekday)
                const clone = [ ...(parsedDayObject?.[weekday] ?? []) ]
                if (!parsedSection[tabs[foundTab]?.value]) return
                parsedSection[tabs[foundTab]?.value][currentDate.unix()] = clone.map((item) => {
                    const {
                        ticket_date_inventory = [],
                        ...base
                    } = item
                    const foundDate = ticket_date_inventory.filter((inventory) => {
                        return dayjs.unix(Number(inventory.timestamp)).format('YYYYMMDD') === currentDate.format('YYYYMMDD')
                    })
                    return {
                        ...item,
                        ticket_date_inventory: foundDate,
                        timestamp: currentDate.unix(),
                        available: foundDate.reduce((acc: number, cur) => acc + cur.available , 0),
                        total: foundDate.reduce((acc: number, cur) => acc + cur.total , 0),
                    }
                })
            }
        })

        return parsedSection
    }, [FilterOption, baseInfo, ticketSection])

    const formatedDate = useCallback(() => {
        if (!sectionData) return [] 
        return Object.keys(sectionData[value]).map((key: any) => ({ info: sectionData[value][key], id: key}))
    }, [sectionData, value])

    const calculateExported = useCallback(() => {
        const exportedObject: {
            [thirdPartyId: number]: {
                [date: number]: {
                    [section: number]: number,
                }
            }
        } = {}
        formatedDate().map(({
            info
        }) => {
            info.map((
                item
            ) => {
                item?.ticket_distribution?.map((next) => {
                    const {
                        ticket_distribution_adjustment,
                        ticket_type,
                        ticket_date_inventory
                    } = next

                    const thirdPartyId = ticket_type?.third_party?.id || 0
                    const {id, timestamp} = ticket_date_inventory
                    const total = ticket_distribution_adjustment.reduce((acc, next) => acc + next.amount, 0)

                    exportedObject[thirdPartyId] = {
                        ...(exportedObject[thirdPartyId] ?? {}),
                        [timestamp]: {
                            ...(exportedObject?.[thirdPartyId]?.[timestamp] ?? {}),
                            [id as number]: total,
                        }
                    }
                })
            })
        })
        return exportedObject
    }, [formatedDate])

    const generatedThirdParty = useCallback(() => {
        const allThirdParty: ThirdParty[] = []
        ticketType.map((type: TicketType) => {
            const {
                third_party
            } = type
            if (!third_party) return;
            allThirdParty.push({ id: third_party.id, name: third_party.name })
        })
        return allThirdParty
    }, [ticketType])

    useEffect(() => {
        if (ticketSection && ticketSection.length > 0) {
            const parsedSection = generateCalendarData()
            setSectionData(parsedSection)
            // const parsedExported = calculateExported()
            // setExportedToThirdParty(parsedExported)
        }
    }, [ticketSection, t, generateCalendarData])

    useEffect(() => {
        const parsedExported = calculateExported()
        setExportedToThirdParty(parsedExported)
        const allThirdParty = generatedThirdParty()
        setThirdParty(allThirdParty)
    }, [value, calculateExported, generatedThirdParty])

    const calAvailable = ({info}: {
        info: {
            ticket_date_inventory: {
                available: number
            }[]
        }[]
    }) => {
        return info?.reduce(
            (acc: number, cur) => {
                const allTicket = cur.ticket_date_inventory.reduce(
                    (acc: number, { available }: { available: number }) => acc + available, 0
                )

                return acc + allTicket
            }, 0
        )
    }

    const handleSelected = useCallback((checked: boolean, id: number, parent?: number) => {
        if (sectionData?.[value]) {
            let selected = {}
            if (sectionData[value][id]) {
                selected = checked? {
                    [id]: sectionData[value][id]
                    .map((section) => section.id)
                }: {}
            } 
            else if (parent && sectionData[value][parent]) {
                selected = checked? {
                    [parent]: [...(selectedTime[parent] || []), id]
                }: {
                    [parent]: selectedTime[parent].filter(target => target !== id)
                }
            }
            setSelectedTime(selected)
        }
    }, [sectionData, selectedTime, value])

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                accessorKey: "id",
                header: "",
                cell: (data) => (
                    <Box sx={{ display: 'flex', alignItems: 'center'}}>
                        <Checkbox
                            value={data.row.original.id}
                            checked={
                                (data.row.original.info && selectedTime?.[data.row.original.id]) ||
                                selectedTime?.[data.row.original.timestamp]?.includes(data.row.original.id)
                            }
                            onChange={(event) => 
                                handleSelected(event.target.checked, data.row.original.id, data.row.original.timestamp)
                            }
                            aria-label={`event-id-${data.row.original.id}-actions`}
                        />
                        {
                            data.row.original.info && (
                                <Image
                                    src={
                                        data.row.getIsExpanded()? 
                                        `/images/icon-arrowUp.svg`
                                        :`/images/icon-arrowDown.svg`
                                    }
                                    width={16}
                                    height={16}
                                    alt='expands'
                                    onClick={() => {
                                        const expanded = data.row.getIsExpanded()
                                        data.row.toggleExpanded(!expanded)
                                    }}
                                />
                            )
                        }
                    </Box>
                ),
            },
            {
                accessorKey: "id",
                header: tTicket("available_day"),
                cell: (data) => (
                    <>
                        {
                            data.row.original?.info?
                            dayjs.unix(data.row.original.id).format("YYYY/M/D"):
                            ''
                        }
                    </>
                )
            },
            {
                accessorKey: "time",
                header: tTicket("time_slot"),
                cell: (data) => {
                    const txtArr: string[] = []
                    if (data.row.original.start_time) {
                        const {
                            hour,
                            min
                        } = generateDisplayableTime(data.row.original.start_time, true)
                        txtArr.push(`${hour}:${min}`)
                    }
                    if (data.row.original.end_time) {
                        const {
                            hour,
                            min
                        } = generateDisplayableTime(data.row.original.end_time, true)
                        txtArr.push(`${hour}:${min}`)
                    }
                    return (
                        <>
                            { txtArr.join(" - ") }
                        </>
                    )
                }
            },
            {
                accessorKey: "ticket_date_inventory",
                header: tTicket("total_qty"),
                cell: (data) => (
                    <>
                        {
                            calAvailable(data.row.original)
                        }
                        {
                            data.row.original?.ticket_date_inventory?.reduce(
                                (acc: number, { total }: { total: number}) => acc + total, 0
                            )
                        }
                    </>
                )
            },
            {
                accessorKey: "ticket_date_inventory",
                header: tTicket("available"),
                cell: (data) => (
                    <>
                        {
                            calAvailable(data.row.original)
                        }
                        {
                            data.row.original?.ticket_date_inventory?.reduce(
                                (acc: number, { available }: { available: number}) => acc + available, 0
                            )
                        }
                    </>
                )
            },
            ...(Object.values(thirdParty).flat().map(({id, name}) => ({
                accessorKey: `third_party_${name}`,
                header: `${tTicket("export_to")} ${name}`,
                cell: (data: any) => (
                    <>
                        {
                            data.row.original.info && 
                            (
                                Object.values(exportedToThirdParty?.[id]?.[data.row.original.id] ?? [])?.reduce(
                                    (acc, item) => acc + item ,0
                                )
                            )
                        }
                        {
                            !data.row.original.info && (
                                (data.row.original.ticket_date_inventory || [])?.reduce(
                                    (acc: number, {id: sectionId}: {
                                        id: number
                                    }) => {
                                        return acc + (exportedToThirdParty?.[id]?.[data.row.original.timestamp]?.[sectionId] ?? 0)
                                    } ,0
                                )
                            )
                        }
                    </>
                )
            })))
        ],
        [
            exportedToThirdParty,
            thirdParty,
            handleSelected,
            selectedTime,
            tTicket
        ]
    );

    const table = useReactTable({
        data: formatedDate(),
        columns,
        getSubRows: (row) => {
            return row.info
        },
        getCoreRowModel: getCoreRowModel(),
        getExpandedRowModel: getExpandedRowModel(),
        manualPagination: true,
    });

    const renderSection = () => (
        <Card
            sx={{
                display: 'flex',
                flexDirection: 'column',
                padding: '20px',
                gap: '16px'
            }}
        >            
            <Box>
                <PaginationTable
                    table={table}
                    isLoading={isLoading}
                    msg={t("title_empty")}
                    skipPagination={true}
                />
            </Box>
        </Card>
    )

    const handlePageChange = (value: number) => {
        router.push(`/event/${id}/export/${value}`)
    }

    const handleFilter = (val: any) => {
        const options = FilterOption()
        const found = options.findIndex(({ value, label }) => value === val)

        if (found > -1) setValue(options[found].value)
    }

    const renderSectionTable = () => {
        return (
            <Card>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        padding: '20px',
                        justifyContent: 'space-between',
                        flexWrap: 'wrap',
                        rowGap: '12px'
                    }}
                >
                    <Box 
                        sx={{
                            display: 'flex',
                            gap: '16px',
                            width: '451px',
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}
                    >
                        <Typography
                            variant='body1'
                            fontWeight='bold'
                            width='400px'
                        >
                            {tTicket("filter_by_date")}
                        </Typography>
                        <Select
                            data={FilterOption()}
                            handleOnChange={handleFilter}
                            defaultValue={value}
                            value={value}
                        />
                    </Box>
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            gap: '16px',
                            justifyContent: 'center'
                        }}
                    >
                        <Typography
                            variant='body2'
                            fontWeight='bold'
                            sx={{
                                textDecoration: 'underline',
                                marginTop: 'auto',
                                marginBottom: 'auto'
                            }}
                            color={COLORS.GREY_6}
                            onClick={() => router.push(`/event/${id}/history`)}
                        >
                            {tTicket("history")}
                        </Typography>
                        <Select
                            label={tTicket("export_to_third_party")}
                            data={
                                _.uniqBy(Object.values(thirdParty).map(({ id, name}) => {
                                    return { label: name, value: id }
                                }), 'id')
                            }
                            sx={{ width: '200px' }}
                            handleOnChange={handlePageChange}
                        />
                    </Box>
                </Box>

                {renderSection()}
            </Card>
        )
    }
    console.log("sectionData >> ", sectionData)
    return(
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                paddingRight: '32px',
                gap: '32px',
                height: 'fit-content'
            }}
        >   
            <BasicDateCalendar 
                startDate={baseInfo.start_date}
                endDate={baseInfo.end_date}
                extra={
                    sectionData &&
                     _.keyBy(Object.values(sectionData).map((item) => Object.values(item)).flat(2), 'timestamp')
                }
            />
            {renderSectionTable()}
        </Box>
    )
}

export default InventoryTab;