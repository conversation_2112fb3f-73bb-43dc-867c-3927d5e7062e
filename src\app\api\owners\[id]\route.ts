import { IOwner } from "@/interface/IOwner";
import {
  authorizedDelete,
  authorizedGet,
  authorizedPut,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { SOCIAL_MEDIAS } from "@/utils/constants";
import { NextRequest } from "next/server";

async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const data = await authorizedGet<any>(
      `/owners/${params.id}`,
      await getAuthHeaders(req)
    );
    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { socialMedias, ...body } = await req.json();

    const data = await authorizedPut<IOwner>(
      `/owners/${params.id}`,
      await getAuthHeaders(req),
      { ...body, socialUrls: socialMedias }
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await authorizedDelete(`/owners/${params.id}`, await getAuthHeaders(req));
    return Response.json({ status: "success" }, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET, PUT, DELETE };
