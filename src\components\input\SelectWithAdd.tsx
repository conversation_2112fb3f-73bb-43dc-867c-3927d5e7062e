"use client";
import * as React from "react";
import { Autocomplete, Box, Chip, MenuItem, Modal, Select as MuiSelect, SelectProps, TextField } from "@mui/material";
import { useTranslations } from "next-intl";
import InputContainer, { InputContainerProps } from "./InputContainer";
import ModalContainer from "../ModalContainer";
import CancelButton from "../buttons/CancelButton";
import UpdateButton from "../buttons/UpdateButton";

interface Props extends InputContainerProps {
  data: { label: string; value?: string|number, extra?: any }[];
  customChange: (val: any) => void;
}

export default function SelectWithAdd({
  label,
  description,
  placeholder,
  variant = "outlined",
  fullWidth = true,
  error,
  required,
  multiple = false,
  customChange,
  data,
  ...otherProps
}: Props & Omit<SelectProps, "error">) {
  const t = useTranslations("error");
  const tCommon = useTranslations("common");
  const [inputValue, setInputValue] = React.useState("")
  const [options, setOptions] = React.useState(data)
  const [selected, setSelected] = React.useState(data)
  const [open, setOpen] = React.useState<{label: string, idx: Number, value?: any}|never|undefined>()

  React.useEffect(() => {
    if (!selected) return
    customChange(selected)
  }, [selected, customChange])

  const handleOnChange = (event: any, data: any) => {
    setSelected(data)
  }

  const handleInputValue = (event: any) => {
    const {
        value
    } = event.target

    if (value[value.length - 1] === ",") {
        const replaced = value.replace(",", "")
        setInputValue("")
        setSelected([...selected, { label: replaced, value: replaced }])
    } else {
        setInputValue(value)
    }
  }

  const handleClose = () => {
    setOpen(undefined)
  }

  const handleUpdate = () => {
    if (!open) return
    setSelected(selected.map((val, index) => {
      if (index !== open.idx) return val
      return {
        ...val,
        label: open.label
      }
    }))
    setOpen(undefined)
  }

  const handleEdit = (val: string) => {
    if (!open) return;
    setOpen({
      ...open,
      label: val
    })
  }

  return (
    <InputContainer label={label} description={description} required={required}>
        <Autocomplete
            multiple
            id="tags-outlined"
            options={options}
            getOptionLabel={(option: any) => option.label}
            filterSelectedOptions
            renderTags={(tagValue, getTagProps) => {
              return tagValue.map((option, idx) => (
                <Chip
                  {...getTagProps({ index: idx })}
                  key={`chip_${idx}`}
                  label={option.label}
                  onClick={() => setOpen({...option, idx})}
                />
              ));
            }}
            renderInput={(params: any) => (
                <TextField
                    {...params}
                    label={label}
                    placeholder={placeholder}
                    onChange={handleInputValue}
                    value={inputValue}
                />
            )}
            value={selected}
            onChange={handleOnChange}
        />
        <Modal open={!!open} onClose={handleClose}>
          <ModalContainer sx={{ width: 'fit-content', padding: '40px', gap: '32px' }}>
              <TextField
                  value={open?.label}
                  onChange={(event) => handleEdit(event.target.value)}
              />
              <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginTop: "30px" }}>
                  <CancelButton onAction={handleClose} />
                  <UpdateButton onAction={handleUpdate} label={tCommon("button_save")}/>
              </Box>
          </ModalContainer>
        </Modal>
    </InputContainer>
  );
}
