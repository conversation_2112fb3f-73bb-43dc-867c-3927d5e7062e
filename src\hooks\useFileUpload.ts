"use client";
import {
  ACCEPTED_AUDIO_TYPES,
  ACCEPTED_IMAGE_TYPES,
  ACCEPTED_VIDEO_TYPES,
} from "@/utils/constants";
import { getSession } from "next-auth/react";
import { useCallback } from "react";
import xior from "xior";

const readFile = (file: File) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = new Int8Array(e.target?.result as any);
      resolve(data);
    };
    reader.onerror = (e) => {
      reject(Error("Read file fail."));
    };
    reader.readAsArrayBuffer(file);
  });

const useFileUpload = () => {
  const uploadFile = useCallback(
    async (file?: File | string, module: string = "users") => {
      if (!file) return "";
      if (typeof file === "string") return file;

      const session = await getSession();
      if (!session?.accessToken) throw Error("invalid_access_token");
      const res = await xior.post(
        "/files",
        {
          module,
          contentType: file.type,
        },
        {
          headers: { Authorization: `Bearer ${session!.accessToken}` },
          baseURL: process.env.NEXT_PUBLIC_API_BASE,
        }
      );

      const { url, presignedUrl } = res.data;
      const data = await readFile(file);
      await xior.put(presignedUrl, data, {
        headers: { "Content-Type": file.type },
      });
      return url;
    },
    []
  );

  const uploadFileForRegister = useCallback(
    async (file: File | string, otp: string, email: string) => {
      if (!file) return "";
      if (typeof file === "string") return file;

      const res = await xior.post(
        "/files",
        {
          module: "users",
          contentType: file.type,
          email,
          otp: parseInt(otp),
        },
        {
          baseURL: process.env.NEXT_PUBLIC_API_BASE,
        }
      );

      const { url, presignedUrl } = res.data;
      
      const data = await readFile(file);
      await xior.put(presignedUrl, data, {
        headers: { "Content-Type": file.type },
      });
      console.log("upload success:", url);
      return url;
    },
    []
  );

  return { uploadFile, uploadFileForRegister };
};

export default useFileUpload;
