import { IListResponse } from "@/interface/IListResponse";
import { IMember } from "@/interface/IMember";
import {
  authorizedGet,
  authorizedPost,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";

async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const region = searchParams.get("region")? searchParams.get("region")?.split(","): undefined
    const data = await authorizedGet<IListResponse<IMember>>(
      "/api/public/v1/event",
    //   await getAuthHeaders(req),,
      {},
      {
        page: searchParams.get("page"),
        size: searchParams.get("size"),
        search: searchParams.get("search"),
        region,
        status: searchParams.get("status")
      }
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const data = await authorizedPost<any>(
        `/api/admin/v1/event`,
        //   await getAuthHeaders(req)
        {},
        body
    );
    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET, POST };
