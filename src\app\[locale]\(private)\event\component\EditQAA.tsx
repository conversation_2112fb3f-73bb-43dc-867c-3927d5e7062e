"use client";
import * as React from "react";
import { Box, Button, Modal, TextField, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { ICollection } from "@/interface/ICollection";
import { useRouter } from "next/navigation";
import { LANGUAGE_CODE, ROUTES } from "@/utils/constants";
import EditButton from "@/components/buttons/EditButton";
import TextEditor from "@/components/RichEditor";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";
import Image from "next/image";
import { EventTranslation, extractTranslationBody } from "@/utils/translation";

interface Props {
    id?: number;
    target: number;
    content: {
      id: number,
      answser: string,
      question: string,
    }[];
    language: LANGUAGE_CODE
}

const buttonStyle = {
  flex: 1,
  fontSize: "1rem",
  fontWeight: 700,
  minWidth: "132px",
  minHeight: "46px",
};

const EditQAA = ({ id, target, content, language }: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations("event");
  const [open, setOpen] = React.useState(false);
  const [qaa, setQAA] = React.useState<any>({});

  React.useEffect(() => {
    const found = content.find((item) => item.id === target)
    if (!found) return
    setQAA(found)
  }, [content, target])

  const mutation = useMutation({
    mutationFn: () => {
        const url = ['/api/event/qaa']
        if (id) url.push(`/${id}`)
        return xior.put(url.join(""), {
          qaa: content.map((item) => {
              if (item.id === target) return qaa
              return item
          }),
          language
        })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["event"]});
      setOpen(false);
    },
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (mutation.isPending) return;
    setOpen(false);
  }, [mutation.isPending]);

  const handleUpdate = () => {
    mutation.mutate()
  }

  const handleChange = (target: 'question'|'answer', value: string) => {
    setQAA({
        ...qaa,
        [target]: value
    })
  }

  return (
    <Box>
        <Box onClick={handleOpen}>
            <Image
                src={'/images/icon-edit.svg'}
                width={48}
                height={48}
                alt={'edit'}
            />
        </Box>
        <Modal open={open} onClose={handleClose}>
            <ModalContainer sx={{ padding: '40px', gap: '32px' }}>
                <Typography variant="h3">{t("qaa")}</Typography>
                <TextField
                    size="medium"
                    disabled={mutation.isPending}
                    label={t("question")}
                    value={qaa.question}
                    onChange={(event) => handleChange('question', event.target.value)}
                    placeholder={t("question")}
                />

                <TextField
                    size="medium"
                    disabled={mutation.isPending}
                    label={t("answer")}
                    value={qaa.answer}
                    onChange={(event) => handleChange('answer', event.target.value)}
                    placeholder={t("answer")}
                />

                <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginTop: "30px" }}>
                    <CancelButton onAction={handleClose} />
                    <UpdateButton onAction={handleUpdate} />
                </Box>
            </ModalContainer>
        </Modal>
    </Box>
  );
};

export default EditQAA;
