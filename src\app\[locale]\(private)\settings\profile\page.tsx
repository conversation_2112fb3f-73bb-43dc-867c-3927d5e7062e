"use client";
import type { NextPage } from "next";
import { Box, CircularProgress } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import { useQuery } from "@tanstack/react-query";
import xior from "xior";
import LabelValue from "@/components/LabelValue";
import EditButton from "@/components/buttons/EditButton";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import { IProfile } from "@/interface/IProfile";
import { format } from "date-fns";
import { useMemo } from "react";

const Profile: NextPage = () => {
  const t = useTranslations("profile");
  const router = useRouter();
  const { data, isLoading } = useQuery({
    queryKey: ["profile"],
    queryFn: () => xior.get<IProfile>("/api/profile").then((res) => res.data),
  });

  const addressValue = useMemo(
    () =>
      [data?.addressLine1, data?.addressLine2, data?.addressLine3]
        .filter((item) => !!item)
        .join("\n"),
    [data?.addressLine1, data?.addressLine2, data?.addressLine3]
  );

  if (isLoading) {
    return (
      <Box
        sx={{ height: "100%" }}
        display={"flex"}
        justifyContent="center"
        alignItems={"center"}
      >
        <CircularProgress color="primary" />
      </Box>
    );
  }

  return (
    <Box sx={{ height: "100%" }}>
      <PageHeader title={t("label_title")}>
        <EditButton
          onClick={() => {
            router.push(`${ROUTES.PROFILE}/edit`);
          }}
        />
      </PageHeader>

      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
      >
        <Box
          component={"img"}
          src={data?.photoUrl}
          sx={{
            height: 132,
            width: 132,
            borderRadius: 66,
            border: "0.5px solid #000000",
            mb: 3,
            objectFit: "cover",
          }}
        />
        <LabelValue
          label={t("label_identity")}
          value={data?.identity && t(`identity_value_${data.identity}`)}
        />
        <LabelValue label={t("label_name")} value={data?.name} />
        <LabelValue
          label={t("label_gender")}
          value={data?.gender && t(`gender_value_${data.gender}`)}
        />
        <LabelValue label={t("label_email")} value={data?.email} />
        <LabelValue
          label={t("label_phone_number")}
          type="phone"
          value={data?.phoneNumber}
        />
        <LabelValue
          label={t("label_contact_methods")}
          type="contacts"
          value={data?.contacts}
        />
        <LabelValue
          label={t("label_dob")}
          value={data?.dateOfBirth && format(data.dateOfBirth, "dd-MM-yyyy")}
        />
        <LabelValue label={t("label_address")} value={addressValue} />
        <LabelValue
          type="country"
          label={t("label_country")}
          value={data?.countryCode}
        />
        <LabelValue
          type="socialMedia"
          label={t("label_social_media")}
          value={data?.socialMedias}
        />
      </Box>
    </Box>
  );
};

export default Profile;
