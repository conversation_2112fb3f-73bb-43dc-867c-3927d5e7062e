import { Action, AnyAction, combineReducers, configureStore, ThunkAction } from "@reduxjs/toolkit";
import { commonReducer } from "./store/commonSlice";
import saleOrderListReducer from "./store/saleOrderListSlice";
import { createWrapper, HYDRATE } from "next-redux-wrapper";

export interface State {
  common: any;
}

// const combinedReducer = combineReducers({
//   common: commonReducer,
//   saleOrder: saleOrderListReducer,
//   sideMenu: sideMenuReducer,
// });
export const store = configureStore({
  reducer: {
    common: commonReducer,
    saleOrder: saleOrderListReducer,
    // sideMenu: sideMenuReducer,
  },
});

// const reducer = (state: ReturnType<typeof combinedReducer>, action: AnyAction) => {
//   return combinedReducer(state, action);
// };

export const makeStore = () => store;
type Store = ReturnType<typeof makeStore>;

export type AppStore = ReturnType<typeof makeStore>;
export type AppDispatch = Store["dispatch"];
export type RootState = ReturnType<Store["getState"]>;
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;

export const wrapper = createWrapper(makeStore, { debug: true });
