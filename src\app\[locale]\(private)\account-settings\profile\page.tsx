"use client"
import {
    Ava<PERSON>,
    <PERSON><PERSON>,
    Checkbox,
    IconButton,
    Modal,
    <PERSON>Field,
    Typography,
  } from "@mui/material";
import { useTranslations } from "next-intl";
import xior, { XiorError } from "xior";
import { IProfile } from "@/interface/IProfile";
import { useEffect, useState } from "react";
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import CloseIcon from '@mui/icons-material/Close';
import CustomButton from "@/components/buttons/CustomButton"
import LockIcon from '@mui/icons-material/Lock';
import EditIcon from '@mui/icons-material/Edit';
import CustomTextField from "@/components/input/CustomTextField"
import {
  checkPasswordLength,
  checkUppercase,
  checkLowercase,
  checkSpecialCharacter,
  checkNumber,
} from "@/utils/validators";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import {showErrorPopUp,showSuccessPopUp} from "@/utils/toast";
import ChangePassword from "./change-password";
import ProfileDetails from "./profile-details";
import CircularProgress from '@mui/material/CircularProgress';

// const Item = styled(Paper)(({ theme }) => ({
//     backgroundColor: '#fff',
//     ...theme.typography.body2,
//     padding: theme.spacing(1),
//     textAlign: 'center',
//     color: theme.palette.text.secondary,
//     ...theme.applyStyles('dark', {
//       backgroundColor: '#1A2027',
//     }),
//   }));

// const style = {
//   position: 'absolute',
//   top: '50%',
//   left: '50%',
//   transform: 'translate(-50%, -50%)',
//   width: 400,
//   bgcolor: 'background.paper',
//   // border: '2px solid #000',
//   boxShadow: 24,
//   p: 4,
// };

// const textFieldStyle = {
//     '& .MuiOutlinedInput-root': {
//       '&:hover fieldset': {
//           borderColor: 'rgba(79, 183, 71, 1)', 
//       },
//       '&.Mui-focused fieldset': {
//           borderColor: 'rgba(79, 183, 71, 1)', 
//       },
//   },
//   '& .MuiInputLabel-root.Mui-focused': {
//       color: 'rgba(79, 183, 71, 1)', 
//   },
// }

// const selectStyle = {
//   '&:hover .MuiOutlinedInput-notchedOutline': {
//     borderColor: 'rgba(79, 183, 71, 1)',
//   },
//   '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
//     borderColor: 'rgba(79, 183, 71, 1)',
//   },
// }

// const menuItemStyle = {
//   '&:hover': {
//     backgroundColor: 'rgba(79, 183, 71, 1)', // MenuItem hover 時背景顏色
//   },
//   '&.Mui-selected': {
//     backgroundColor: 'rgba(79, 183, 71, 1)', // 選中時背景顏色
//     '&:hover': {
//       backgroundColor: 'rgba(79, 183, 71, 0.8)', // 選中且 hover 時的背景顏色
//     },
//   },
// }


const Profile = () =>{
    const t = useTranslations("profile");
    const spt = useTranslations("setup_profile");
    const [profileDto,setProfileDto] =  useState<IProfile | undefined>(undefined);
    const [country, setCountry] = useState("hk");
    const [isCheck,setIsCheck] = useState<boolean>(false);
    const [open, setOpen] = React.useState(false);
    const [newPassword, setNewPassword] = useState<string>("");
    const [confirmNewPassword, setConfirmNewPassword] = useState<string>("");
    const [currentPassword,setCurrentPassword] = useState<string>("");
    const { showToast: showSuccess } = showSuccessPopUp();
    const { showToast: showError } = showErrorPopUp();
    const [validations, setValidations] = useState({
      length: false,
      uppercase: false,
      lowercase: false,
      specialChar: false,
      number: false,
    });

    console.log("profileDto",profileDto)
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);  

    const handleNewPasswordChange = (event:any) =>{
      const newPassword = event.target.value;
      setNewPassword(newPassword);
      setValidations({
        length: checkPasswordLength(newPassword),
        uppercase: checkUppercase(newPassword),
        lowercase: checkLowercase(newPassword),
        specialChar: checkSpecialCharacter(newPassword),
        number: checkNumber(newPassword),
      });
    }
 
    const handleConfirmNewPassword = (event:any) =>{
      setConfirmNewPassword(event.target.value);
    }

    const handleCurrentPassword = (event:any) =>{
      setCurrentPassword(event.target.value);
    }

    const handleChange = (event: SelectChangeEvent) => {
      setCountry(event.target.value as string);
    };

    const fetchProfileDataApi = async () =>{
      try {
        const response = await xior.get<IProfile>('/api/profile');
        setProfileDto(response.data);
      } catch (err) {
        console.error("Failed to fetch profile data", err);
      };
    }

    const handleChangeReceivedPromotionCode = (event:any) =>{
      setIsCheck(event.target.checked)
    }

    const handleChangePasswordApi = async () =>{

      if(!currentPassword){
        showError("Current password name is required.");
        return;
      }

      if(!newPassword){
        showError("New password name is required.");
        return;
      }

      if(!confirmNewPassword){
        showError("Confirm new password is required.");
        return;
      }

      if(newPassword !== confirmNewPassword){
        showError("Passwords do not match.");
        return;
      } 

      try{

        const response = await xior.post("/api/auth/change-password",{
          oldPassword:currentPassword,
          newPassword:newPassword,
          confirmPassword:confirmNewPassword
        })

        console.log("update password successfully", response.data)

        if(response){
          showSuccess("Update password successfully");
          await handleClose()
        }
        
      }catch(err){
        console.log("invalid_credentials ",err);
        showError ("Invalid credentials, Please try again."); 
      }
      
    }

    useEffect(() => {
        fetchProfileDataApi();
    }, []);

  //   useEffect(() => {
  //     if (profile?.receivePromotions) {
  //         setIsCheck(profile.receivePromotions);
  //     }
  // }, [profile]);

  //   console.log("profile",profile)
    
    return(
        <>
        {
          profileDto ?
          <ProfileDetails
          profileDto={profileDto}
          handleOpen={handleOpen}
          fetchProfileDataApi={fetchProfileDataApi}
          />:
          <Box sx={{ display: 'flex' }}>
          <CircularProgress />
          </Box>
        }
          <ChangePassword
          handleChangePasswordApi={handleChangePasswordApi}
          handleClose={handleClose}
          open={open}
          newPassword={newPassword}
          confirmNewPassword={confirmNewPassword}
          currentPassword={currentPassword}
          handleConfirmNewPassword={handleConfirmNewPassword}
          handleCurrentPassword={handleCurrentPassword}
          handleNewPasswordChange={handleNewPasswordChange}
          validations={validations}
          />
        </>
    )
}

export default Profile;