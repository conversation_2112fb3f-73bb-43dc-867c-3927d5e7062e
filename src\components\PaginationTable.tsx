"use client";
import React from "react";
import { flexRender, Table } from "@tanstack/react-table";
import {
  Table as MuiTable,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Box,
  Pagination,
  PaginationItem,
} from "@mui/material";
import ArrowBackIcon from "./icons/ArrowBackIcon";
import ArrowFirstIcon from "./icons/ArrowFirstIcon";
import { COLORS } from "@/styles/colors";

interface Props<DataType> {
  table: Table<DataType>;
  fullWidth?: boolean;
  button?: any;
  isLoading?: boolean;
  msg?: string;
  skipPagination?: boolean;
  renderCustomItem?: () => React.ReactNode
}

const PaginationTable = <T,>({
  table,
  fullWidth,
  button,
  isLoading,
  skipPagination = false,
  msg,
  renderCustomItem
}: Props<T>) => {
  const {
    pagination: { pageSize, pageIndex },
  } = table.getState();

  const renderNormalTable = () => (
    <TableContainer sx={{ width: fullWidth ? "100%" : "fit-content" }}>
      <MuiTable>
        <TableHead sx={{
          "& th": {
            backgroundColor: COLORS.GREY_3,
            borderWidth: '0px',
            color: COLORS.GREY_6
          }
        }}>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow
              key={headerGroup.id}
            >
              {headerGroup.headers.map((header) => {
                return (
                  <TableCell key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableCell>
                );
              })}
            </TableRow>
          ))}
        </TableHead>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow
              key={row.id}
            >
              {row.getVisibleCells().map((cell) => (
                <TableCell
                  key={cell.id}
                  style={{
                    width: cell.column.getSize(),
                    borderWidth: '0px'
                  }}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </MuiTable>
      {
        !isLoading && table.getRowModel()?.rows?.length === 0? (
          <div style={{ alignItems: 'center', textAlign: 'center'}}>{
            msg? msg: 'No record'
          }</div>
        ): null
      }
    </TableContainer>
  )

  return (
    <Box
      height="100%"
      display={"flex"}
      flexDirection={"column"}
      justifyContent={"space-between"}
      gap={"30px"}
    >
      {renderCustomItem? renderCustomItem() : renderNormalTable()}
      {button}
      <Box alignSelf={"center"}>
        {
          !skipPagination && (
            <Pagination
          shape="rounded"
          variant="outlined"
          showFirstButton
          showLastButton
          count={table.getPageCount()}
          page={pageIndex + 1}
          onChange={(event: React.ChangeEvent<unknown>, newPage: number) => {
            table.setPageIndex(newPage - 1);
          }}
          renderItem={(item) => (
            <PaginationItem
                  slots={{
                    previous: () => <ArrowBackIcon sx={{ height: 14, width: 7 }} />,
                    next: () => (
                      <ArrowBackIcon
                        sx={{ height: 14, width: 7, transform: "rotate(180deg)" }}
                      />
                    ),
                    first: () => <ArrowFirstIcon sx={{ height: 14, width: 11 }} />,
                    last: () => (
                      <ArrowFirstIcon
                        sx={{ height: 14, width: 11, transform: "rotate(180deg)" }}
                      />
                    ),
                    // , next: ArrowForwardIcon
                  }}
                  {...item}
                />
              )}
            />
          )
        }
        
      </Box>
    </Box>
  );
};

export default PaginationTable;
