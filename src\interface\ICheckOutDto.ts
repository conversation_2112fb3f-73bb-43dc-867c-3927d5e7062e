export interface ICheckOutDto {
    success: boolean;
    order:   Order;
}

export interface Order {
    quantity: number;
    product:  Product;
    user:     User;
    jwt:      string;
}

export interface Product {
    id:                  number;
    name:                string;
    description:         string;
    thumbnail:           string;
    audio:               string;
    owner:               number;
    createdAt:           number;
    updatedAt:           number;
    status:              number;
    price:               string;
    tax:                 string;
    type:                number;
    currency:            string;
    weight:              string;
    weightUnit:          string;
    isDigital:           boolean;
    throughTicketSystem: number;
    eventId:             null;
}

export interface User {
    id:                   number;
    email:                string;
    password:             string;
    name:                 string;
    verificationCode:     null;
    resetPasswordToken:   null;
    resetPasswordExpires: null;
    isActive:             boolean;
    gender:               string;
    phoneNumber:          string;
    dateOfBirth:          Date;
    countryCode:          string;
    address:              string;
    photoUrl:             null;
    receivePromotions:    boolean;
    identity:             string;
    role:                 string;
    supportGoogle:        boolean;
    supportFacebook:      null;
}
