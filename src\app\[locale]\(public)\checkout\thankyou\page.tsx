"use client"
import { <PERSON>, <PERSON><PERSON>, Container, IconButton, Typography } from "@mui/material";
import React, { useEffect, useState } from 'react';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorIcon from '@mui/icons-material/Error';
const ThankYouPage = () => {
    const [orderNo, setOrderNo] = useState<string>('');
    const [txnState, setTxnState] = useState<string | null>(null);
    const handleCloseTab = () => {
        window.close();
    };

    useEffect(() => {
        const currentUrl = window.location.href;
        const url = new URL(currentUrl);
        const merchantOrderNo = url.searchParams.get('merchant_order_no');
        const txnStateFromUrl = url.searchParams.get('txn_state');
        if (merchantOrderNo) setOrderNo(merchantOrderNo)
        if(txnStateFromUrl) setTxnState(txnStateFromUrl)
    }, [])


    const renderPaymentStatusMessage = () =>{

        if(txnState?.includes("SUCCESS")){
            return(
                <>
                <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    height="85vh"
                    flexDirection="column"
                    sx={{
                        backgroundColor: "rgba(246, 249, 255, 1)",
                        marginTop: "40px",
                        marginRight: "100px",
                        marginLeft: "100px"
                    }}
                >
                    <IconButton sx={{ color: "rgba(195, 214, 82, 1)" }}>
                        <CheckCircleOutlineIcon fontSize="large" />
                    </IconButton>
                    <Typography variant="h1">Congratulations!</Typography><br />
                    <Typography variant="body1">
                        Your order <span style={{ fontWeight: 'bold', color: '#24378C' }}>#{orderNo}</span> has been placed.
                    </Typography>
                    <Typography variant="body1">We have just sent the invoice to your email.</Typography>
                    <Typography variant="body1">Please close the tab and return to Funverse.</Typography><br />
                    <Button onClick={handleCloseTab}>Close</Button>
                </Box>
                </>
            )
        }else if(txnState?.includes("PAYERROR")){
            return(
                <>
                    <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    height="85vh"
                    flexDirection="column"
                    sx={{
                        backgroundColor: "rgba(246, 249, 255, 1)",
                        marginTop: "40px",
                        marginRight: "100px",
                        marginLeft: "100px"
                        }}
                    >
                    <IconButton sx={{ color: "rgba(255, 0, 0, 1)" }}>
                        <ErrorIcon style={{ fontSize: '35px' }}/>
                    </IconButton>
                        <Typography id="modal-modal-title" variant="h2" component="h2">
                            Oops...
                        </Typography>
                        <Typography id="modal-modal-description" sx={{ mt: 2,mb: 2 }}>
                        An error occurred while processing your payment. Please try again.
                        </Typography>
                        <Button  variant="contained" onClick={() => {
                             console.log("History length:", window.history.length); // Log history length
                            window.history.go(-2)}}>Back</Button>
                    </Box>
                </>
            )
        }else{
            return(
                <>
                </>
            )
        }
    }



    return (
        <div>
            <Container>
                {
                    renderPaymentStatusMessage()
                }
            </Container>
        </div>
    );
}

export default ThankYouPage;