import { Box, Table, TableBody, TableContainer, TableHead, Typography } from "@mui/material"
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { useEffect, useState } from "react";
import { IProductWarehouseRecords } from "@/interface/IProductWarehouseRecord";
import xior from "xior";
import AdjustmentHistoryRow from "./AdjustmentHistoryRow";

type Props = {
    params: { id: string };
}

const AdjustmentHistoryTable = ({ params }: Props) => {

    const [getInventoryRecordsList, setGetInventoryRecordsList] = useState<IProductWarehouseRecords[] | undefined>(undefined)

    const getInventoryRecordsApi = async () => {
        const response = await xior.get(`/api/inventory/getrecords/${params.id}`)
        setGetInventoryRecordsList(response.data);
    }

    useEffect(() => {
        getInventoryRecordsApi();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    console.log(getInventoryRecordsList)

    return (
        <>
            <TableContainer>
                <Table sx={{ minWidth: 650, mt: "10px" }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell>Updated At</TableCell>
                            <TableCell>Activity By</TableCell>
                            <TableCell>Unavailable</TableCell>
                            <TableCell>Change in Unavailable</TableCell>
                            <TableCell>Committed</TableCell>
                            <TableCell>Change in Committed</TableCell>
                            <TableCell>Available</TableCell>
                            <TableCell>Change in Available</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {

                            getInventoryRecordsList?.map((value) => (
                                <AdjustmentHistoryRow key={value.id} productWarehouseRecordDto={value} />
                            ))

                        }
                    </TableBody>
                </Table>
            </TableContainer>
        </>
    )
}

export default AdjustmentHistoryTable