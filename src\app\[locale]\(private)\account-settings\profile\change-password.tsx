"use client"
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>box,
    IconButton,
    Modal,
    TextField,
    Typo<PERSON>,
  } from "@mui/material";
import { useTranslations } from "next-intl";
import xior, { XiorError } from "xior";
import { IProfile } from "@/interface/IProfile";
import { useEffect, useState } from "react";
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import CloseIcon from '@mui/icons-material/Close';
import CustomButton from "@/components/buttons/CustomButton"
import LockIcon from '@mui/icons-material/Lock';
import EditIcon from '@mui/icons-material/Edit';
import CustomTextField from "@/components/input/CustomTextField"
import CheckCircleIcon from '@mui/icons-material/CheckCircle';


type Props = {
    handleChangePasswordApi:() => void
    handleClose:()=> void
    open:boolean
    newPassword:string
    confirmNewPassword:string
    currentPassword:string
    handleNewPasswordChange:(event:any) => void
    handleConfirmNewPassword:(event:any) => void
    handleCurrentPassword:(event:any) => void
    validations:any
}

const ChangePassword = ({
                        handleChangePasswordApi,
                        handleClose,
                        open,
                        newPassword,
                        confirmNewPassword,
                        currentPassword,
                        handleNewPasswordChange,
                        handleConfirmNewPassword,
                        handleCurrentPassword,
                        validations,
                        }:Props) =>{

    const rpt = useTranslations("reset_password");

    const isFormValid = () => {
      return (
          validations.length &&
          validations.uppercase &&
          validations.lowercase &&
          validations.specialChar &&
          validations.number 
      );
      };

    const style = {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 400,
        bgcolor: 'background.paper',
        // border: '2px solid #000',
        boxShadow: 24,
        p: 4,
      };

      const passwordValidation = {
        display: "flex",
        color:"rgba(189, 189, 189, 1)"
      }
      
      const textStyles = {
        color:"rgba(189, 189, 189, 1)",
      }

    return(
        <>
        <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
        >
        <Box sx={style}>
            <Typography id="modal-modal-title" variant="h6" component="h2">
            Text in a modal
            </Typography>
            <Typography id="modal-modal-description" sx={{ mt: 2 }}>
            Duis mollis, est non commodo luctus, nisi erat porttitor ligula.
            </Typography>
        </Box>
        </Modal>
            <Modal
        open={open}
        // onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
        sx={{
            backdropFilter: 'blur(4px)', // 添加模糊效果
            backgroundColor: 'rgba(122, 122, 123, 0.5)', // 设置半透明背景
        }}
        >
        <Box sx={style}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end' }}>
            <IconButton onClick={handleClose}>
                <CloseIcon/>
            </IconButton>
            </Box>
            <Typography id="modal-modal-title" variant="h2" component="h2">
            {rpt('title')}
            </Typography>
            <Box sx={{
                display:'flex',
                flexDirection:"column",
                minWidth: 120,
                mt:4,
                gap: 4
            }}>
            <CustomTextField
            namespace="change_password"
            label="label_current_password"
            placeholder="placeholder_current_password"
            type="password"
            value={currentPassword}
            onChange={handleCurrentPassword}
            />
            <CustomTextField
            namespace="change_password"
            label="label_new_password"
            placeholder="placeholder_new_password"
            type="password"
            value={newPassword}
            onChange={handleNewPasswordChange}
            />
            <CustomTextField
            namespace="change_password"
            label="label_confirm_password"
            placeholder="placeholder_confirm_password"
            type="password"
            value={confirmNewPassword}
            onChange={handleConfirmNewPassword}
            />
            <Box>
            <Box sx={passwordValidation}>
                <CheckCircleIcon sx={{ color: validations.length ? '#ff7802' : 'gray' }} />&nbsp;
                <Typography >
                8-20 characters
                </Typography>
            </Box>
            <Box sx={passwordValidation}>
                <CheckCircleIcon sx={{ color: validations.uppercase ? '#ff7802' : 'gray' }} />&nbsp;
                <Typography sx={textStyles}>
                An UPPERCASE letter
                </Typography>
            </Box>
            <Box sx={passwordValidation}>
                <CheckCircleIcon sx={{ color: validations.lowercase ? '#ff7802' : 'gray' }} />&nbsp;
                <Typography sx={textStyles}>
                A lowercase letter
                </Typography>
            </Box>
            <Box sx={passwordValidation}>
                <CheckCircleIcon sx={{ color: validations.specialChar ? '#ff7802' : 'gray' }} />&nbsp;
                <Typography sx={textStyles}>
                    A special character
                </Typography>
            </Box>
            <Box sx={passwordValidation}>
                <CheckCircleIcon sx={{ color: validations.number ? '#ff7802' : 'gray' }} />&nbsp;
                <Typography sx={textStyles}>
                    A number
                </Typography>
            </Box>
            </Box>
            <CustomButton label="確認" disabled={!isFormValid()} onClick={handleChangePasswordApi}/>
            </Box>
        </Box>
        </Modal>
        </>
    )
}

export default ChangePassword;