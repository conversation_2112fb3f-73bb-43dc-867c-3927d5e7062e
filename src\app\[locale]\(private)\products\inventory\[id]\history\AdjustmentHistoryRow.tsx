import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { IProductWarehouseRecords } from "@/interface/IProductWarehouseRecord";
import { format } from 'date-fns';

type Props = {
    productWarehouseRecordDto:IProductWarehouseRecords
}

const AdjustmentHistoryRow = ({productWarehouseRecordDto}:Props) =>{

    const createdAtformattedDate = format(new Date(productWarehouseRecordDto.createdAt! * 1000), "yyyy-MM-dd HH:mm:ss"); 

    const { unavailableChanged,committedChanged,quantityChanged } = productWarehouseRecordDto;

    const getColor = (value:any) => {
        if (value! > 0) return 'green';
        if (value! < 0) return 'red';
        return 'black';
    };

    return(
        <>
            <TableRow>
                <TableCell>{createdAtformattedDate}</TableCell>
                <TableCell>{productWarehouseRecordDto.userName}</TableCell>
                <TableCell>
                {productWarehouseRecordDto.currentUnavailable ? productWarehouseRecordDto.currentUnavailable : "-"}
                 </TableCell>
                 <TableCell style={{ color: getColor(unavailableChanged) }}> {unavailableChanged ? `(${unavailableChanged})` : "-"}</TableCell>
                <TableCell>
                    {productWarehouseRecordDto.currentCommitted ? productWarehouseRecordDto.currentCommitted : "-"}
                </TableCell>
                <TableCell style={{ color: getColor(committedChanged) }}> {committedChanged ? `(${committedChanged})` : "-"}</TableCell>
                <TableCell>
                    {productWarehouseRecordDto.currentQuantity ? productWarehouseRecordDto.currentQuantity : "-"}
                </TableCell>
                <TableCell style={{ color: getColor(quantityChanged) }}> {quantityChanged ? `(${quantityChanged})` : "-"}</TableCell>
            </TableRow>
        </>
    )
}

export default AdjustmentHistoryRow