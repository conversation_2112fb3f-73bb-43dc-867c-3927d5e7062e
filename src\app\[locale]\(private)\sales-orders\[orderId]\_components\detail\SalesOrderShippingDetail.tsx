"use client";
import { Box, Paper, Stack, styled } from "@mui/material";
import { useTranslations } from "next-intl";
import { orderShippingType } from "../../../_enums/order.enum";
import { SaleOrderDetailType } from "../../../_schema/saleOrder.schema";

type Props = {
  saleOrderDetail?: SaleOrderDetailType;
};

const StyledBox = styled(Paper)(({ theme }) => ({
  width: 320,
  borderRadius: 16,
  border: `1px solid ${theme.palette.incutix.grey[400]}`,
  padding: "20px",
  backgroundColor: theme.palette.incutix.white,
}));
const DetailTitle = styled(Box)(({ theme }) => ({
  marginBottom: "24px",
  fontSize: 18,
  fontWeight: 700,
  color: "#000000",
}));
const DetailItemLabel = styled(Box)(({ theme }) => ({
  height: 20,
  display: "flex",
  width: "100%",
  justifyContent: "left",
  alignItems: "center",
  fontSize: 14,
  fontWeight: 400,
  color: theme.palette.incutix.grey[600],
}));
const DetailItemValue = styled(Box)(({ theme }) => ({
  minHeight: 20,
  display: "flex",
  width: "100%",
  justifyContent: "right",
  alignItems: "center",
  fontSize: 14,
  fontWeight: 400,
  color: "#000000",
  textAlign: "right",
}));

const useShippingTypeMapper = () => {
  const t = useTranslations("sales_orders.detail.shipping_detail");

  return {
    [orderShippingType.express]: t("shipping_method_express"),
    [orderShippingType.storePickup]: t("shipping_method_pickup"),
  };
};

const ConditionalDisplay = ({
  condition,
  children,
}: {
  condition: boolean;
  children: React.ReactNode;
}) => {
  return condition ? children : <></>;
};

const SalesOrderShippingDetail = ({ saleOrderDetail }: Props) => {
  const t = useTranslations("sales_orders.detail.shipping_detail");
  const shippingTypeMapper = useShippingTypeMapper();

  const data = saleOrderDetail?.orderShipping;

  return (
    <StyledBox elevation={0}>
      <DetailTitle>{t("title")}</DetailTitle>
      <Stack spacing={1}>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("shipping_method")}</DetailItemLabel>
          <DetailItemValue>{shippingTypeMapper[data?.shippingType ?? ""] ?? ""}</DetailItemValue>
        </Box>
        <ConditionalDisplay condition={!!data?.name}>
          <Box display={"flex"} flexDirection={"row"}>
            <DetailItemLabel>{t("consignee")}</DetailItemLabel>
            <DetailItemValue>{data?.name ?? ""}</DetailItemValue>
          </Box>
        </ConditionalDisplay>
        <ConditionalDisplay condition={!!data?.countryCode && !!data?.tel}>
          <Box display={"flex"} flexDirection={"row"}>
            <DetailItemLabel>{t("contract_no")}</DetailItemLabel>
            <DetailItemValue>{`${data?.countryCode} ${data?.tel}`}</DetailItemValue>
          </Box>
        </ConditionalDisplay>
        <ConditionalDisplay condition={!!data?.address}>
          <Box display={"flex"} flexDirection={"row"}>
            <DetailItemLabel>{t("shipping_address")}</DetailItemLabel>
            <DetailItemValue>{data?.address ?? ""}</DetailItemValue>
          </Box>
        </ConditionalDisplay>
      </Stack>
    </StyledBox>
  );
};

export default SalesOrderShippingDetail;
