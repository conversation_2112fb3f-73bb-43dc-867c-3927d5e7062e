export interface SalesOrderDNProduct {
    DNProduct: DNProduct[];
}

export interface DNProduct {
    id:               number;
    orderId:          number;
    productId:        number;
    quantity:         number;
    price:            string;
    currency:         string;
    metaData:         string;
    name:             string;
    description:      string;
    thumbnail:        string;
    audio:            string;
    owner:            number;
    createdAt:        number;
    updatedAt:        number;
    status:           number;
    taxIncluded:      number;
    tax:              string;
    type:             number;
    weight:           string;
    weightUnit:       string;
    isDigital:        number;
    openToAllMembers: number;
    category:         number;
    compareAt:        string;
    productType:      number;
    roomExcluded:     string;
    collections:      string;
    tags:             string;
    userId:           number;
}