"use client";
import * as React from "react";
import { useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { InputContainerProps } from "./InputContainer";
import { IOwner } from "@/interface/IOwner";
import { IProductCategory, SubCategory } from "@/interface/IProductCategory";
import { Select, MenuItem, Box } from "@mui/material";



interface Props extends InputContainerProps {
    placeholder?: string;
    disabled?: boolean;
    // onChange?: (value: any) => void ;
    onChange?: any;
    value?: any;
    defaultValue?: any;
    displayEmpty?: any;
}

const generateCategoryStructure = (categories: Array<IProductCategory>) => {
    const subCategoryList: SubCategory = {}
    const categoryList: Array<IProductCategory> = categories?.map((category) => {
        if (category.children) {
            subCategoryList[category.id] = category.children
        }
        return category
    })
    return {
        categoryList,
        subCategoryList
    }
}

export default function SelectCategory({ onChange, value, displayEmpty, ...otherProps }: Props) {
    const [category, setCategory] = React.useState<any>([])
    const [selectedCategory, setSelectedCategory] = React.useState(0)
    const [subCategory, setSubCategory] = React.useState<any>({})
    const [selectedSubCategory, setSelectedSubCategory] = React.useState(0)
    const [init, setInt] = React.useState(false)

    const {
        data
    } = useQuery({
        queryKey: ["productCategory"],
        queryFn: async () =>
            xior
                .get<IListResponse<IProductCategory>>("/api/product-category")
                .then((res) => ({
                    items: res.data.items
                }))
    });

    React.useEffect(() => {
        if (data?.items) {
            const {
                categoryList = [],
                subCategoryList = []
            } = generateCategoryStructure(data.items)

            setCategory(categoryList)
            setSubCategory(subCategoryList)
            setInt(true)
        }
    }, [data])

    React.useEffect(() => {
        if (init) {
            setSelectedSubCategory(value)
            const values = Object.values(subCategory)?.flat(1)
            const category: any = values.find((item: any) => item.id === value)
            setSelectedCategory(category?.parentId)
        }
        /* eslint-disable react-hooks/exhaustive-deps */
    }, [init])

    const handleOnChange = (event: any) => {
        const {
            value
        } = event.target

        setSelectedCategory(value)
    }

    const handleSubCategoryOnChange = (event: any) => {
        const {
            value
        } = event.target

        setSelectedSubCategory(value)
        onChange(event)
    }

    return (
        <React.Fragment>
            <Box display={'flex'} flexDirection={"row"} gap={1}>
                {/* <Select
                    required
                    data={category?.map((category: any) => ({
                        label: category.name,
                        value: category.id
                    })) ?? []}
                    sx={{ width: 360 }}
                    value={selectedCategory}
                    onChange={handleOnChange}
                    displayEmpty
                    size="small"
                >
                    {
                        category?.map((category: any) => (
                            <MenuItem key={category.id} value={category.id}>{category.name}</MenuItem>
                        ))
                    }
                </Select> */}
                <Select
                    required
                    sx={{ width: 360 }}
                    value={selectedCategory}
                    onChange={handleOnChange}
                    displayEmpty
                    size="small"
                >
                    {category?.map((cat: any) => (
                        <MenuItem key={cat.id} value={cat.id}>
                            {cat.name}
                        </MenuItem>
                    )) ?? <MenuItem value=""><em>No categories available</em></MenuItem>}
                </Select>

                {/* <Select
                    required
                    data={subCategory[selectedCategory]?.map((category: any) => ({
                        label: category.name,
                        value: category.id
                    })) ?? []}
                    sx={{ width: 360 }}
                    value={selectedSubCategory}
                    onChange={handleSubCategoryOnChange}
                    displayEmpty
                    size="small"
                >
                    {
                        subCategory?.[selectedCategory]?.map((category: any) => (
                            <MenuItem key={category.id} value={category.id}>{category.name}</MenuItem>
                        ))
                    }
                </Select> */}
                <Select
                    required
                    sx={{ width: 360 }}
                    value={selectedSubCategory}
                    onChange={handleSubCategoryOnChange}
                    displayEmpty
                    size="small"
                >
                    {subCategory?.[selectedCategory]?.map((category: any) => (
                        <MenuItem key={category.id} value={category.id}>
                            {category.name}
                        </MenuItem>
                    )) ?? <MenuItem value=""><em>No subcategories available</em></MenuItem>}
                </Select>
                
            </Box>
        </React.Fragment>
    );
}
