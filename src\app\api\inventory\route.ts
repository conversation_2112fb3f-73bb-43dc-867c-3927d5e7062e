import {
    authorizedGet,
    getAuthHeaders,
    authorizedPut,
    handleApiError,
    authorizedPost,
  } from "@/utils/api";
import { NextRequest , NextResponse} from "next/server";
import { IProductWarehouseRecords } from "@/interface/IProductWarehouseRecord";


async function GET(req: NextRequest) { 
    try {

      const data = await authorizedGet(
        '/inventory',
        await getAuthHeaders(req)
      )
      return Response.json(data, { status: 200 });
      } catch (error) {
        return handleApiError(error);
      }

}

async function POST(req: NextRequest) {
    try{

      const inventoryRecords:IProductWarehouseRecords = await req.json();

      const data = await authorizedPost<IProductWarehouseRecords>(
        '/inventory',
        await getAuthHeaders(req),
        inventoryRecords
      )

      return NextResponse.json(data, { status: 200 });
    } catch(error){
      return handleApiError(error);
    }
}

// async function PUT(req: NextRequest) {

//   let emptyBody;

//   try {
//     emptyBody = await req.json();
//   } catch (error) {
//     return NextResponse.json({ message: 'Invalid JSON' }, { status: 400 });
//   }

//   try {
//     const data = await authorizedPut(
//       '/inventory/getupdateInventory',
//       await getAuthHeaders(req),
//       emptyBody
//     );

//     return NextResponse.json(data, { status: 200 });
//   } catch (error) {
//     return handleApiError(error);
//   }
// }

export { GET , POST};

