import {Alert, Snackbar} from "@mui/material";

type Props ={
  open:boolean,
  handleClose: ()=> void
}

export default function AddUpdateProductSuccessSnackBar({open,handleClose}:Props){
  return(
    <Snackbar
      sx={{
        marginTop:"64px",
        backgroundColor:"white",
        borderRadius: "10px",
        border: "2px solid #24378C", 
        color:"#24378C",
      }}
      open={open}
      autoHideDuration={6000}
      onClose={handleClose}
      anchorOrigin={{vertical: "top",horizontal:"center"}}
    >
      <Alert
        onClose={handleClose}
        severity="success"
        variant="filled"
        sx={{
          width: '100%' ,
          backgroundColor:"white",
          border: "2px solid #24378C", 
          color:"#24378C"
        }}
      >
        Successfully Updated
      </Alert>
    </Snackbar>
  )
}