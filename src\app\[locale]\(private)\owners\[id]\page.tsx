"use client";
import { Box, CircularProgress } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import { useQuery } from "@tanstack/react-query";
import xior from "xior";
import LabelValue from "@/components/LabelValue";
import EditButton from "@/components/buttons/EditButton";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import { IOwner } from "@/interface/IOwner";
import Link from "next/link";

interface Props {
  params: { id: string };
}

const OwnerDetail = ({ params: { id } }: Props) => {
  const t = useTranslations();
  const router = useRouter();
  const { data, isLoading } = useQuery({
    queryKey: ["owner"],
    queryFn: () =>
      xior.get<IOwner>(`/api/owners/${id}`).then((res) => res.data),
  });

  if (isLoading) {
    return (
      <Box
        sx={{ height: "100%" }}
        display={"flex"}
        justifyContent="center"
        alignItems={"center"}
      >
        <CircularProgress color="primary" />
      </Box>
    );
  }
  
  return (
    <Box sx={{ height: "100%" }}>
      <PageHeader title={[t("owner.label_title"), data?.name || ""]}>
        <EditButton
          onClick={() => {
            router.push(`${ROUTES.OWNER}/${id}/edit`);
          }}
        />
      </PageHeader>

      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
      >
        { !data?.photoUrl 
        && 
          <Box
            component={"a"}
            href={`${ROUTES.OWNER}/${id}/edit`}
            sx={{
              height: 132,
              width: 132,
              borderRadius: 100,
              border: "0.5px solid #000000",
              mb: 3,
              objectFit: "cover",
              background: "#c5c6c7"
            }}
          />
        }
        { data?.photoUrl 
        && 
          <Box
            component={"img"}
            src={data?.photoUrl}
            sx={{
              height: 132,
              width: 132,
              borderRadius: 66,
              border: "0.5px solid #000000",
              mb: 3,
              objectFit: "cover",
            }}
          />
        }
        <LabelValue label={t("owner.label_owner_id")} value={data?.ownerId} />
        <LabelValue label={t("owner.label_name")} value={data?.name} />
        <LabelValue
          label={t("owner.label_gender")}
          value={data?.gender && t(`profile.gender_value_${data.gender}`)}
        />
        <LabelValue label={t("owner.label_email")} value={data?.email} />
        <LabelValue
          label={t("owner.label_owner_introduction")}
          value={data?.introduction}
        />
        <LabelValue
          type="country"
          label={t("owner.label_country")}
          value={data?.countryCode}
        />
        <LabelValue
          type="socialMedia"
          label={t("owner.label_social_media")}
          value={data?.socialMedias}
        />
      </Box>
    </Box>
  );
};

export default OwnerDetail;
