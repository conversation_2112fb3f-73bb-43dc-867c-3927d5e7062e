import * as React from "react";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import dayjs, { Dayjs } from "dayjs";
import styled from 'styled-components';
import { COLORS } from "@/styles/colors";
import { Box, Typography } from '@mui/material';
import { useTranslations } from "next-intl";

const StyledDateCalendar = styled(DateCalendar)({
  '&.MuiDateCalendar-root': {
    display: 'block',
    margin: '5px',
    width: '100%',
    maxHeight: 'none',
    marginTop: '0px',
    height: 'auto',
    '& .MuiPickersCalendarHeader-root': {
      backgroundColor: COLORS.GREY_4,
      marginBottom: '0px',
      marginTop: '0px',
      position: 'relative'
    },
    '& .MuiDayCalendar-weekDayLabel': {
      fontSize: '1rem',
      backgroundColor: COLORS.GREY_4,
      width: '100%',
      margin: '0px',
      border: `1px solid ${COLORS.GREY_3}`
    },
    '& div[role="row"]': {
      justifyContent: 'space-around',
    },
    '& .MuiDayCalendar-slideTransition': {
      minHeight: '35vw',
    },
    '& .MuiPickersDay-root': {
      height: '50px',
      width: '50px',
      fontSize: '1rem',
      border: `1px solid ${COLORS.GREY_3}`
    },
    "& .MuiButtonBase-root-MuiIconButton-root-MuiPickersArrowSwitcher-button":
    {
      padding: "0px !important",
    },
    "& .MuiPickersCalendarHeader-labelContainer": {
      marginLeft: "auto !important",
    },
    "& .MuiIconButton-edgeEnd": {
      top: 0,
      position: 'absolute',
      left: '5%'
    },
    "& .MuiIconButton-edgeStart": {
      top: 0,
      position: 'absolute',
      right: '5%'
    },
    "& .MuiPickersCalendarHeader-switchViewButton": {
      display: 'none'
    },
    "& .MuiDayCalendar-weekContainer": {
      margin: '0px'
    }
  }
});

type Props = {
  startDate: number,
  endDate: number,
  extra?: { [date: number]: any }
}

export default function BasicDateCalendar({ startDate, endDate, extra }: Props) {
  const [value, setValue] = React.useState(dayjs(startDate * 1000));
  const [saleStart, setSaleStart] = React.useState(dayjs(startDate * 1000))
  const [saleEnd, setSaleEnd] = React.useState(dayjs(endDate * 1000))
  const t = useTranslations('common')

  const [extraMap, setExtraMap] = React.useState<{ [date: string]: any }>();

  const isValidDate = (timestamp: number) => {
    const timestampObject = dayjs(timestamp)
    return (
      timestampObject.format('YYYYMMDD') >= saleStart.format('YYYYMMDD') 
      && timestampObject.format('YYYYMMDD') < saleEnd.format('YYYYMMDD')
    )
  }

  const bgColorTone = [
    COLORS.PRIMARY_4,
    COLORS.BLUE_3,
    COLORS.SECONDARY_3
  ]

  const colorTone = [
    COLORS.PRIMARY_1,
    COLORS.BLUE_1,
    COLORS.ORANGE
  ]

  React.useEffect(() => {
    if (!extra) return;
    const parsedExtra: { [date: string]: any } = {}
    
    Object.keys(extra).forEach((key) => {
      const numKey = Number(key)
      const dateKey = dayjs.unix(numKey).format('YYYYMMDD')
      parsedExtra[dateKey] = extra[numKey]
    })
    setExtraMap(parsedExtra)
  }, [extra])

  const renderTags = (timestamp: number) => {
    const date = dayjs(timestamp).format('YYYYMMDD')
    const foundMap = extraMap?.[date]
    if (!foundMap) return <></>
    const {
      ticket_date_inventory: inventory,
      ticket_distribution: distribution
    } = foundMap

    const thirdParty: { [name: string]: number } = {}
    const available = inventory.reduce((
      acc: number, cur: { available: number }
    ) => acc + cur.available, 0)

    distribution.forEach(({
      ticket_distribution_adjustment: adjustment,
      ticket_type: {
        third_party: {
          name
        }
      }
    }: {
      ticket_distribution_adjustment: {
        amount: number
      }[],
      ticket_type: {
        third_party: {
          name: string
        }
      }
    }) => {
      if (!thirdParty[name]) {
        thirdParty[name] = adjustment.reduce((acc, {amount}) => acc + amount ,0)
      } else {
        thirdParty[name] = thirdParty[name] + adjustment.reduce((acc, {amount}) => acc + amount ,0)
      }
    })

    return (
      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '4px',
          rowGap: '4px'
        }}
      >
        <Box
          sx={{
            backgroundColor: bgColorTone[0],
            borderRadius: '4px',
            color: colorTone[0],
            fontSize: '10px',
            padding: '1px 3px 0px 3px'
          }}
        >
          {`${t("total")}: ${available}`}
        </Box>
        {
          Object.keys(thirdParty).sort().map((key, idx) => {
            const targetIdx = idx + 1 > 2? 2: idx+ 1
            return (
              <Box
                key={`${thirdParty[key]}_${idx}`}
                sx={{
                  backgroundColor: bgColorTone[targetIdx],
                  borderRadius: '4px',
                  color: colorTone[targetIdx],
                  fontSize: '10px',
                  padding: '1px 3px 0px 3px'
                }}
              >
                {`${key}: ${thirdParty[key]}`}
              </Box>
            )
          })
        }
      </Box>
    )
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <StyledDateCalendar
        value={value}
        maxDate={saleEnd}
        minDate={saleStart}
        dayOfWeekFormatter={(_day) => `${dayjs(_day.valueOf()).format('ddd')}`}
        onChange={(newValue) => setValue(newValue)}
        slots={{
          day: (props) => (
            <Box sx={{ width: '100%', padding: '8px', aspectRatio: 163/90, border: `1px solid ${COLORS.GREY_3}`}}>
              <Typography
                variant="body2"
                fontWeight="400"
                color={
                  // @ts-ignore
                  isValidDate(props['data-timestamp'])? COLORS.BLACK : COLORS.GREY_4
                }
              >
                {
                  // @ts-ignore
                  dayjs(props['data-timestamp']).get('date')
                }
              </Typography>
              {
                // @ts-ignore
                renderTags(props['data-timestamp'])
              }
            </Box>
          ),
        }}
      />
    </LocalizationProvider>
  );
}