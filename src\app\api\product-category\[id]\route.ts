import { IProductCategory } from "@/interface/IProductCategory";
import {
  authorizedDelete,
  authorizedGet,
  authorizedPut,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";

async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const data = await authorizedGet<IProductCategory>(
      `/product-categories/${params.id}`,
      await getAuthHeaders(req)
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const category = await req.json();
    const data = await authorizedPut<IProductCategory>(
      `/product-categories/${params.id}`,
      await getAuthHeaders(req),
      { name: category.name, parentId: category.parent?.id || null }
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await authorizedDelete(
      `/product-categories/${params.id}`,
      await getAuthHeaders(req)
    );
    return Response.json({ status: "success" }, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET, PUT, DELETE };
