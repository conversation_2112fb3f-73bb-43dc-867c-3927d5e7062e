import { IListResponse } from "@/interface/IListResponse";
import { IMember } from "@/interface/IMember";
import {
  authorizedGet,
  authorizedPost,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";

async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const data = await authorizedPost<IListResponse<IMember>>(
      "/api/admin/v1/ticket/variation",
    //   await getAuthHeaders(req),,
      {},
      body
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { POST };
