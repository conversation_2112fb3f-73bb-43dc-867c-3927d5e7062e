"use client";
import { Chip, styled } from "@mui/material";
import {
  orderStatus,
  OrderStatusType,
} from "@/app/[locale]/(private)/sales-orders/_enums/order.enum";
import { useTranslations } from "next-intl";

type OrderStatusTagProps = {
  status: OrderStatusType;
};

// NOTE: Payment Status
const InProgressChip = styled(Chip)(({ theme }) => ({
  backgroundColor: theme.palette.incutix.secondary[300],
  color: theme.palette.incutix.secondary[100],
  border: 0,
  padding: "0px 0px",
  "& span.MuiChip-label": {
    fontWeight: 700,
    fontSize: 14,
    padding: "0px 12px",
  },
}));
const CompletedChip = styled(Chip)(({ theme }) => ({
  backgroundColor: theme.palette.incutix.primary[400],
  color: theme.palette.incutix.primary[200],
  border: 0,
  padding: "0px 0px",
  "& span.MuiChip-label": {
    fontWeight: 700,
    fontSize: 14,
    padding: "0px 12px",
  },
}));
const CancelChip = styled(Chip)(({ theme }) => ({
  backgroundColor: theme.palette.incutix.error[200],
  color: theme.palette.incutix.error[100],
  border: 0,
  padding: "0px 0px",
  "& span.MuiChip-label": {
    fontWeight: 700,
    fontSize: 14,
    padding: "0px 12px",
  },
}));

const useOrderStatusTagText = () => {
  const t = useTranslations("sales_orders.table.tag.payment_status");

  return {
    [orderStatus.inProgress]: t("in_progress"),
    [orderStatus.complete]: t("completed"),
    [orderStatus.cancel]: t("cancel"),
  };
};

const OrderStatusTag = ({ status }: OrderStatusTagProps) => {
  const orderStatusTagMapper = useOrderStatusTagText();
  const label = orderStatusTagMapper[status] ?? "";

  return (
    {
      [orderStatus.inProgress]: <InProgressChip label={label} />,
      [orderStatus.complete]: <CompletedChip label={label} />,
      [orderStatus.cancel]: <CancelChip label={label} />,
    }[status] ?? <></>
  );
};

export default OrderStatusTag;
