import React, { ChangeEvent } from "react";
import { IconButton, InputAdornment, TextFieldProps } from "@mui/material";
import TextField from "./TextField";
import { ClearIcon } from "@mui/x-date-pickers";
import debounce from "lodash.debounce";
import { SearchOutlined } from "@mui/icons-material";
import { InputContainerProps } from "./InputContainer";

interface Props {
  value: string;
  onChange: (value: string) => void;
  maxWidth?: number;
  style?: { [x: string]: any };
}
const SearchInput = ({ value, onChange: onChangeText, style, maxWidth }: Props) => {
  const searchRef = React.useRef<HTMLInputElement>();

  const handleKeywordChange = debounce((event: ChangeEvent<HTMLInputElement>) => {
    onChangeText(event.target.value);
  }, 1000);

  const handleOnClear = () => {
    if (searchRef?.current) {
      searchRef.current.value = "";
      onChangeText("");
    }
  }

  return (
    <TextField
      inputRef={searchRef}
      sx={{
          maxWidth: maxWidth || 450,
          '& input::placeholder':{
            fontSize: '13px', fontWeight:400
          },
          ...(style || {})
      }}
      size="medium"
      placeholder="Search"
      onChange={handleKeywordChange}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start" >
            <IconButton
              onClick={handleOnClear}
              edge="end"
              disabled={!value}
            >
              <SearchOutlined  style={{width:'1.5rem', fontSize: '1.5rem', height:'1.5rem'}}/>
            </IconButton>
          </InputAdornment>
        ),
        endAdornment: value? (
          <InputAdornment position="end" >
            <IconButton
              onClick={handleOnClear}
              edge="end"
              disabled={!value}
            >
              <ClearIcon  style={{width:'1.5rem', fontSize: '1.5rem', height:'1.5rem'}}/>
            </IconButton>
          </InputAdornment>
        ): null,
      }}
    />
  );
};

export default SearchInput;
