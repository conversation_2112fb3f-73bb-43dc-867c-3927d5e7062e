"use client"
import { useTranslations } from 'next-intl';
import { Box, IconButton, styled, Tab, Tabs, Typography } from '@mui/material';
import React, { useState, useEffect, useMemo } from 'react';
import TextField from '@/components/input/TextField';
import SearchInput from '@/components/input/SearchInput';
import Select from '@/components/input/Select';
import AddNewButton from '@/components/buttons/AddNewButton';
import { ROUTES } from '@/utils/constants';
import MyButton from '@/components/buttons/CustomButton';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { ColumnDef, getCoreRowModel, PaginationState, useReactTable } from '@tanstack/react-table';
import xior from 'xior';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import PageSelector from '@/components/PageSelector';
import PaginationTable from '@/components/PaginationTable';
import EventCard from './event/card';
import { SUPPORT_REGION } from '@/utils/timezone';
import AddEvent from './component/AddEvent';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const a11yProps = (index: number) => {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

const CustomTabPanel = (props: TabPanelProps) => {
    const { children, value, index, ...other } = props;

    return (
        <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
        >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );
}

const Container = styled(Box)(({}) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '32px'
}))

const Event = () =>{
    const t = useTranslations('event');
    const tCommon = useTranslations('common');
    const [value, setValue] = useState(0);
    const [keyword, setKeyword] = useState("");
    const [region, setRegion] = useState<string[]>([]);
    const router = useRouter();
    const [pagination, setPagination] = React.useState<PaginationState>({
        pageIndex: 0,
        pageSize: 10,
    });

    const TabContent = [{
        label: 'active',
        value: 'ACTIVE'
    }, {
        label: 'draft',
        value: 'DRAFT'
    }, {
        label: 'expired',
        value: 'EXPIRED'
    }, {
        label: 'inactive',
        value: 'INACTIVE'
    }]

    const {
        data: event,
        refetch,
        isLoading
    } = useQuery({
        queryKey: ["event", { pagination }],
        queryFn: async () => {
            return xior
            .get("/api/event", {
                params: {
                    page: pagination.pageIndex + 1,
                    size: pagination.pageSize,
                    search: keyword,
                    status: TabContent.find((item, idx) => idx === value)?.value,
                    region: region?.join(",")
                },
            })
        },
        placeholderData: keepPreviousData,
        retry: 3,
    });

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                accessorKey: "id",
                header: "",
                cell: (data) => (
                <Box
                    display={"flex"}
                    flexDirection={"row"}
                    justifyContent={"center"}
                    gap={1}
                    mx={1}
                    key={`event-id-${data.row.original.id}-actions`}
                >
                    {data.row.original.id}
                </Box>
                ),
            },
            {
                accessorKey: "name",
                header: t("label_name"),
            },
            {
                accessorKey: "updated_at",
                header: t("label_updated_at"),
                cell: (data) => {
                    const datetime = data.getValue()
                    if (!datetime) return "-"
                    if (datetime) {
                        return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
                    }
                }
            },
            {
                accessorKey: "created_at",
                header: t("label_registered_at"),
                cell: (data) => {
                const datetime = data.getValue()
                    if (!datetime) return "-"
                    if (datetime) {
                        return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
                    }
                }
            },
        ],
        [t]
    );
    const defaultData = useMemo<any[]>(() => [], []);
    const table = useReactTable({
        data: event?.data?.data?.events ?? defaultData,
        columns,
        rowCount: event?.data?.count,
        state: { pagination },
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        enableMultiRowSelection: true,
        manualPagination: true,
    });

    useEffect(() => {
        if (keyword || region || value) {
            refetch()
        }
    }, [refetch, keyword, region, value])

    const handlePageDetail = (id: number) => {
        router.push(`/event/${id}`)
    }

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const renderTab = () => (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '32px'
            }}
        >
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={value} onChange={handleChange}>
                    {
                        TabContent.map(({ label, value }, index) => (
                            <Tab key={label} label={tCommon(`${label}`)} {...a11yProps(index)} />
                        ))
                    }
                </Tabs>
            </Box>

            {renderActionBar()}
        </Box>
    )

    const renderActionBar = () => (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between'
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '16px'
                }}
            >
                <SearchInput
                    value={keyword}
                    onChange={(value: string) => {
                        setKeyword(value);
                    }}
                    maxWidth={300}
                    style={{
                        width: '300px'
                    }}
                />
                <Select
                    data={SUPPORT_REGION.map((region) => {
                        return {
                            ...region,
                            label: tCommon(region.label)
                        }
                    })}
                    multiple={true}
                    value={region || []}
                    handleOnChange={(value) => {
                        setRegion(value as string[])
                    }}
                    sx={{ width: '300px', borderRadius: '999px' }}
                    size="medium"
                />
            </Box>
            <AddEvent />
        </Box>
    )

    const renderTable = () => (
        <Box display={"flex"} flexDirection={"column"} height={"100%"}>
            {/* <Box display={"flex"} flexDirection={"row"} mb={3}>
                <Box flex={1} />
                <PageSelector
                    value={pagination.pageSize}
                    onChange={(newPageSize) => {
                        table.setPageSize(newPageSize);
                    }}
                />
            </Box> */}
            <Box flex={1}>
                <PaginationTable
                    table={table}
                    isLoading={isLoading}
                    msg={t("title_empty")}
                    renderCustomItem={() => {
                        return (
                            <>
                                {
                                    table.getRowModel().rows.map((row) => {
                                        const {
                                            name: eventName,
                                            created_at: createdAt,
                                            updated_at: updatedAt,
                                            start_date: startDate,
                                            start_time: startTime,
                                            end_date: endDate,
                                            end_time: endTime,
                                            venue,
                                            id,
                                            region_code: regionCode,
                                            ticket_setting: ticketSetting,
                                            event_media: medias = []
                                        } = row.original

                                        const {
                                            sale_start_datetime: saleStartDateTime,
                                            sale_end_datetime: saleEndDateTime,
                                            ticket_section: ticketSection = [],
                                        } = (ticketSetting || {})

                                        const totalTicket = ticketSection.reduce((
                                            acc: number,
                                            current: { quantity: number }
                                        ) => acc + current.quantity, 0)
                                        
                                        return (
                                            <EventCard
                                                key={`event_${id}_${name}`}
                                                eventName={eventName}
                                                createdAt={createdAt}
                                                startDate={startDate}
                                                startTime={startTime}
                                                endDate={endDate}
                                                endTime={endTime}
                                                id={id}
                                                venue={venue}
                                                regionCode={regionCode}
                                                medias={medias}
                                                saleStartDateTime={saleStartDateTime}
                                                saleEndDateTime={saleEndDateTime}
                                                totalTicket={totalTicket}
                                                onClick={() => handlePageDetail(id)}
                                            />
                                        )
                                    })  
                                }
                            </>
                        )
                            
                         
                   
                    }}
                />
            </Box>
        </Box>
    )

    return(
        <Container>
            <Typography>
                {t("list_event_title")}
            </Typography>
            {renderTab()}
            {renderTable()}
        </Container>
    )
}

export default Event;