"use client";
import * as React from "react";
import { Box, Button, Modal, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import DeleteButton from "@/components/buttons/DeleteButton";
import ModalContainer from "@/components/ModalContainer";
import { useRouter } from "next/navigation";
import { IInvitation } from "@/interface/IInvitation";

interface Props {
    invitation: IInvitation;
    refetch: Function;
}

const buttonStyle = {
  flex: 1,
  fontSize: "1rem",
  fontWeight: 700,
  minWidth: "132px",
  minHeight: "46px",
};

const DeleteInvitation = ({ invitation, refetch }: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const mutation = useMutation({
    mutationFn: () => xior.delete(`/api/members/invitation/${invitation.id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["invitation"]});
      refetch();
      setOpen(false);
    },
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (mutation.isPending) return;
    setOpen(false);
  }, [mutation.isPending]);

  return (
    <Box>
      <DeleteButton onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer
          sx={{
            alignItems: "center",
            minWidth: 510,
            minHeight: "300px",
            boxSizing: "border-box",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
          }}
        >
          <Box my={2} textAlign="center">
            <Typography fontWeight={700} fontSize={"1.25rem"}>
              {t("invitation.title_confirm_delete")}
            </Typography>
          </Box>
          <Box display={"flex"} flexDirection={"row"} minWidth={300}>
            <Button
              variant="contained"
              color="secondary"
              disabled={mutation.isPending}
              sx={{ color: COLORS.BLACK, ...buttonStyle }}
              onClick={handleClose}
            >
              {t("common.button_stay_in_page")}
            </Button>
            <Box width={36} />
            <Button
              variant="contained"
              disabled={mutation.isPending}
              onClick={() => mutation.mutate()}
              sx={buttonStyle}
            >
              {t("common.button_delete")}
            </Button>
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default DeleteInvitation;
