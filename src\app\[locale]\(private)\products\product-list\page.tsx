"use client"
import AddNewButton from "@/components/buttons/AddNewButton";
import ExportButton from "@/components/buttons/ExportButton";
import ImportButton from "@/components/buttons/ImportButton";
import PageHeader from "@/components/PageHeader";
import { Box, Typography } from "@mui/material"
import { useTranslations } from "next-intl";
import ProductsTable from "./components/ProductsTable";
import { ROUTES } from "@/utils/constants";
import { useEffect, useState } from "react";
import { IProduct } from "@/interface/IProduct";
import xior from "xior";


const ProductList = () =>{
    const t = useTranslations("product");
    const [allProductsDtoList,setAllProductsDto] = useState<IProduct[] | undefined>( undefined);
  

      const getAllProductsApi = async () => {

            try {
                const response = await xior.get(`/api/products`);
                setAllProductsDto(response.data);
            } catch (error) {
                console.error('Error fetching products:', error);
            }
    };
      


    useEffect(() => {
        getAllProductsApi();
    }, []); 
    
    return(
        <>
        <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
        <PageHeader title={t("label_title")}>
            <>
            <ExportButton  />
            <ImportButton  />
            <AddNewButton href={ROUTES.CREATE_PRODUCTS}/>
            </>
        </PageHeader>
        <Box flex={1} padding="26px 34px">
            {
                allProductsDtoList &&
            <ProductsTable 
            allProductsDtoList={allProductsDtoList}
            getAllProductsApi={getAllProductsApi}
            />
            }
            <Typography sx={{
                mt:"5px"
            }}>Product Create: {allProductsDtoList?.length}/1000</Typography>
        </Box>
        </Box>
        </>
    )
}

export default ProductList