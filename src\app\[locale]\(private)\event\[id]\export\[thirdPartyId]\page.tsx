"use client"
import { useTranslations } from 'next-intl';
import { Box, Card, Checkbox, Icon, IconButton, Menu, MenuItem, Skeleton, styled, Tab, Tabs, Typography } from '@mui/material';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { keepPreviousData, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import xior from 'xior';
import { COLORS } from '@/styles/colors';
import { generateDisplayableDate, generateDisplayableTime } from '@/utils/date';
import Image from 'next/image';
import { AVAILABLE_DAY } from '@/utils/constants';
import dayjs from 'dayjs';
import { TicketSection } from '../../section/page';
import { ColumnDef, ExpandedState, getCoreRowModel, getExpandedRowModel, useReactTable } from '@tanstack/react-table';
import PaginationTable from '@/components/PaginationTable';
import CancelButton from '@/components/buttons/CancelButton';
import UpdateButton from '@/components/buttons/UpdateButton';
import { useRouter } from 'next/navigation';
import _ from 'lodash';
import TextField from '@/components/input/TextField';
import Select from '@/components/input/Select';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const Container = styled(Box)(({}) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '32px'
}))

const a11yProps = (index: number) => {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

const CustomTabPanel = (props: TabPanelProps) => {
    const { children, value, index, ...other } = props;

    return (
        <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
        >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );
}

interface Props {
  params: { id: string, thirdPartyId: string };
}

const ThirdPartyExport = ({ params: { id, thirdPartyId } }: Props) =>{
    const t = useTranslations('event');
    const tTicket = useTranslations('ticket');
    const tCommon = useTranslations('common');
    const router = useRouter();
    const [value, setValue] = useState(0);
    const [sectionData, setSectionData] = useState<{
        [x: number]: {
            [x: number]: TicketSection[]
        }
    }>();
    const [exportValue, setExportValue] = useState<{ [x: number]: {
        [x: number]: number
    } }>({})
    const [expanded, setExpanded] = useState<ExpandedState>({})
    const [selectedTime, setSelectedTime] = useState<{
        [x: number]: number[]
    }>({})
    const [copy, setCopy] = useState<{
        [startTime: number]: number
    }>({})
    const queryClient = useQueryClient();
    const [ticketTypeId, setTicketTypeId] = useState(undefined)

    const {
        data: eventInfo,
        refetch,
        isLoading
    } = useQuery({
        queryKey: ["event"],
        queryFn: async () => {
            return xior
            .get(`/api/event/${id}`)
        },
        placeholderData: keepPreviousData,
        retry: 3,
    });

    const {
        ticket_setting: ticketSetting = {},
        ...baseInfo
    } = (eventInfo?.data?.data?.event ?? {})

    const {
        ticket_section: ticketSection = [],
        ticket_type: ticketType = []
    } = (ticketSetting ?? {})

    const availableType = ticketType.filter((type: { third_party: { id: number }}) => {
        const {
            third_party
        } = type
        return third_party?.id === Number(thirdPartyId)
    })
    const thirdParty = availableType?.[0]?.third_party

    const TabContent = useCallback(() => {
        const filterOption: { label: string, value: number }[] = []

        ticketSection.map((section: TicketSection) => {
            const {
                day,
                date
            } = section

            if (day) {
                const found = AVAILABLE_DAY.find((available) => available.value === day)
                if (found) filterOption.push(found)
            }
            if (date) {
                filterOption.push({ label: dayjs.unix(Number(date)).format('YYYY/M/D'), value: Number(date)})
            }
        })
        return _.uniqBy(filterOption, 'value').sort(({value: valueA}, {value: valueB}) => valueA - valueB)
    }, [ticketSection])


    const targetTicketType = availableType.find((type: { id: number }) => type.id === Number(ticketTypeId))

    const {
        ticket_type_to_section: availableSection = [],
        multiply
    } = (targetTicketType ?? {})

    const sectionFilter = _.uniq((availableSection ?? []).map(({
        ticket_section: {
            date,
            day
        }
    }: {
        ticket_section: {
            date: number | null,
            day: number | null
        }
    }) => {
        return date || day
    })).sort()

    useEffect(() => {
        if (ticketSection && ticketSection.length > 0) {
            const {
                start_date: eventStartDate,
                end_date: eventEndDate
            } = baseInfo

            const startDate = dayjs.unix(Number(eventStartDate)).startOf('day')
            const endDate = dayjs.unix(Number(eventEndDate)).startOf('day')

            const diffInDays = endDate.diff(startDate, 'days')

            const parsedDayObject: { dateExcemption: number[] } & { [x: string]: TicketSection[] } = {
                dateExcemption: []
            }
            ticketSection.map((section: TicketSection) => {
                const {
                    day,
                    date,
                } = section

                if (day) {
                    if (parsedDayObject[day]) {
                        parsedDayObject[day].push(section)
                    } else {
                        parsedDayObject[day] = [section]
                    }
                }
                if (date) {
                    parsedDayObject.dateExcemption.push(Number(date))
                    if (parsedDayObject[date]) {
                        parsedDayObject[date].push(section)
                    } else {
                        parsedDayObject[date] = [section]
                    }
                }
            })
            const parsedSection: {[x: number]: { [x: number]: TicketSection[] } } = {}
            const tabs = TabContent()

            tabs.forEach(({ value }, index) => {
                parsedSection[value] = {}
            })

            Array(diffInDays).fill(0).map((_, idx) => {
                const currentDate = startDate.add(idx, 'day')
                const nextDate = startDate.add(idx+1, 'day')
                
                const found = parsedDayObject.dateExcemption.find((item) => {
                    return nextDate.unix() >= item && currentDate.unix() <= item
                })

                if (found) {
                    const foundTab = tabs.findIndex(({ value }) => value === found)
                    if (!foundTab || !tabs[foundTab]?.value) return
                    parsedSection[tabs[foundTab].value] = { [found]: parsedDayObject[found].map((item) => {
                        return {
                            ...item,
                            available: item.ticket_date_inventory?.reduce((acc: number, cur) => acc + cur.available , 0),
                            total: item.ticket_date_inventory?.reduce((acc: number, cur) => acc + cur.total , 0),
                        }
                    }) }
                } else {
                    const weekday = currentDate.day()
                    const foundTab = tabs.findIndex(({ value }) => value === weekday)
                    const clone = [ ...(parsedDayObject?.[weekday] ?? []) ]
                    if (!parsedSection[tabs[foundTab]?.value]) return
                    parsedSection[tabs[foundTab]?.value][currentDate.unix()] = clone.map((item) => {
                        const {
                            ticket_date_inventory = [],
                            ...base
                        } = item
                        const foundDate = ticket_date_inventory.filter((inventory) => {
                            return dayjs.unix(Number(inventory.timestamp)).format('YYYYMMDD') === currentDate.format('YYYYMMDD')
                        })
                        return {
                            ...item,
                            ticket_date_inventory: foundDate,
                            timestamp: currentDate.unix(),
                            available: foundDate.reduce((acc: number, cur) => acc + cur.available , 0),
                            total: foundDate.reduce((acc: number, cur) => acc + cur.total , 0),
                        }
                    })
                }
            })
            setSectionData(parsedSection)
        }
    }, [ticketSection, ticketType, TabContent, baseInfo])

    useEffect(() => {
        setValue(sectionFilter?.[0] as number)
    }, [ticketTypeId, sectionFilter])

    const handleExportOptions = useCallback((timestamp: number, id: number, amount: number) => {
        setExportValue({
            ...exportValue,
            [timestamp]: {
                ...(exportValue[timestamp] || {}),
                [id]: amount
            }
        })
    }, [exportValue])

    const calAvailable = ({info}: {
        info: {
            ticket_date_inventory: {
                available: number
            }[]
        }[]
    }) => {
        return info?.reduce(
            (acc: number, cur) => {
                const allTicket = cur.ticket_date_inventory.reduce(
                    (acc: number, { available }: { available: number }) => acc + available, 0
                )

                return acc + allTicket
            }, 0
        )
    }

    const handleSelected = useCallback((checked: boolean, id: number, parent?: number) => {
        if (sectionData?.[value]) {
            let selected = {}
            if (sectionData[value][id]) {
                selected = checked? {
                    [id]: sectionData[value][id]
                    .map((section) => section.id)
                }: {}
            } 
            else if (parent && sectionData[value][parent]) {
                selected = checked? {
                    [parent]: [...(selectedTime[parent] || []), id]
                }: {
                    [parent]: selectedTime[parent].filter(target => target !== id)
                }
            }
            setSelectedTime(selected)
        }
    }, [sectionData, selectedTime, value])

    const getStartTimes = (key: number) => {
        return sectionData?.[value]?.[key]?.map(
            (section) => ({ id: section.id, startTime: section.start_time})
        ) || []
    }

    const handleCopyToAll = () => {
        const copyObject = handleCopy(true)
        const sectionKeys = Object.keys(sectionData?.[value] || {}).map((item) => Number(item))
        const allObject: {
            [x: number]: {
                [x: number]: number;
            };
        } = {}
        sectionKeys.forEach((key) => {
            const ids = (sectionData?.[value] || {})[key].map((item) => Number(item.id))
            const pasteObject = handlePaste(copyObject, { [key]: ids })
            if (pasteObject) allObject[key] = pasteObject
        })
        setExportValue(allObject)
    }

    const handleCopy = (skipSetState: boolean = false) => {
        const key = Object.keys(selectedTime)?.[0]
        const target = Object.values(selectedTime)?.[0]
        // skip if no copy
        if (!target) return;
        const numKey = Number(key)
        const startTimes = getStartTimes(numKey)
        const copyObject: { [startTime: number]: number } = {}

        target.forEach((targetId) => {
            const found = startTimes.find((item) => item.id === targetId)
            if (!found) return
            const quantity = exportValue?.[numKey]?.[targetId] ?? 0
            copyObject[found?.startTime] = quantity || 0
        })

        if (skipSetState) return copyObject

        setCopy(copyObject)
    }

    const handlePaste = (
        manualCopy?: { [startTime: number]: number; },
        manualSelect?: { [x: number]: number[] }
    ) => {
        const key = Object.keys(manualSelect || selectedTime)?.[0]
        const target = Object.values(manualSelect || selectedTime)?.[0]
        const copyKey = Object.keys(manualCopy || copy)
        // skip if no copy
        if (!target) return;
        const numKey = Number(key)
        const startTimes = getStartTimes(numKey)

        const pasteObject: { [id: number]: number } = {}
        
        copyKey.forEach((key: string) => {
            const numCopyKey = Number(key)
            const found = startTimes.find((item) => item.startTime === numCopyKey)
            if (!found || !found.id) return
            const quantity = (manualCopy || copy)[numCopyKey]
            pasteObject[found.id] = quantity || 0
        })

        if (manualCopy && manualSelect) return pasteObject
        setExportValue({
            ...exportValue,
            [key]: pasteObject
        })
    }

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                accessorKey: "id",
                header: "",
                cell: (data) => (
                    <Box sx={{ display: 'flex', alignItems: 'center'}}>
                        <Checkbox
                            value={data.row.original.id}
                            checked={
                                (data.row.original.info && selectedTime?.[data.row.original.id]) ||
                                selectedTime?.[data.row.original.timestamp]?.includes(data.row.original.id)
                            }
                            onChange={(event) => 
                                handleSelected(event.target.checked, data.row.original.id, data.row.original.timestamp)
                            }
                            aria-label={`event-id-${data.row.original.id}-actions`}
                        />
                        {
                            data.row.original.info && (
                                <Image
                                    src={
                                        data.row.getIsExpanded()? 
                                        `/images/icon-arrowUp.svg`
                                        :`/images/icon-arrowDown.svg`
                                    }
                                    width={16}
                                    height={16}
                                    alt='expands'
                                    onClick={() => {
                                        const expanded = data.row.getIsExpanded()
                                        data.row.toggleExpanded(!expanded)
                                    }}
                                />
                            )
                        }
                    </Box>
                ),
            },
            {
                accessorKey: "id",
                header: tTicket("available_day"),
                cell: (data) => (
                    <>
                        {
                            data.row.original?.info?
                            dayjs.unix(data.row.original.id).format("YYYY/M/D"):
                            ''
                        }
                    </>
                )
            },
            {
                accessorKey: "time",
                header: tTicket("time_slot"),
                cell: (data) => {
                    const txtArr: string[] = []
                    if (data.row.original.start_time) {
                        const {
                            hour,
                            min
                        } = generateDisplayableTime(data.row.original.start_time, true)
                        txtArr.push(`${hour}:${min}`)
                    }
                    if (data.row.original.end_time) {
                        const {
                            hour,
                            min
                        } = generateDisplayableTime(data.row.original.end_time, true)
                        txtArr.push(`${hour}:${min}`)
                    }
                    return (
                        <>
                            { txtArr.join(" - ") }
                        </>
                    )
                }
            },
            {
                accessorKey: "ticket_date_inventory",
                header: tTicket("total_qty"),
                cell: (data) => (
                    <>
                        {
                            calAvailable(data.row.original)
                        }
                        {
                            data.row.original?.ticket_date_inventory?.reduce(
                                (acc: number, { total }: { total: number}) => acc + total, 0
                            )
                        }
                    </>
                )
            },
            {
                accessorKey: "ticket_date_inventory",
                header: tTicket("available"),
                cell: (data) => (
                    <>
                        {
                            calAvailable(data.row.original)
                        }
                        {
                            data.row.original?.ticket_date_inventory?.reduce(
                                (acc: number, { available }: { available: number}) => acc + available, 0
                            )
                        }
                    </>
                )
            },
            {
                accessorKey: "export",
                header: `${tTicket("export_to")} ${thirdParty?.name || ""}`,
                cell: (data) => (
                    <>
                        {
                            !data.row.original.info && (
                                <TextField
                                    key={
                                        `export_${
                                            data.row.original.date ||
                                            data.row.original.timestamp
                                        }_${data.row.original.id}`
                                    }
                                    type="number"
                                    defaultValue={exportValue?.[
                                        data.row.original.date ||
                                        data.row.original.timestamp
                                    ]?.[data.row.original.id] ?? 0}
                                    onBlur={(event) => {
                                        const {
                                            value
                                        } = event.target
                                        const numValue = Number(value)
                                        const {
                                            timestamp,
                                            date,
                                            id,
                                        } = data.row.original
                                        if (numValue > data.row.original.available) {
                                            return
                                        }
                                        handleExportOptions(
                                            date || timestamp,
                                            id,
                                            numValue > data.row.original.available? data.row.original.available : Number(numValue)
                                        )
                                    }}
                                />
                            )
                        }
                        {
                            data.row.original.info && (
                                <>
                                    {
                                        Object.values(exportValue?.[data.row.original.id]?? {}).reduce(
                                            (acc, cur) =>  acc + cur.valueOf(),0
                                        )
                                    }
                                </>
                            )
                        }
                    </>
                )
            },
            {
                accessorKey: "export_after",
                header: tTicket("avaiable_after_export"),
                cell: (data) => (
                    <>
                        {
                            !data.row.original.info && (
                                <>
                                    {
                                        data.row.original.available - 
                                        (exportValue?.[
                                            data.row.original.date ||
                                            data.row.original.timestamp
                                        ]?.[data.row.original.id] ?? 0) * multiply
                                    }
                                </>
                            )
                        }
                        {
                            data.row.original.info && (
                                <>
                                    {
                                        calAvailable(data.row.original) -
                                        Object.values(exportValue?.[data.row.original.id]?? {}).reduce(
                                            (acc, cur) =>  acc + cur.valueOf(), 0
                                        ) * multiply
                                    }
                                </>
                            )
                        }
                    </>
                )
            },
        ],
        [
            exportValue,
            selectedTime,
            thirdParty,
            handleExportOptions,
            handleSelected,
            multiply,
            tTicket
        ]
    );

    const defaultData = useMemo<any[]>(() => [], []);
    const table = useReactTable({
        data: sectionData?.[value]?
            Object.keys(sectionData[value]).map((key: any) => ({ info: sectionData[value][key], id: key}))
            : defaultData,
        columns,
        getSubRows: (row) => {
            return row.info
        },
        state: {
            expanded: expanded, // must pass expanded state back to the table
        },
        getCoreRowModel: getCoreRowModel(),
        getExpandedRowModel: getExpandedRowModel(),
        onExpandedChange: setExpanded,
        manualPagination: true,
    }); 

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const renderButtonGroup = () => (
        <Box
            sx={{
                display: 'flex',
                gap: '12px'
            }}
        >
            <Box
                sx={{ padding: "15px 20px", border: `2px solid ${COLORS.GREY_3}`, borderRadius: '999px'}}
                display={Object.values(selectedTime)?.[0]? undefined: 'none'}
                onClick={() => setSelectedTime({})}
            >
                <Typography variant='body2' fontWeight='700'>
                    {`${tTicket("selected")} ${Object.values(selectedTime)?.[0]?.length ?? ""}`}
                </Typography>
            </Box>
            <Box
                sx={{ padding: "15px 20px", border: `2px solid ${COLORS.GREY_3}`, borderRadius: '999px'}}
                display={Object.values(selectedTime)?.[0]? undefined: 'none'}
                onClick={handleCopyToAll}
            >
                <Typography variant='body2' fontWeight='700'>
                    {tTicket("copy_all")}
                </Typography>
            </Box>
            <Box
                sx={{ padding: "15px 20px", border: `2px solid ${COLORS.GREY_3}`, borderRadius: '999px'}}
                onClick={() => handleCopy()}
                display={
                    Object.values(selectedTime)?.[0]? undefined: 'none'
                }
            >
                <Typography variant='body2' fontWeight='700'>
                    {tTicket("copy")}
                </Typography>
            </Box>
            <Box 
                sx={{ padding: "15px 20px", border: `2px solid ${COLORS.GREY_3}`, borderRadius: '999px'}}
                display={
                    Object.values(copy)?.[0] !== undefined && Object.values(selectedTime)?.[0]
                    ? undefined: 'none'
                }
                onClick={() => handlePaste()}
            >
                <Typography variant='body2' fontWeight='700'>
                    {tTicket("paste")}
                </Typography>
            </Box>
        </Box>
    )

    const renderTab = () => (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '32px'
            }}
        >
            <Box 
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                }}
            >
                <Tabs value={value} onChange={handleChange}>
                    {
                        TabContent().filter(({ label, value}) => sectionFilter.includes(value))
                            .map(({ label, value }, index) => (
                            <Tab
                                key={label}
                                label={label}
                                value={value}
                                {...a11yProps(index)}
                            />
                        ))
                    }
                </Tabs>
                <PaginationTable
                    table={table}
                    isLoading={isLoading}
                    msg={t("title_empty")}
                    skipPagination={true}
                />
            </Box>
        </Box>
    )

    const handleClose = () => {
        router.back()
    }

    const mutation = useMutation({
        mutationFn: async () => {
            const allTicketDate = ticketSection.map((section: TicketSection) => {
                const {
                    id,
                    date,
                    ticket_date_inventory = []
                } = section

                return ticket_date_inventory.map((inventory) => ({
                    ...inventory,
                    sectionId: id,
                    date
                }))
            }).flat()
            const updateObject: {
                ticketTypeId: number,
                ticketDateInventoryId: number,
                ticketSectionId: number,
                quantity: number
            }[] = []
            Object.keys(exportValue).map((key) => {
                const numKey = Number(key)
                const dateKeys = Object.keys(exportValue[numKey])
                dateKeys.map((key) => {
                    const numDateKey = Number(key)
                    const found = allTicketDate.find(({ date, timestamp, sectionId }: {
                        date?: number, timestamp?: number, sectionId?: number
                    }) => {
                        return (timestamp === numKey || date === numKey)
                            && numDateKey === sectionId
                    })
                    updateObject.push({
                        ticketTypeId: Number(ticketTypeId),
                        ticketDateInventoryId: found.id,
                        ticketSectionId: numDateKey,
                        quantity: exportValue[numKey][numDateKey]
                    })
                })
            })
            const res = await xior.post('/api/ticket/export', {branch: updateObject})
            const {
                filePath = '',
                name = ''
            } = res?.data?.data
            var link = document.createElement("a");
            link.download = name;
            link.href = filePath;
            link.click();
            return;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["event"]});
            // router.back()
        },
    });

    const handleUpdate = () => {
        mutation.mutate()
    }

    return(
        <Container>
            <Typography variant='h3' fontWeight='bold'>
                {`${tTicket("export_to")} ${thirdParty?.name ?? ""}`}
            </Typography>
            
            <Box sx={{ display: 'flex', gap: '32px', flexDirection: 'row' }}>
                <Box sx={{ display: 'flex', flexGrow: 1, flexDirection: 'column', gap: '16px' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body1" fontWeight="bold">
                            {tTicket('choose_ticket_type')}
                        </Typography>
                        <Select
                            data={availableType.map((type: { id: number, name: string }) => ({ value: type.id, label: type.name }))}
                            label={tTicket('choose_ticket_type')}
                            fullWidth={false}
                            sx={{ width: '100%', maxWidth: '360px' }}
                            handleOnChange={(val) => setTicketTypeId(val)}
                        />
                    </Box>

                    {
                        ticketTypeId && (
                            <Card 
                                sx={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '24px',
                                    padding: '20px',
                                    borderRadius: '16px'
                                }}
                            >
                                {renderTab()}
                            </Card>
                        )
                    }
                    
                    {ticketTypeId && renderButtonGroup()}
                </Box>
                

                <Box sx={{ display: 'flex', flexDirection: 'column', width: '30%', maxWidth: '320px', gap: '32px' }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                        <CancelButton disabled={mutation.isPending} onAction={handleClose} />
                        <UpdateButton disabled={mutation.isPending} onAction={handleUpdate} />
                    </Box>
                </Box>
            </Box>
        </Container>
    )
}

export default ThirdPartyExport;