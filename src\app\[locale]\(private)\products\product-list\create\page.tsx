"use client";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import PageHeader from "@/components/PageHeader";
import { ROUTES } from "@/utils/constants";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Information from "./Information";
import Selling from "./Selling";
import {IProduct} from "@/interface/IProduct";
import xior from "xior";
import {showErrorPopUp} from "@/utils/toast";

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
  }
  
  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;
  
    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
      </div>
    );
  }
  
  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }

const CreateProductPage = () => {
    const [value, setValue] = React.useState(0);
    const [addProductDto,setAddProductDto] = useState<IProduct>({
      name:'',
      description:'',
      thumbnail:'',
      audio:'',
      owner: '',
      roomExcluded:"",
      productType:1,
      status:1,
      type:1,
      currency:"HKD",
      price:0,
      compareAt:0,
      weight:0,
      weightUnit:"",
      openToAllMembers:false,
      category:0,
      taxIncluded: false,
      tax:0,
      quantity:0,
      productImage: [],
      tags: [],
      collections: [],
      memberGroup: []
    });
    const {showToast} = showErrorPopUp() 

    console.log("Check add product",addProductDto)
    const handleAddProductDtoChange = (addProductDto:IProduct) =>{
      setAddProductDto(addProductDto)
    }

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
      setValue(newValue);
    };
    const t = useTranslations("product");
    const router = useRouter();

    const handleAddProductApi = async () =>{
      try{

        const response = await xior.post('/api/products',addProductDto,{
          headers: {
            'Accept-Language': 'en' 
        }
        });

        console.log("add product data", response.data)
        showToast(`${addProductDto.name} created successfully`);
        router.push(ROUTES.PRODUCT_LIST); 
      }catch(error){
        console.log('call add product api error',error)
        showToast("create product error, please try again.");
      }
    
    }

    // const handleSaveAndRedirect = async () => {
    //   try {
    //     await handleAddProductApi(); 
    //     //router.push(ROUTES.PRODUCT_LIST); 
    //   } catch (error) {
    //     console.error("Error adding product:", error);

    //   }
    // };

    return(
        <>
        <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
            <PageHeader title={`${t("label_title")} > ${t("label_product_create")}`}>
                <>
                    <CancelButton
                        onAction={() => router.push(ROUTES.PRODUCT_LIST)}
                    />
                    <SaveButton onAction={handleAddProductApi}/>
                </>
            </PageHeader>
            <Box flex={1} padding="26px 34px">
            <Box sx={{ width: '100%' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
                <Tab label="INFORMATION" {...a11yProps(0)} />
                <Tab label="SELLING" {...a11yProps(1)} />
                </Tabs>
            </Box>
            <CustomTabPanel value={value} index={0}>
               <Information 
               addProductDto={addProductDto} 
               handleAddProductDtoChange={handleAddProductDtoChange}/>
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
                <Selling
                addProductDto={addProductDto}
                handleAddProductDtoChange={handleAddProductDtoChange}
                />
            </CustomTabPanel>
            </Box>
            </Box>
        </Box>
        </>
    )
}

export default CreateProductPage;