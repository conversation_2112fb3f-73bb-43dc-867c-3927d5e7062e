import React from "react";
import {
  Box,
  Select,
  SelectChangeEvent,
  Typography,
  MenuItem,
} from "@mui/material";
import ExpandMoreRoundedIcon from "@mui/icons-material/ExpandMoreRounded";

interface Props {
  value: number;
  onChange: (value: number) => void;
}

const PageSelector = ({ value, onChange }: Props) => {
  return (
    <Box display={"flex"} flexDirection={"row"} alignItems={"center"}>
      <Typography fontSize={13} fontWeight={400}>
        Show per page:
      </Typography>
      <Select
        size="small"
        sx={{ mx: 2, minWidth: '80px', textAlign:'center', fontSize: '13px', fontWeight:400  }}
        IconComponent={ExpandMoreRoundedIcon}
        value={value.toString()}
        onChange={(event: SelectChangeEvent) => {
          onChange(parseInt(event.target.value));
        }}
      >
        <MenuItem value={10}>10</MenuItem>
        <MenuItem value={15}>15</MenuItem>
        <MenuItem value={20}>20</MenuItem>
        <MenuItem value={25}>25</MenuItem>
        <MenuItem value={30}>30</MenuItem>
      </Select>
    </Box>
  );
};

export default PageSelector;
