import React from "react";
import { Button, ButtonProps } from "@mui/material";
import { useTranslations } from "next-intl";
import ExcelIcon from "../icons/ExcelIcon";
import { ButtonHeadTable } from "./styled";

const ExportButton = (props: ButtonProps) => {
    const t = useTranslations("common");

    return (
        <ButtonHeadTable
            variant="contained"
            endIcon={<ExcelIcon sx={{ height: 16, width: 16 }} />}
            {...props}
        >
            {t("button_export_to_excel")}
        </ButtonHeadTable>
    );
};

export default ExportButton;
