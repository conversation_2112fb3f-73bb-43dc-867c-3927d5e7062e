"use client";
import * as React from "react";
import { Autocomplete, MenuItem, Select as MuiSelect, SelectProps, TextField } from "@mui/material";
import { useTranslations } from "next-intl";
import InputContainer, { InputContainerProps } from "./InputContainer";
import { AVAILABLE_DAY } from "@/utils/constants";
import Image from "next/image";

interface Props extends InputContainerProps {
  value: (number|string|undefined)[];
  customChange: (val: any) => void;
  removeDay: () => void;
  options: {label: string, value: number}[];
  seq: number;
}

export default function SelectDay({
  label,
  description,
  placeholder,
  variant = "outlined",
  fullWidth = true,
  value = [],
  options = [],
  error,
  required,
  multiple = false,
  customChange,
  removeDay,
  seq,
  ...otherProps
}: Props & Omit<SelectProps, "error">) {
  const t = useTranslations("error");
  const [selected, setSelected] = React.useState<{ label: string, value: string|number }[]>([])

  React.useEffect(() => {
    if (!selected) return
    customChange(selected.map(item => item.value))
  }, [selected, customChange])

  const handleOnChange = (event: any, data: any) => {
    setSelected(data)
  }

  React.useEffect(() => {
    if (!value) return;
    const found = AVAILABLE_DAY.filter((item) => value.includes(item.value))
    setSelected(found)
  }, [value])

  return (
    <InputContainer
      required={required}
      blockStyle={{ display: 'flex', flexDirection: 'row', gap: '12px' }}
    >
        <Autocomplete
            multiple
            id="tags-outlined"
            options={options}
            getOptionLabel={(option: any) => option.label}
            filterSelectedOptions
            renderInput={(params: any) => (
                <TextField
                    {...params}
                    label={label}
                    placeholder={placeholder}
                />
            )}
            value={selected}
            onChange={handleOnChange}
            sx={{ flex: 1 }}
        />
        <Image
          src="/images/icon-delete.svg"
          width={48}
          height={48}
          alt={`delete?`}
          onClick={() => removeDay()}
        />
    </InputContainer>
  );
}
