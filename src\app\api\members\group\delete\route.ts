import { IMember } from "@/interface/IMember";
import {
  authorizedPost,
  authorizedDelete,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";


async function POST(req: NextRequest) {
  try {
    const addMemberToGroupData = await req.json();
    const data = await authorizedPost(
      "/member/group/delete",
      await getAuthHeaders(req),
      addMemberToGroupData
    );
    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { POST };
