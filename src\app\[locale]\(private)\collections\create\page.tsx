"use client";
import PageHeader from "@/components/PageHeader";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import FileInput from "@/components/input/FileInput";
import TextField from "@/components/input/TextField";
import useFileUpload from "@/hooks/useFileUpload";
import { CollectionSchema } from "@/schemas/CollectionSchema";
import { ROUTES } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";

interface Props {
  params: { id: string };
}

type FormValue = z.infer<typeof CollectionSchema>;
const placeholderStyle = { fontSize: "10px", fontWeight: 300, color: "#777" };

const CreateCollection = ({ params: { id } }: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const router = useRouter();
  const { uploadFile } = useFileUpload();

  const {
    handleSubmit,
    control,
    formState: { isDirty, isSubmitting, errors },
    setError,
  } = useForm<FormValue>({
    resolver: zodResolver(CollectionSchema),
    defaultValues: {
      name: "",
      description: "",
      photoUrl: "",
    },
  });

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const { photoUrl, ...otherData } = data;
      const payload = {
        ...otherData,
        photoUrl: await uploadFile(photoUrl, "collection"),
      };
      await xior.post(`/api/collections`, {
        ...payload,
      });
      queryClient.invalidateQueries({ queryKey: ["collections"] });
      router.push(ROUTES.COLLECTION);
    } catch (e) {
      setError("name", { message: "unknown_error" });
    }
  };

  return (
    <Box
      sx={{ height: "100%" }}
      display={"flex"}
      flexDirection={"column"}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
    >
      <PageHeader
        title={[
          t("collection.label_title"),
          t("collection.title_create_collection"),
        ]}
      >
        <>
          <CancelButton onAction={() => router.push(ROUTES.COLLECTION)} />
          <SaveButton disabled={isSubmitting || !isDirty} />
        </>
      </PageHeader>
      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
        maxWidth={450}
      >
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              placeholder={t("collection.label_collection_name")}
              required
              disabled={isSubmitting}
              label={t("collection.label_collection_name")}
              error={errors?.name?.message}
              labelStyle={{ fontSize: "13px", fontWeight: 700 }}
              blockStyle={{ marginBottom: "0.5rem" }}
              sx={{ "& input::placeholder": placeholderStyle }}
            />
          )}
        />
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("collection.label_description")}
              labelStyle={{ fontSize: "13px", fontWeight: 700 }}
              placeholder={t("collection.placeholder_description")}
              error={errors?.description?.message}
              multiline
              rows={8}
              sx={{ "& textarea::placeholder": placeholderStyle }}
            />
          )}
        />
        <Controller
          name="photoUrl"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("collection.label_thumbnail")}
              </Typography>
              <Typography fontSize={12} color={'grey'}>
                <i>
                  {
                    t("file_upload.image_metadata").split("\n").map((item: string) => (
                      <>{item}<br /></>
                    ))
                  }
                </i>
              </Typography>
              <FileInput
                value={field.value}
                onChange={field.onChange}
                labelStyle={{ fontSize: "13px", fontWeight: 700 }}
                metadata={t("file_upload.thumbnail_upload")}
                disabled={isSubmitting}
                error={errors.photoUrl?.message}
                type="image"
              />
            </>
          )}
        />
      </Box>
    </Box>
  );
};

export default CreateCollection;
