import GoogleButton from "@/components/buttons/GoogleButton";
import { TokenResponse, useGoogleLogin } from "@react-oauth/google"
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import xior from "xior";

type GoogleLoginProps = {
    setLoginError?: Function
}

export const GoogleLoginButton = (props: GoogleLoginProps) => {
    const router = useRouter();

    const login = useGoogleLogin({
        onSuccess: (codeResponse) => handleLogin(codeResponse),
        onError: (error) => console.log('Login Failed:', error)
    });

    const handleLogin = async (user: TokenResponse) => {
        try {
            const loginInfo = await xior
            .post("/api/auth/social-media/google", { token: user.access_token })
            const res = await signIn("credentials", { ...loginInfo.data, type: "google", redirect: false })
            if (res?.error) throw Error("invalidCredentials");
            router.push("/");
        } catch(err) {
            if (props.setLoginError) props.setLoginError()
        }

    }

    return (
        <GoogleButton
            sx={{width: 30, height: 60, borderRadius: 30, border: '1px solid grey'}}
            onClick={() => login()}
            fullWidth
            variant="outlined"
        />
    )
}