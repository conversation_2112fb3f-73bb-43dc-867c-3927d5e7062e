"use client";
import * as React from "react";
import { Box, Button, Modal, TextField, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { ICollection } from "@/interface/ICollection";
import { useRouter } from "next/navigation";
import { CURRENCY, LANGUAGE_CODE, ROUTES, SALES_THRESHOLD, SUPPORT_CURRENCY } from "@/utils/constants";
import EditButton from "@/components/buttons/EditButton";
import TextEditor from "@/components/RichEditor";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";
import { DatePicker, DateTimePicker } from "@mui/x-date-pickers";
import Select from "@/components/input/Select";
import SelectWithAdd from "@/components/input/SelectWithAdd";
import Image from "next/image";

interface Props {
    id: number,
    language: LANGUAGE_CODE
}

type Variation = {
    value: string;
    extra?: { id: number }
}

const AddTicketVariation = ({ 
    id,
    language
}: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const t = useTranslations("ticket");
  const tCommon = useTranslations("common");
  const [open, setOpen] = React.useState(false);
  const [ticketVariation, setTicketVariation] = React.useState<{
    name?: string,
    ticketVariationOption?: Variation[],
  }>({});

  const mutation = useMutation({
    mutationFn: () => {
        const clone = { 
            ticketSettingId: id,
            name: ticketVariation.name,
            value: (ticketVariation.ticketVariationOption || []).map((option) => option.value),
            language
        }
        return xior.post("/api/ticket/variation", clone)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["event"]});
      setOpen(false);
    },
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (mutation.isPending) return;
    setOpen(false);
  }, [mutation.isPending]);

  const handleUpdate = () => {
    mutation.mutate()
  }

  const handleOnChange = (key: 'name'|'ticketVariationOption', value: any) => {
    setTicketVariation({
        ...ticketVariation,
        [key]: value
    })
  }

  return (
    <Box>
      <EditButton label={t("add_variation")} isPrimay={false} onClick={handleOpen} isSmall={true} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer sx={{ padding: '40px', gap: '32px' }}>
            <Typography variant="h3">{t("add_variation")}</Typography>

            <TextField
                label={t("variation_label")}
                value={ticketVariation.name}
                onChange={(event) => handleOnChange("name", event.target.value)}
            />

            <SelectWithAdd
                data={[]}
                label={t("values_with_remind")}
                placeholder={tCommon("input_placeholder")}
                customChange={(value) => handleOnChange("ticketVariationOption", value)}
            />

            <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginTop: "30px" }}>
                <CancelButton onAction={handleClose} />
                <UpdateButton onAction={handleUpdate} label={tCommon("button_save")}/>
            </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default AddTicketVariation;
