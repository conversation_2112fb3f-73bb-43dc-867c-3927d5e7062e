export interface ClientOrderDetails {
    orderDetails: OrderDetail[];
}

export interface OrderDetail {
    orderId:           number;
    orderNumber:       string;
    deliveryStatus:    null | string;
    transactionStatus: string;
    deliveryDetails:   DeliveryDetails;
    promoCode:         PromoCode;
    currency:          string;
    totalPrice:        string;
    paymentMethod:     null | string;
    tax:               null;
    billingAddress:    Address;
    deliveryAddress:   Address;
    productId:         number;
    productName:       string;
    thumbnailUrl:      string;
    productType:       number;
    price:             string;
    quantity:          number;
}

export interface Address {
    name:         string;
    phoneNumber:  string;
    addressLine1: null | string;
    addressLine2: null | string;
    country:      null | string;
    postalCode:   null | string;
}

export interface DeliveryDetails {
    orderedTime:         number;
    pendingDeliveryTime: number;
    paidTime:            number;
    deliveredTime:       number | null;
}

export interface PromoCode {
    code:     null;
    discount: null;
}
