"use client";
import * as React from 'react';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import PersonDetails from './PersonalDetails';
import {PeronDetialsDto} from "@/interface/IPersonDetails"
import Shipping from './Shipping';
import Payment from './Payment';
import { Grid, IconButton, styled } from '@mui/material';
import {ChekoutDto} from "@/interface/ILatestCheckOutResult"
import { useEffect, useState } from 'react';
import {ProductDetailDto} from "@/interface/IProductDetailDto"
import { IPaymentCreateDto } from '@/interface/IPaymentGateway';
import xior from "xior";
import QRCode from "react-qr-code";
import Modal from '@mui/material/Modal';
import { ICheckOutDto } from "@/interface/ICheckOutDto";
import ErrorIcon from '@mui/icons-material/Error';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

type Props = {
  paymentCreateDto: IPaymentCreateDto;
  handlePaymentDto:(paymentCreateDto:IPaymentCreateDto)=> void
  checkoutDto: ICheckOutDto
  finalAmount:number
  getPromoCode:string
}

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};
 
const CheckoutStepper = ({paymentCreateDto,handlePaymentDto,finalAmount,getPromoCode,checkoutDto}:Props) =>{

    // const steps = ['Personal Details', 'Shipping', 'Payment'];
    const router = useRouter()
    const [activeStep, setActiveStep] = React.useState(0);
    const [skipped, setSkipped] = React.useState(new Set<number>());
    const [steps, setSteps] = React.useState(['Personal Details', 'Shipping', 'Payment']);
    const [peronDetialsDto,setPeronDetialsDto] = React.useState<PeronDetialsDto>({
      name: '',
      areaCode: '',
      PhoneNum: null,
      email: ''
    })
    const [productDetailDto, setProductDetailDto] = useState<ProductDetailDto | undefined>(undefined);
    const [paymentReturnUrl, setPaymentReturnUrl] = useState<string>('')
    const [qrCodeSrc,setQrCodeSrc]= useState<string>('')
    const [open, setOpen] = React.useState(false);
    const [isPaying,setIsPaying] = useState<boolean>(false);
    const [orderNo, setOrderNo] = useState('');
    const [orderListener, setOrderListener] = useState<WebSocket|undefined>();

    useEffect(() => {
      return () => setOrderNo('')
    }, [])

    const openOrderListener = React.useCallback(() => {
      if (!orderNo) {
        console.error("Cant find order id")
        return;
      }
      const ws = new WebSocket(`${process.env.WEBSOCKET_URL}/order?id=${orderNo}`)
      setOrderListener(ws)
      ws.onopen = () => console.log(`listening to order: ${orderNo} response`)
      ws.onmessage = (event) => {
        console.info(`message from ${orderNo} >> ${event.data}`)
        try {
          let data = JSON.parse(event.data)
          if (data.success) {
            router.push(`/checkout/thankyou?merchant_order_no=${orderNo}`)
          }
        } catch(error) {
          console.error("[listener formet error] >> " + error)
        }
      }
    }, [orderNo, router])

    const closeOrderListener = React.useCallback(() => {
      if (!orderListener) {
        console.error("web socket not opened")
        return;
      }
      orderListener.close()
      orderListener.onclose = () => console.info(`order (${orderNo}) listener closed`)
    }, [orderListener, orderNo])

    useEffect(() => {
      if (orderNo && !orderListener) {
        openOrderListener()
      } else if (orderListener) {
        closeOrderListener()
      }
    }, [orderNo, orderListener, openOrderListener, closeOrderListener])

    const handlePaymentBtnClose = () => setOpen(false);
    const handlePaymentBtnOpen = () =>{
      setOpen(true)
    }
    const [errorOpen, setErrorOpen] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');

    const handleErrorOpen = () => setErrorOpen(true);
    const handleErrorClose = () => setErrorOpen(false);
    // const [isMobile,setIsMobile] = useState<string>('')
    const isMobile = (): boolean => {
      const userAgent = navigator.userAgent || navigator.vendor;
  
      // 檢查用戶代理字符串是否包含移動設備的標識符
      return /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
  };
  // 使用範例
  if (isMobile()) {
      console.log("使用的是移動設備");
  } else {
      console.log("使用的是桌面瀏覽器");
  }
    // const generate = () =>{
    //   QRCode.toDataURL.
    // }
    // console.log("payment log", paymentReturnUrl)
    const fetchProductDetails = React.useCallback(async () =>{

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/products/${checkoutDto.order.product.id}`,{
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
      }, 
      });
      const data = await response.json();
      setProductDetailDto(data);
    }, [ checkoutDto ])

    const renderPopupError = () => {
      return (
          <Modal
              open={errorOpen}
              onClose={handleErrorClose}
              aria-labelledby="modal-modal-title"
              aria-describedby="modal-modal-description"
          >
              <Box sx={style}>
              <   Box sx={{ display: 'flex', justifyContent: 'center'}}>
                  <IconButton style={{ color: 'rgba(235, 87, 87, 1)'}}>
                    <ErrorIcon style={{ fontSize: '35px' }}/>
                  </IconButton>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'center'}}>
                  <Typography id="modal-modal-title" variant="h2" component="h2">
                      Oops...
                  </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'center'}}>
                  <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                      {errorMessage}
                  </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <Button onClick={handleErrorClose} variant="contained">Back</Button>
                  </Box>
              </Box>
          </Modal>
      );
  };

    const handleAddPersonDetialsChange = (peronDetialsDto:PeronDetialsDto) =>{
      setPeronDetialsDto(peronDetialsDto)
    }
  
    const isStepOptional = (step: number) => {
      return step === 1;
    };
  
    const isStepSkipped = (step: number) => {
      return skipped.has(step);
    };
  
    const handleNext = () => {
      let newSkipped = skipped;
      if (isStepSkipped(activeStep)) {
        newSkipped = new Set(newSkipped.values());
        newSkipped.delete(activeStep);
      }
  
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      setSkipped(newSkipped);
    };
  
    const handleBack = () => {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const renderBackButton = () =>{
      if (productDetailDto?.productType === 2) {
        setActiveStep((prevActiveStep) => prevActiveStep - 2);
      }else if(productDetailDto?.productType === 1){
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
      }
    }
  
    const handleSkip = () => {
      if (!isStepOptional(activeStep)) {
        // You probably want to guard against something like this,
        // it should never occur unless someone's actively trying to break something.
        throw new Error("You can't skip a step that isn't optional.");
      }
  
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      setSkipped((prevSkipped) => {
        const newSkipped = new Set(prevSkipped.values());
        newSkipped.add(activeStep);
        return newSkipped;
      });
    };

    const Item = styled('div')(({ theme }) => ({
      backgroundColor: '#fff',
      ...theme.typography.body2,
      padding: theme.spacing(1),
      textAlign: 'center',
      color: theme.palette.text.secondary,
      ...theme.applyStyles('dark', {
        backgroundColor: '#1A2027',
      }),
    }));


    const pid = productDetailDto?.id;
    const quantity = checkoutDto?.order.quantity;
    const price = productDetailDto?.price
    const freeType = paymentCreateDto.feeType;
    const paymentType = paymentCreateDto.paymentType;
    const paymentPlatform = paymentCreateDto.paymentPlatform;
    const totalFee = quantity * Number(price);
    // const totalFee = finalAmount;
    const description = productDetailDto?.description;
    const clientName = paymentCreateDto.clientName;
    const clientEmail = checkoutDto.order.user.email;
    const clientContactNumber = paymentCreateDto.clientContactNumber;
    const billingAddressCountry = paymentCreateDto.billingAddressCountry;
    const billingAddress1 = paymentCreateDto.billingAddress[0];
    const billingAddress2 = paymentCreateDto.billingAddress[1];
    const billingAddressPostalCode = paymentCreateDto.billingAddressPostalCode;
    const deliveryAddressCountry = paymentCreateDto.deliveryAddressCountry;
    const deliveryAddress1 = paymentCreateDto.deliveryAddress?.[0];
    const deliveryAddress2 = paymentCreateDto.deliveryAddress?.[1];
    const deliveryAddressPostalCode = paymentCreateDto.deliveryAddressPostalCode;
    const promoCode = getPromoCode;
    const hasPhysical = productDetailDto?.isDigital;

    const logObject = {
      pid,
      quantity,
      price,
      freeType,
      paymentType,
      paymentPlatform,
      totalFee,
      description,
      clientName,
      clientEmail,
      clientContactNumber,
      billingAddress: {
          country: billingAddressCountry,
          address1: billingAddress1,
          address2: billingAddress2,
          postalCode: billingAddressPostalCode,
      },
      deliveryAddress: {
          country: deliveryAddressCountry,
          address1: deliveryAddress1,
          address2: deliveryAddress2,
          postalCode: deliveryAddressPostalCode,
      },
      promoCode,
      hasPhysical
  };
  
  console.log(logObject);


    console.log("check final totalFee",totalFee)
    console.log("check final promoCode",getPromoCode)



    const callPaymentsAPI = async () => {
      const productDetailDto = {
        product: [
          { id: pid, quantity: quantity }
        ],
        feeType: freeType, 
        paymentType:paymentType,
        paymentPlatform:paymentPlatform,
        totalFee:totalFee,
        description:description,
        clientName:clientName,
        clientEmail:clientEmail,
        clientContactNumber:clientContactNumber,
        billingAddressCountry:billingAddressCountry,
        billingAddress:[
          billingAddress1,billingAddress2
        ],
        billingAddressPostalCode:billingAddressPostalCode,
        deliveryAddressCountry:deliveryAddressCountry,
        deliveryAddress:[
          deliveryAddress1,deliveryAddress2
        ],
        deliveryAddressPostalCode:deliveryAddressPostalCode,
        promoCode:getPromoCode
      };
      
      try{
        setIsPaying(true)
        const response = await xior.post('/api/payment', productDetailDto, {
          headers: {
              'Accept-Language': 'en' 
          }
        });
        setIsPaying(false);
      if(paymentPlatform === 'Airwallex'){
        window.location.href = response.data.redirectUrl
      }else if(paymentPlatform === 'AliPay'){
        const alipayUrl = response.data.redirectUrl;
        if (isMobile()) {
          window.location.href = `alipayhk://platformapi/startapp?appId=20000067&url=${encodeURIComponent(alipayUrl)}`;
      }else{
        // open ws
        setOrderNo(response.data.orderNo)
        setPaymentReturnUrl(alipayUrl);
        handlePaymentBtnOpen();
      }
      }else if (paymentPlatform === 'WeChatPay') {
        const wechatUrl = response.data.redirectUrl; 
        const url = new URL(wechatUrl.replace("weixin://", "http://")); 
        const prePayId = url.searchParams.get("pr"); 
    
        if (isMobile()) {
            if (prePayId) {
                window.location.href = `weixin://wxpay/bizpayurl?pr=${prePayId}`;
            } else {
                console.error("Prepayment transaction code not found.");
                alert("Unable to proceed with payment, please try again.");
            }
        } else {
            // open ws
            setOrderNo(response.data.orderNo)
            setPaymentReturnUrl(wechatUrl);
            handlePaymentBtnOpen();
        }
    }
        console.log('Response Data:', response.data);
      }catch(error){
        setIsPaying(false);
        console.error('Error calling payment API:', error);
        setErrorMessage("An error occurred while processing your payment. Please try again."); // 設置錯誤信息
        handleErrorOpen(); // 打開模態框
      };
    }
    
    // const handleSubmit = async () => {
 
    //   switch (activeStep) {
    //     case 0:
    //       // Call API for Personal Details
    //       console.log("Submitting Personal Details");
    //       // console.log(peronDetialsDto);

    //       break;
    //     case 1:
    //       // Call API for Shipping
    //       console.log("Submitting Shipping Details");
    //       break;
    //     case 2:
    //       // Call API for Payment
    //       console.log("Submitting Payment");
    //       console.log(paymentCreateDto);
    //       break;
    //     default:
    //       break;
    //   }
    //   handleNext(); 
    // };

    const handlesetActiveStep = () =>{
      setActiveStep(1)
    }

    const renderQRcodePaymentText = () =>{
      if(paymentPlatform === 'AliPay'){
        return(
          <>
            <Image
              src="https://www.alipayhk.com/wp-content/uploads/2020/12/AlipayHK.png"
              alt="Other"
              style={{ width: '150px' }}
            />
          </>
        )
      }else if(paymentPlatform === 'WeChatPay'){
        return(
          <>
            <Image
              src="https://walkthechat.com/wp-content/uploads/2017/04/Screen-Shot-2017-04-11-at-6.34.16-PM.png"
              alt="Other"
              style={{ width: '150px'}}
            />
          </>
        )
      }
    }

    const renderActiveStepContent = () =>{
        if(activeStep === 0){
            return(
                <>
                { paymentCreateDto && checkoutDto && (
                  <PersonDetails peronDetialsDto={peronDetialsDto} 
                  handleAddPersonDetialsChange={handleAddPersonDetialsChange} 
                  paymentCreateDto={paymentCreateDto} 
                  handlePaymentDto={handlePaymentDto}
                  checkoutDto={checkoutDto}
                  handlesetActiveStep={handlesetActiveStep}
                  />
                )
                }
                <Box sx={{ flexGrow: 1 }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    {/* <Item>              <Button
                color="inherit"
                disabled={activeStep === 0}
                onClick={handleBack}
                sx={{ mr: 1 }}
              >
                Back
              </Button></Item> */}
                  </Grid>
                  <Grid item xs={6}>
                    {/* <Item><Button onClick={() => {{}}}>Shipping Details</Button></Item> */}
                  </Grid>
                </Grid>
              </Box>
                </>
            )
        }else if(activeStep === 1){

          if (productDetailDto?.productType === 2) {
            handleNext(); 
            return null; 
          }
      
            return(
                <>
                { paymentCreateDto &&
                <Shipping paymentCreateDto={paymentCreateDto} handlePaymentDto={handlePaymentDto}/>
                }
                <Box sx={{ flexGrow: 1 }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Item>              <Button
                color="inherit"
                onClick={handleBack}
                sx={{ mr: 1 }}
              >
                Back
              </Button></Item>
                  </Grid>
                  <Grid item xs={6}>
                    <Item><Button onClick={() => {setActiveStep(2)}} variant="contained">Go To Payment</Button></Item>
                  </Grid>
                </Grid>
              </Box>
                </>
            )
        }else if(activeStep === 2 ){
            return(
                <>
                {
                <Payment paymentCreateDto={paymentCreateDto} handlePaymentDto={handlePaymentDto}/>
                }

                <Box sx={{ flexGrow: 1 }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Item>              <Button
                color="inherit"
                onClick={renderBackButton}
                sx={{ mr: 1 }}
              >
                Back
              </Button></Item>
                  </Grid>
                  <Grid item xs={6}>
                    <Item><Button onClick={callPaymentsAPI}  disabled={isPaying} variant="contained">Pay Now</Button></Item>
                  </Grid>
                </Grid>
                {/* <Box style={{ marginTop: '20px' }}>
                {paymentReturnUrl && (
                    <QRCode value={paymentReturnUrl} />
                )}
                </Box> */}
                <Modal
                open={open}
                // onClose={handlePaymentBtnClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
              >
                <Box sx={style}>
                  <Typography id="modal-modal-title" variant="h6" component="h2">
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center'}}>
                  {
                    renderQRcodePaymentText()
                  }
                  </Box>
                  {paymentReturnUrl && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                      
                    <QRCode value={paymentReturnUrl} />
                    </Box>
                )}
                  <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                  </Typography>
                </Box>
              </Modal>
              {/* <Button onClick={handleOpen}>Open modal</Button> */}
              {renderPopupError()}
              </Box>
                </>
            )
        }
    }
  
    const handleReset = () => {
      setActiveStep(0);
    };

    useEffect(() => {
      fetchProductDetails();
    }, [totalFee, fetchProductDetails]);

    return(
        <Box sx={{ width: '100%' }}>
        <Stepper activeStep={activeStep}>
          {steps.map((label, index) => {
            const stepProps: { completed?: boolean } = {};
            const labelProps: {
              optional?: React.ReactNode;
            } = {};
            // if (isStepOptional(index)) {
            //   labelProps.optional = (
            //     <Typography variant="caption">Optional</Typography>
            //   );
            // }
            if (isStepSkipped(index)) {
              stepProps.completed = false;
            }
            return (
              <Step key={label} {...stepProps}>
                <StepLabel {...labelProps}>{label}</StepLabel>
              </Step>
            );
          })}
        </Stepper>
        {activeStep === steps.length ? (
          <React.Fragment>
            <Typography sx={{ mt: 2, mb: 1 }}>
              All steps completed - you&apos;re finished
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
              <Box sx={{ flex: '1 1 auto' }} />
              <Button onClick={handleReset}>Reset</Button>
            </Box>
          </React.Fragment>
        ) : (
          <React.Fragment>
            {
                renderActiveStepContent()
            }
            <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
              {/* <Button
                color="inherit"
                disabled={activeStep === 0}
                onClick={handleBack}
                sx={{ mr: 1 }}
              >
                Back
              </Button> */}
              <Box sx={{ flex: '1 1 auto' }} />
              {/* {isStepOptional(activeStep) && (
                <Button color="inherit" onClick={handleSkip} sx={{ mr: 1 }}>
                  Skip
                </Button>
              )} */}
              {/* <Button onClick={handleSubmit}>
                {activeStep === steps.length - 1 ? 'Pay Now' : 'Next'}
              </Button> */}
            </Box>
          </React.Fragment>
        )}
      </Box>
    )
}

export default CheckoutStepper;