import {
  authorizedGet,
  authorizedPut,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { CONTACTS_METHODS, SOCIAL_MEDIAS } from "@/utils/constants";
import { jwtDecode } from "jwt-decode";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";

async function GET(req: NextRequest) {
  try {
    const token = await getToken({ req });
    if (!token?.accessToken) throw Error("Unauthorized");
    const userId = jwtDecode(token.accessToken).sub!;
    const data = await authorizedGet<any>(
      `/users/${userId}`,
      await getAuthHeaders(req)
    );

    const user: Record<string, any> = {
      contacts: {},
      socialMedias: {},
    };

    for (const key in data) {
      switch (key) {
        case "address":
          user.addressLine1 = data.address?.[0] || "";
          user.addressLine2 = data.address?.[1] || "";
          user.addressLine3 = data.address?.[2] || "";
          break;
        case "socialUrls":
          for (const socialMediaKey in SOCIAL_MEDIAS) {
            user.socialMedias[socialMediaKey] =
              data.socialUrls?.[socialMediaKey]?.url || "";
          }
          break;
        case "contacts":
          for (const contactKey in CONTACTS_METHODS) {
            user.contacts[contactKey] =
              data.contacts?.[contactKey]?.value || "";
          }
          break;
        case "receivePromotions":
          user[key] = data[key];
          break;
        default:
          user[key] = data[key] || "";
      }
    }

    return Response.json(user, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function PUT(req: NextRequest) {
  try {
    const token = await getToken({ req });
    if (!token?.accessToken) throw Error("Unauthorized");
    const userId = jwtDecode(token.accessToken).sub!;

    const {
      addressLine1,
      addressLine2,
      addressLine3,
      gender,
      identity,
      dateOfBirth,
      socialMedias,
      contacts: contactsValue,
      ...body
    } = await req.json();

    const socialUrls: Record<string, any> = {};
    for (const key in socialMedias) {
      if (socialMedias[key]) {
        socialUrls[key] = { url: socialMedias[key] };
      }
    }

    const contacts: Record<string, any> = {};
    for (const key in contactsValue) {
      if (contactsValue[key]) {
        contacts[key] = { value: contactsValue[key] };
      }
    }

    const data = await authorizedPut(
      `/users/${userId}`,
      await getAuthHeaders(req),
      {
        ...body,
        identity,
        address: [addressLine1, addressLine2, addressLine3],
        gender: identity === "personal" && gender !== "" ? gender : null,
        dateOfBirth: dateOfBirth !== "" ? dateOfBirth : null,
        socialUrls,
        contacts,
      }
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET, PUT };
