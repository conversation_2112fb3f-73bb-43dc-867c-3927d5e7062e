"use client";
import type { NextPage } from "next";
import { Box, Card, styled, Typography } from "@mui/material";
import Link from "next/link";
import { useTranslations } from "next-intl";
import PublicPageContainer from "@/components/PublicPageContainer";
import SubmitButton from "@/components/buttons/SubmitButton";
import GoogleButton from "@/components/buttons/GoogleButton";
import TextField from "@/components/input/TextField";
import Image from "next/image";
import { ROUTES } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { LoginSchema } from "@/schemas/LoginSchema";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { GoogleLoginButton } from "./GoogleLoginButton";
import { useEffect } from "react";
import { showErrorToast, showToast } from "@/utils/toast";

type FormValue = z.infer<typeof LoginSchema>;

const Login: NextPage = () => {
  const t = useTranslations("login");
  const router = useRouter();
  
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
  } = useForm<FormValue>({
    defaultValues: {
      email: "",
      password: "",
    },
    resolver: zodResolver(LoginSchema),
  });

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const res = await signIn("credentials", { ...data, redirect: false });
      if (res?.error) throw Error("invalidCredentials");
      router.push("/home");
    } catch (e) {
      setLoginError()
    }
  };

  const setLoginError = () => {
    setError("password", {
      type: "custom",
      message: "invalid_credential",
    });
    showErrorToast(401)
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
      }}
    >
      <Card 
        sx={{
          display: 'flex',
          flexDirection: 'column',
          padding: '40px',
          width: '70%',
          maxWidth: '500px',
          maxHeight: '438px',
          aspectRatio: 4/5,
          gap: '56px',
        }}
      >
        <Box sx={{ width: '40vw' }}>
          <Image
            src={"/images/bg-login.svg"}
            width={0}
            height={0}
            sizes="100vw"
            style={{ width: 'auto', height: '36px' }}
            alt={'incutix'}
          />
        </Box>
        <Box
          component={"form"}
          style={{ width: "100%" }}
          action="/api/auth/callback/credentials"
          onSubmit={handleSubmit(onSubmit)}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '32px'
          }}
        >
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <TextField
                value={field.value}
                onChange={field.onChange}
                size="medium"
                disabled={isSubmitting}
                label={t("placeholder_email")}
                placeholder={t("placeholder_email")}
                error={errors?.email?.message}
              />
            )}
          />
          <Controller
            name="password"
            control={control}
            render={({ field }) => (
              <TextField
                value={field.value}
                onChange={field.onChange}
                size="medium"
                disabled={isSubmitting}
                type="password"
                label={t("placeholder_password")}
                placeholder={t("placeholder_password")}
                error={errors?.password?.message}
              />
            )}
          />
          <SubmitButton
            sx={{ marginTop: 1.25, marginBottom: 3.5 }}
            disabled={isSubmitting}
          >
            {t("button_login")}
          </SubmitButton>
        </Box>
      </Card>
    </Box>
  );
};

export default Login;
