'use client'
import {
    Typography,
  } from "@mui/material";
import { useTranslations } from "next-intl";
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import IProfile from "./profile/page";
import dynamic from "next/dynamic";
import PersonIcon from '@mui/icons-material/Person';
import ConfirmationNumberIcon from '@mui/icons-material/ConfirmationNumber';
import WalletIcon from '@mui/icons-material/Wallet';
import { useEffect } from "react";
import MyTicket from "./my-ticket/page";
import PurchaseRecord from "./purchase-record/page";

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
  }
  
  function TabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;
  
    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`vertical-tabpanel-${index}`}
        aria-labelledby={`vertical-tab-${index}`}
        {...other}
      >
        {value === index && (
          <Box sx={{ p: 3 }}>
            <Typography>{children}</Typography>
          </Box>
        )}
      </div>
    );
  }
  
  function a11yProps(index: number) {
    return {
      id: `vertical-tab-${index}`,
      'aria-controls': `vertical-tabpanel-${index}`,
    };
  }

const AccountSettings = () =>{
    const t = useTranslations("navbar");
    const [value, setValue] = React.useState(-1);

    React.useEffect(() => {
        setValue(0); 
      }, []);

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
      setValue(newValue);
    };

    // if (value === -1) {
    //     return <Loader />; // 伺服器端渲染時顯示加載狀態
    //   }
    
    useEffect(() => {
      const hash = window.location.hash;
      if (hash) {
          switch (hash) {
              case '#my_ticket':
                  setValue(1);
                  break;
              case '#purchase_record':
                  setValue(2);
                  break;
              default:
                  setValue(0);
          }
      }
    }, []);
  
    return (
      <Box
        sx={{ flexGrow: 1, bgcolor: 'background.paper', display: 'flex',mt:2, mb:2 }}
      >
        <Tabs
          orientation="vertical"
          variant="scrollable"
          value={value}
          onChange={handleChange}
          aria-label="Vertical tabs example"
          // sx={{ borderRight: 1, borderColor: 'divider',display:"flex", mr:2, ml:2 }}
          sx={{ display:"flex", mr:2, ml:2 }}
          TabIndicatorProps={{
            sx: {
                backgroundColor: 'transparent', 
            }
        }}
        >
          <Tab label={t('account_information')}
               icon={<PersonIcon />}
          {...a11yProps(0)}    
                    sx={{
                    border: '1px solid rgba(244, 245, 246, 1)', 
                    '&:hover': {
                        borderColor: 'rgba(79, 183, 71, 1)', 
                    },
                    '&.Mui-selected': {
                      color: 'rgba(79, 183, 71, 1)', 
                      borderColor: 'rgba(79, 183, 71, 1)',
                    },
                    width:"150px",
                    borderRadius:"16px"
                }}/>
          <Tab label={t('my_ticket')}
               icon={<ConfirmationNumberIcon />}
          {...a11yProps(1)}  sx={{
                    border: '1px solid rgba(244, 245, 246, 1)', 
                    '&:hover': {
                        borderColor: 'rgba(79, 183, 71, 1)', 
                    },
                    '&.Mui-selected': {
                      color: 'rgba(79, 183, 71, 1)', 
                      borderColor: 'rgba(79, 183, 71, 1)',
                    },
                    mt:1,
                    width:"150px",
                    borderRadius:"16px"
                }}/>
          <Tab label={t('purchase_record')} 
               icon={<WalletIcon />}
          {...a11yProps(2)}  sx={{
                    border: '1px solid rgba(244, 245, 246, 1)', 
                    '&:hover': {
                        borderColor: 'rgba(79, 183, 71, 1)', 
                    },
                    '&.Mui-selected': {
                      color: 'rgba(79, 183, 71, 1)', 
                      borderColor: 'rgba(79, 183, 71, 1)',
                    },
                    mt:1,
                    width:"150px",
                    borderRadius:"16px"
                }}/>
          {/* <Tab label="Item Four" {...a11yProps(3)} />
          <Tab label="Item Five" {...a11yProps(4)} />
          <Tab label="Item Six" {...a11yProps(5)} />
          <Tab label="Item Seven" {...a11yProps(6)} /> */}
        </Tabs>
        <TabPanel value={value} index={0}>
            <IProfile/>
        </TabPanel>
        <TabPanel value={value} index={1}>
            <MyTicket/>
        </TabPanel>
        <TabPanel value={value} index={2}>
            <PurchaseRecord/>
        </TabPanel>
        {/* <TabPanel value={value} index={3}>
          Item Four
        </TabPanel>
        <TabPanel value={value} index={4}>
          Item Five
        </TabPanel>
        <TabPanel value={value} index={5}>
          Item Six
        </TabPanel>
        <TabPanel value={value} index={6}>
          Item Seven
        </TabPanel> */}
      </Box>
    );
}

export default AccountSettings;