"use client";

import ModalContainer from "@/components/ModalContainer";
import CancelButton from "@/components/buttons/CancelButton";
import EditButton from "@/components/buttons/EditButton";
import SaveButton from "@/components/buttons/SaveButton";
import { IMemberGroup } from "@/interface/IMemberGroup";
import { IMember } from "@/interface/IMember";
import { MemberGroupSchema } from "@/schemas/MemberGroupSchema";
import { Box, Modal, Typography, Select, MenuItem } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import * as React from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { IListResponse } from "@/interface/IListResponse";

type FormValue = z.infer<typeof MemberGroupSchema>;

interface Props {
  target: IMember;
  updateContents: Function;
}

const EditMemberList = ({ target, updateContents }: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const [groups, setGroups] = React.useState<Array<any>>([])
  const [selectedGroups, setSelectedGroups] = React.useState<Array<any>>([])

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
    reset,
  } = useForm<FormValue>();

  const dataQuery = useQuery({
    queryKey: ["member_groups"],
    queryFn: async () =>
      xior
        .get<IListResponse<IMemberGroup>>("/api/member-groups",)
        .then((res) => {
          return res.data;
        }),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  React.useEffect(() => {
    if (dataQuery?.data?.items) setGroups(dataQuery.data.items)
  }, [dataQuery?.data?.items])

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (isSubmitting) return;
    setOpen(false);
  }, [isSubmitting]);

  const onSubmit: SubmitHandler<FormValue> = async () => {
    try {
      if (selectedGroups?.length === 0) {
        setError("groups", { type: "custom", message: "please select at least one item(s)" })
        return;
      }
      const groups = selectedGroups
      const reqBody = {
        members: target,
        groups
      }
      const res = await xior.post("/api/members/group/create", reqBody);
      queryClient.invalidateQueries({ queryKey: ["addToGroup"] });
      await updateContents()
      setSelectedGroups([])
      reset();
      setOpen(false);
    } catch (e) {
      setError("name", { type: "custom", message: "duplicated_tags" });
    }
  };

  const handleOnChange = (event: any) => {
    const {
      value
    } = event.target
    setSelectedGroups([...new Set([
      ...selectedGroups,
      ...value
    ])])
  }

  return (
    <Box>
      <EditButton sx={{ marginRight: 1.5 }} onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("member_management_list.label_add_to_group")}
            </Typography>
          </Box>
          <Typography fontSize={10}>
            User: <br />
            <>{target.email} <br /></>
            <br />
          </Typography>
          <Controller
            name="groups"
            control={control}
            render={({ field }) => (
              <Select
                required
                multiple={true}
                disabled={isSubmitting}
                error={!!errors?.groups?.message}
                sx={{ width: 360 }}
                value={selectedGroups}
                onChange={handleOnChange}
              >
                {
                  groups.map((group) => (
                    <MenuItem key={group.id} value={group.id}>{group.name}</MenuItem>
                  ))
                }
              </Select>
            )}
          />
          <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
            <CancelButton disabled={isSubmitting} onAction={handleClose} />
            <SaveButton disabled={isSubmitting} />
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default EditMemberList;
