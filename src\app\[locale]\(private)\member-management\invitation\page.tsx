"use client";
import type { NextPage } from "next";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import ExportButton from "@/components/buttons/ExportButton";
import ImportButton from "@/components/buttons/ImportButton";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import InvitationListTable from "./components/InvitationListTable";
import CreateInvitation from "./components/CreateInvitation";

const InvitationList: NextPage = () => {
  const t = useTranslations("invitation");
  const router = useRouter();

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={t("label_title")}>
        <>
          <ExportButton  />
          <ImportButton  />
          <CreateInvitation  />
        </>
      </PageHeader>
      <Box flex={1} padding="26px 34px">
        <InvitationListTable />
      </Box>
    </Box>
  );
};

export default InvitationList;
