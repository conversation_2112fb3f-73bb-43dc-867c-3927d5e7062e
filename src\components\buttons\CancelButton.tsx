import React from "react";
import { useTranslations } from "next-intl";
import { BaseButton } from "./styled";
import { COLORS } from "@/styles/colors";
import { Box, Typography } from "@mui/material";

type Props = {
  disabled?: boolean;
  label?: string;
  onAction: () => void;
  style?: React.CSSProperties;
};

const CancelButton = (props: Props) => {
  const { disabled, label, onAction, style } = props;

  const t = useTranslations();
  return (
    <BaseButton
      disabled={disabled}
      onClick={onAction}
      sx={{ padding: "16px 26px", borderRadius: "999px", border: `2px solid ${COLORS.GREY_3}` }}
    >
      <Typography color={COLORS.GREY_8}>{label || t("common.button_cancel")}</Typography>
    </BaseButton>
  );
};

export default CancelButton;
