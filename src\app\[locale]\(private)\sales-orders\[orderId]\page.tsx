import type { NextPage } from "next";
import { Box } from "@mui/material";

import SalesOrderBreadcrumbs from "./_components/SalesOrderBreadcrumbs";
import SalesOrderDetailContainer from "./_components/SalesOrderDetailContainer";
import { retrieveOrderById } from "../_actions/retrieveOrderById.action";
import { retrieveCountryByCode } from "../_actions/retrieveCountryByCode.action";

type SaleOrderDetailProps = { params: { locale: string; orderId: string } };

const SaleOrderDetail: NextPage<SaleOrderDetailProps> = async ({ params }) => {
  const { locale, orderId } = params;
  const data = await retrieveOrderById(isNaN(Number(orderId)) ? 0 : Number(orderId), locale);
  const country = await retrieveCountryByCode(data?.region ?? "", locale);

  return (
    <>
      <Box marginLeft={4} marginTop={3} display={"flex"} flexDirection={"column"}>
        <Box marginBottom={4}>
          <SalesOrderBreadcrumbs orderNo={data?.orderNo ?? ""} />
        </Box>
      </Box>
      <SalesOrderDetailContainer saleOrderDetail={data} country={country?.name ?? ""} />
    </>
  );
};

export default SaleOrderDetail;
