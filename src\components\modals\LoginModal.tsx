import CustomButton from "@/components/buttons/CustomButton";
import { useTranslations } from "next-intl";
import { useState } from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import { Checkbox, FormControlLabel, IconButton, Link } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import CustomTextField from "@/components/input/CustomTextField"
import { signIn } from 'next-auth/react';
import { ROUTES } from "@/utils/constants";
import {showErrorPopUp,showSuccessPopUp} from "@/utils/toast";

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 4,
};

type Props = {
    openLogin:boolean
    handleCloseLogin: () => void
    handleOpenRegister: () => void
    email:string
    handleEmailChange: (event:any) => void
    password:string
    handlePasswordChange: (event:any) => void
    isSubmitting:boolean
    handleSubmit: (event:any) => void
    namespace: string
    rememberMe:boolean
    handleRememberMeChange:(event:any) => void
    handleOpenForgetPassword:() => void
}

const LoginModal = ({openLogin,
                     handleCloseLogin,
                     handleOpenRegister,
                     email,
                     handleEmailChange,
                     password,
                     handlePasswordChange,
                     isSubmitting,
                     handleSubmit,
                     namespace,
                     rememberMe,
                     handleRememberMeChange,
                     handleOpenForgetPassword
                    }:Props) => {
    const t = useTranslations(namespace); 
                        
    return(
        <>
           <Modal
                open={openLogin}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
                sx={{
                    backdropFilter: 'blur(4px)', 
                    backgroundColor: 'rgba(122, 122, 123, 0.5)',
                }}
            >
                <Box sx={{ ...style, paddingTop: 2, paddingRight: 2, borderRadius:"24px" }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end' }}>
                        <IconButton onClick={handleCloseLogin}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                    <Typography id="modal-modal-title" variant="h2" component="h2">
                        {t('login')}
                    </Typography>
                    <Box sx={{
                        display:"flex",
                        mt:1,
                        mb:1
                    }}>
                        <Typography id="modal-modal-title" variant="body1" component="h2">
                            未有帳戶?
                        </Typography>&nbsp;
                        <Link onClick={handleOpenRegister} sx={{ cursor: 'pointer' }}>按此註冊</Link>
                    </Box>
                    <Box sx={{
                        display:'flex',
                        flexDirection:"column",
                        minWidth: 120,
                        gap: 2,
                        mt:2

                    }}>
                    <CustomTextField
                        namespace="login"
                        label="login_email"
                        placeholder="placeholder_email"
                        value={email}
                        onChange={handleEmailChange}
                    />
                    <CustomTextField
                        namespace="login"
                        label="login_password"
                        placeholder="placeholder_password"
                        type="password"
                        value={password}
                        onChange={handlePasswordChange}
                    />
                    <Box sx={{
                        display:"flex",
                        alignItems:"center",
                        justifyContent:"space-between"
                    }}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                checked={rememberMe}
                                onChange={handleRememberMeChange}
                                sx={{
                                    color: 'rgba(189, 189, 189, 1)',
                                    '&.Mui-checked': {
                                    color: 'rgba(79, 183, 71, 0.5)', // Color when checked
                                    },
                                    '&.Mui-checked:hover': {
                                    backgroundColor: 'rgba(165, 62, 255, 0.1)', // Optional: change background on hover
                                    }
                                }}
                                />
                            }
                            label={<Typography >Remember me</Typography>}
                        />
                        <Link onClick={handleOpenForgetPassword} sx={{ cursor: 'pointer' }}>忘記密碼?</Link>
                    </Box>
                    <CustomButton disabled={isSubmitting}  onClick={handleSubmit} namespace="login" label="title"  />
                    </Box>
                </Box>
            </Modal>
        </>
    )
}

export default LoginModal;