import { ICollection } from "@/interface/ICollection";
import { IListResponse } from "@/interface/IListResponse";
import { IRoom } from "@/interface/IRoom";
import { authorizedGet, getAuthHeaders, handleApiError } from "@/utils/api";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";
import xior, { XiorError } from "xior";

async function GET(req: NextRequest) {
  try {
    const token = await getToken({ req });
    if (!token?.accessToken) throw Error("Unauthorized");
    const searchParams = req.nextUrl.searchParams;
    const data = await authorizedGet<IListResponse<ICollection>>(
      "/collections",
      await getAuthHeaders(req),
      {
        page: searchParams.get("page"),
        size: searchParams.get("size"),
        name: searchParams.get("keyword"),
        sortBy: searchParams.get("sortby"),
        sortDirection: searchParams.get("order"),
      }
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error); 
  }
}

async function POST(req: NextRequest) {
  try {
    const token = await getToken({ req });
    if (!token?.accessToken) throw Error("Unauthorized");

    const data = await req.json();

    const res = await xior.post<ICollection>("/collections", data, {
      headers: {
        Authorization: `Bearer ${token?.accessToken}`,
      },
      baseURL: process.env.NEXT_PUBLIC_API_BASE,
    });
    return Response.json(res.data, { status: 200 });
  } catch (error) {
    if (error instanceof XiorError) {
      return new Response(`Error: ${error.message}`, {
        status: error.response?.status,
      });
    }
    return new Response(`Unknown Error`, { status: 500 });
  }
}

export { GET, POST };
