import React from "react";
import { Button, ButtonProps } from "@mui/material";
import { useTranslations } from "next-intl";
import AddRoundedIcon from "@mui/icons-material/AddRounded";
import { ButtonHeadTable } from "./styled";
import MailOutlineIcon from '@mui/icons-material/MailOutline';

interface MyButtonProps extends ButtonProps {
    iconOnly?: boolean;
}

const SendButton: React.FC<MyButtonProps> = (props) => {
    const t = useTranslations("common");
    const { iconOnly, sx, ...otherProps } = props;

    if (iconOnly) {
        return (
            <Button
                sx={{
                    height: 36,
                    width: 36,
                    borderRadius: 18,
                    minWidth: 0,
                    ["& .MuiButton-startIcon"]: {
                        mx: 0,
                    },
                    ...sx,
                }}
                variant="contained"
                startIcon={<MailOutlineIcon sx={{ height: 16, width: 16 }} />}
                {...otherProps}
            />
        );
    }
    return (
        <Button
            variant="contained"
            endIcon={<MailOutlineIcon sx={{ height: 14, width: 14 }} />}
            sx={sx}
            {...otherProps}
        >
            {t("button_send")}
        </Button>
    );
};

export default SendButton;
