"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const CollectionIcon = createSvgIcon(
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.7142 12C13.7142 12.3031 13.5938 12.5938 13.3795 12.8081C13.1652 13.0224 12.8745 13.1428 12.5714 13.1428H3.4285C3.1254 13.1428 2.83471 13.0224 2.62038 12.8081C2.40605 12.5938 2.28564 12.3031 2.28564 12V3.99997C2.28564 3.69687 2.40605 3.40618 2.62038 3.19185C2.83471 2.97752 3.1254 2.85712 3.4285 2.85712H6.28564L7.4285 4.5714H12.5714C12.8745 4.5714 13.1652 4.69181 13.3795 4.90614C13.5938 5.12046 13.7142 5.41115 13.7142 5.71426V12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
</svg>
  ,
  "Collection"
);

export default CollectionIcon;