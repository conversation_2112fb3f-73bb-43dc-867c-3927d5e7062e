import { Button, ButtonProps } from "@mui/material";
import { useTranslations } from "next-intl";
import Image from "next/image";

const GoogleButton = (props: ButtonProps) => {
  const t = useTranslations("login");

  return (
    <Button
      {...props}
      variant="outlined"
      sx={{
        height: 54,
      }}
      startIcon={
        <Image
          src={"/images/logo-google.svg"}
          height={18}
          width={18}
          alt="Google login"
        />
      }
    >
      {t("button_google_login")}
    </Button>
  );
};

export default GoogleButton;
