"use client"
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import PageHeader from "@/components/PageHeader"
import { ROUTES } from "@/utils/constants";
import { Box, Typography } from "@mui/material"
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import SingleInventoryTable from "./SingleInventoryTable";
import { IInventory } from "@/interface/IInventory";
import { useEffect, useState } from "react";
import xior from "xior";
import { IProductWarehouseRecords } from "@/interface/IProductWarehouseRecord";

interface Props {
  params: { id: string };
}

const SingleInventory = ({ params }: Props) => {

  const inventoryId: number = Number(params.id);
  const [getInventoryDto, setGetInventoryDto] = useState<IInventory | undefined>(undefined);
  const [addProductWarehouseRecords, setAddProductWarehouseRecords] = useState<IProductWarehouseRecords>({
    productWarehouseId: inventoryId,
    currentUnavailable: 0,
    currentCommitted: 0,
    currentQuantity: 0,
    sku: ''
  })

  const handleAddRecordsChange = (addProductWarehouseRecords: IProductWarehouseRecords) => {
    setAddProductWarehouseRecords(addProductWarehouseRecords)
  }

  const t = useTranslations("inventory");
  const router = useRouter();

  const getInventoryDtoApi = async () => {

    try {
      const response = await xior.get(`/api/inventory/${params.id}`);
      setGetInventoryDto(response.data);
    } catch (error) {
      console.error('Error fetching inventory:', error);
    }
  };

  const postInventoryRecordsApi = async () => {
    try {
      await xior.post(`/api/inventory`, addProductWarehouseRecords);
      getInventoryDtoApi();
    } catch (error) {
      console.error('Error posting inventory records:', error);
    }
  };

  console.log(addProductWarehouseRecords)

  useEffect(() => {
    getInventoryDtoApi();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (getInventoryDto) {
        setAddProductWarehouseRecords({
            productWarehouseId: inventoryId,
            currentUnavailable: getInventoryDto.unavailable || 0,
            currentCommitted: getInventoryDto.currentCommitted || 0,
            currentQuantity: getInventoryDto.currentQuantity || 0,
            sku: getInventoryDto.sku || ''
        });
    }
  }, [getInventoryDto, inventoryId]);

  return (
    <>
      <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
        <PageHeader title={`${t("label_title")} > ${getInventoryDto?.name}`}>
          <Box>
            <CancelButton onAction={() => router.push(ROUTES.INVENTORY)} />
            <SaveButton onAction={postInventoryRecordsApi} />
          </Box>
        </PageHeader>
        <Box flex={1} padding="26px 34px">
          {
            getInventoryDto &&
            <SingleInventoryTable
              getInventoryDto={getInventoryDto}
              params={params}
              addProductWarehouseRecords={addProductWarehouseRecords}
              handleAddRecordsChange={handleAddRecordsChange}
            />
          }
        </Box>
      </Box>
    </>
  )
}

export default SingleInventory