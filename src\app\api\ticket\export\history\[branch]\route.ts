import { IOwner } from "@/interface/IOwner";
import {
  authorizedDelete,
  authorizedGet,
  authorizedPut,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { SOCIAL_MEDIAS } from "@/utils/constants";
import { NextRequest } from "next/server";

async function GET(req: NextRequest, { params }: { params: { branch: string } }) {
  try {
    const data = await authorizedGet<any>(
        `/api/admin/v1/ticket/export/history/${params.branch}`,
        //   await getAuthHeaders(req)
        {}
    );
    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET };
