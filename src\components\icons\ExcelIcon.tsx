"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const ExcelIcon = createSvgIcon(
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <rect width="16" height="16" fill="url(#pattern0_44_848)" />
    <defs>
      <pattern
        id="pattern0_44_848"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use xlinkHref="#image0_44_848" transform="scale(0.00195312)" />
      </pattern>
      <image
        id="image0_44_848"
        width="512"
        height="512"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>,
  "Excel"
);

export default ExcelIcon;
