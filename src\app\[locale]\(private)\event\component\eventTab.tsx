"use client"
import { useTranslations } from 'next-intl';
import { Box, Card, Checkbox, Icon, IconButton, Skeleton, styled, Tab, Tabs, Typography } from '@mui/material';
import React, { useState, useEffect, useMemo } from 'react';
import { generalPadding } from '@/styles/theme';
import { COLORS } from '@/styles/colors';
import { SUPPORT_REGION, SUPPORT_TIME_ZONE_CODE } from '@/utils/timezone';
import { generateDisplayableDate, generateDisplayableDateTime, generateDisplayableDuration, generateDisplayableTime, getDay } from '@/utils/date';
import Image from 'next/image';
import HtmlContent from '@/components/HtmlContent';
import EditIntroduction from './EditIntroduction';
import TextEditor from '@/components/RichEditor';
import EditTerms from './EditTerms';
import EditQAA from './EditQAA';
import AddQAA from './AddQAA';
import EditMedia from './EditMedia';
import EditButton from '@/components/buttons/EditButton';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import xior from 'xior';
import { LANGUAGE_CODE } from '@/utils/constants';
import { EventTranslation, extractTranslationBody } from '@/utils/translation';
import EditEvent from './EditEvent';

export enum EVENT_STATUS {
    DRAFT='DRAFT',
    PUBLISHED='PUBLISHED',
    ACTIVE='ACTIVE',
    INACTIVE='INACTIVE',
    EXPIRED='EXPIRED'
}


const SkeletonCard = (component: () => React.ReactNode, isLoading: boolean) => {
    if (isLoading) {
        return (
            <Skeleton variant="rectangular" width={'100%'} height={'44px'} />
        )
    } else {
        return component()
    }
}

interface Props {
  eventInfo?: any,
  isLoading: boolean;
  isEdit: boolean;
  language: LANGUAGE_CODE;
}

const EventTab = ({ eventInfo, isLoading, language }: Props) =>{
    const t = useTranslations('event');
    const tTicket = useTranslations('ticket');
    const tCommon = useTranslations('common');
    const [showAll, setShowAll] = useState(false);
    const [image, setImage] = useState<(File|{src: string, id: number})[]>([]);
    const queryClient = useQueryClient();

    const {
        id,
        event_media: eventMedia = [],
        event_qaa: eventQAA = [],
        event_setting: eventSetting = {},
        event_terms: eventTerms = {},
        parent_event: parentEvent = {},
        ticket_setting: ticketSetting = [],
        translation = [],
        ...baseInfo
    } = (eventInfo?.data?.data?.event ?? {})

    useEffect(() => {
        setImage(eventMedia)
    }, [eventMedia])

    const mutation = useMutation({
        mutationFn: () => {
            const url = `/api/event/media/${id}`
            const formData = new FormData()
            const existingIds = eventMedia.map((media: { id: number }) => media.id)
            // @ts-ignore
            const currentIds = image.filter(item => 'id' in item).map((media: { id: number }) => media.id)
            const removeIds = existingIds.filter((id: number) => !currentIds.includes(id))
            for (var x = 0; x < removeIds.length; x++) {
                formData.append("removeIds", removeIds[x])
            }
            for (var x = 0; x < image.length; x++) {
                if (!('src' in image)) {
                    formData.append("event", image[x] as File);
                }
            }
            return xior.put(url, formData)
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["event"]});
        },
    });

    // event section
    const renderEventInfo = () => (
        <Card
            sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '30%',
                minWidth: '278px',
                padding: generalPadding,
                gap: '16px',
                height: 'fit-content'
            }}
        >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant='body1' fontWeight={'bold'}>
                    {t('event_detail')}
                </Typography>
                <EditEvent
                    language={language}
                    id={id}
                    info={{
                        name: baseInfo.name,
                        description: baseInfo.description,
                        regionCode: baseInfo.region_code,
                        timezone: baseInfo.timezone,
                        startDate: baseInfo.start_date * 1000,
                        endDate: baseInfo.end_date * 1000,
                        startTime: baseInfo.start_time * 1000,
                        endTime: baseInfo.end_time * 1000,
                        duration: baseInfo.duration,
                        venue: baseInfo.venue                    
                    }}
                />
            </Box>
            
            {
                [
                    { label: t("event_name"), value: extractTranslationBody(language, EventTranslation.NAME, translation)},
                    { 
                        label: t("region"), 
                        value: SUPPORT_REGION?.find(item => item.value === baseInfo.region_code)?.full ?? '-'
                    },
                    { 
                        label: t("timezone"),
                        value: SUPPORT_TIME_ZONE_CODE?.find(item => item.value === baseInfo.timezone)?.label ?? '-'
                    },
                    {
                        label: t("event_date"),
                        value: [baseInfo.start_date, baseInfo.end_date]
                        .filter(item => item)
                        .map(item => generateDisplayableDate(item))
                        .join('-')
                    },
                    {
                        label: t("event_time"),
                        value: (baseInfo.start_time ? [baseInfo.start_time, baseInfo.end_time]: [])
                        .filter(item => item)
                        .map(item => {
                            const {
                                hour,
                                min
                            } = generateDisplayableTime(item)

                            return `${`${hour}`.padStart(2, '0')}: ${`${min}`.padStart(2, '0')}`
                        })
                        .join(' - ')
                    },
                    { label: t("about_time"), value: `
                            ${baseInfo.duration? generateDisplayableDuration(baseInfo.duration) : "-"}
                        `},
                    { label: t("venue"), value: extractTranslationBody(language, EventTranslation.VENUE, translation)},
                ].map((item) => {
                    return SkeletonCard(() => (
                        <Typography
                            variant='body2'
                            display={'flex'}
                            flexDirection={'column'}
                            gap={'4px'}
                        >
                            <Typography color={COLORS.GREY_6}>{item.label}</Typography>
                            <Typography>{item.value}</Typography>
                        </Typography>
                    ), isLoading)
                })
            }
        </Card>
    )

    const removeImage = (target: number) => {
        setImage(image.filter((img, idx) => idx !== target))
    }

    const renderImage = (idx: number, object: { src: string } | File) => (
        <Box position={'relative'} width="160px" height="100px">
            <Box position={'absolute'} top={-8} right={-12} onClick={() => removeImage(idx)}>
                <Image src={'/images/icon-close.svg'} width={24} height={24} alt={`remove`} />
            </Box>
            <Image 
                src={'src' in object? object.src : URL.createObjectURL(object)}
                width={160}
                height={100}
                style={{
                    borderRadius: '8px',
                    objectFit: 'cover'
                }}
                alt={`image_${idx}`}
            />
        </Box>
    )

    const checkedQAA = (event: any) => {
        const {
            checked,
            value
        } = event.target
    }

    const setFiles = (files: File[]) => {
        const newFiles = Array.from(files).map((file: File) => file)
        setImage([...(image || []), ...newFiles])
    }

    const handleCancel = () => {
        setImage(eventMedia)
    }

    const handleSave = () => {
        mutation.mutate()
    }

    const renderEventRelevant = () => (
        <Box
            display={'flex'}
            flexDirection={'column'}
            gap={'32px'}
            sx={{
                width: '65%',
                minWidth: '278px',
            }}
        >
            <Card
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    padding: generalPadding,
                }}
            >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                        <Typography variant='body1' fontWeight={'bold'}>
                            {t('gallery')}
                        </Typography>
                        
                    </Box>

                    {
                        eventMedia.length !== image.length ? (
                            <Box sx={{ display: 'flex', gap: '16px'}}>
                                <EditButton
                                    label={tCommon('button_cancel')}
                                    isSmall={true}
                                    isPrimay={false}
                                    onClick={handleCancel}
                                />
                                <EditButton
                                    label={tCommon('button_save')}
                                    isSmall={true}
                                    onClick={handleSave}
                                />
                            </Box>
                        ) : (
                            <EditMedia eventId={id} handleChange={setFiles} />
                        )
                    }
                </Box>

                <Typography variant='body3' fontWeight={'400'} color={COLORS.GREY_6}>
                    {t('media_remark')}
                </Typography>
                
                <Box sx={{ display: 'flex', gap: '12px' }}>
                    {
                        (image ?? []).map((
                            imageObject,
                            idx: number
                        ) => renderImage(idx, imageObject))
                    }
                </Box>
            </Card>
            <Card
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    padding: generalPadding
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    }}
                >
                    <Typography variant='body1' fontWeight={'bold'}>
                        {t('event_introduction')}
                    </Typography>
                    <EditIntroduction
                        id={id}
                        language={language}
                        content={extractTranslationBody(language, EventTranslation.CONTENT, eventSetting?.translation)}
                    />
                </Box>
                {
                    eventSetting?.content && (
                        <Box bgcolor={COLORS.GREY_3} padding={'12px'}>
                            <HtmlContent
                                content={extractTranslationBody(language, EventTranslation.CONTENT, eventSetting?.translation)}
                            />
                        </Box>
                    )
                }
            </Card>

            <Card
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    padding: generalPadding
                }}
            >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant='body1' fontWeight={'bold'}>
                        {t('qaa')}
                    </Typography>
                    <AddQAA language={language} eventId={id} content={eventQAA}/>
                </Box>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    {
                        eventQAA?.map(({id, question, translation}: {
                            id: number,
                            question: string,
                            translation: {
                                translation: {
                                    content: string,
                                    fields: EventTranslation,
                                    language_code: LANGUAGE_CODE
                                }
                            }[]
                        }) => (
                            <Box
                                key={`qaa_${id}`}
                                sx={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    backgroundColor: COLORS.GREY_3,
                                    borderRadius: '8px',
                                    justifyContent: 'space-between',
                                    paddingRight: '20px'
                                }}
                            >
                                <Box
                                    sx={{
                                        display: 'flex',
                                        gap: '12px',
                                        alignItems: 'center',
                                    }}
                                >
                                    <Checkbox 
                                        value={id}
                                        aria-label={
                                            extractTranslationBody(language, EventTranslation.QUESTION, translation) ||
                                            question
                                        }
                                        onChange={checkedQAA}
                                    />
                                    <Typography variant='body2'>
                                        {
                                            extractTranslationBody(language, EventTranslation.QUESTION, translation) ||
                                            question
                                        }
                                    </Typography>
                                </Box>
                                <Box
                                    sx={{
                                        display: 'flex',
                                        gap: '12px',
                                        alignItems: 'center',
                                    }}
                                >
                                    <EditQAA
                                        language={language}
                                        id={id}
                                        content={eventQAA.map(({
                                            id,
                                            translation
                                        }: {
                                            id: number,
                                            translation: {
                                                translation: {
                                                    content: string,
                                                    fields: EventTranslation,
                                                    language_code: LANGUAGE_CODE
                                                }
                                            }[]
                                        }) => {
                                            return {
                                                id,
                                                answer: extractTranslationBody(language, EventTranslation.ANSWER, translation),
                                                question: extractTranslationBody(language, EventTranslation.QUESTION, translation),
                                            }
                                        })}
                                        target={id}
                                    />
                                    <Box>
                                        <Image
                                            src={'/images/icon-option.svg'}
                                            width={24}
                                            height={24}
                                            alt={'option'}
                                        />
                                    </Box>
                                </Box>
                            </Box>
                        ))
                    }
                </Box>
            </Card>
            <Card
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    padding: generalPadding
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    }}
                >
                    <Typography variant='body1' fontWeight={'bold'}>
                        {t('terms')}
                    </Typography>
                    <EditTerms
                        id={id}
                        language={language}
                        content={extractTranslationBody(language, EventTranslation.CONTENT, eventTerms?.translation)}
                    />
                </Box>
                {
                    extractTranslationBody(language, EventTranslation.CONTENT, eventTerms?.translation) && (
                        <Box
                            bgcolor={COLORS.GREY_3}
                            padding={'12px'}
                            display={'flex'}
                            flexDirection={'column'}
                            gap={'16px'}
                        >
                            <HtmlContent
                                content={extractTranslationBody(language, EventTranslation.CONTENT, eventTerms?.translation)}
                            />
                            <Box onClick={() => { setShowAll(!showAll) }}>
                                <Typography
                                    variant='body2'
                                    fontWeight={'bold'}
                                    color={COLORS.GREY_6}
                                    display={'flex'}
                                    gap={'4px'}
                                    alignItems={'center'}
                                >
                                    {
                                        showAll? tCommon('show_less'): tCommon('view_all')
                                    }
                                    <Image
                                        src="/images/icon-collapse.svg"
                                        width={20}
                                        height={20}
                                        alt="col"
                                    />
                                </Typography>
                            </Box>
                        </Box>
                    )
                }
            </Card>
        </Box>
    )
    //

    return (
        <Box display={'flex'} flexDirection={'row'} gap={'32px'}>
            {renderEventInfo()}
            {renderEventRelevant()}
        </Box>
    )
}

export default EventTab;