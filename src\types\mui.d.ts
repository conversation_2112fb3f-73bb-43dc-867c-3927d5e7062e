import { createTheme, PaletteColorOptions } from '@mui/material/styles'

declare module '@mui/material/styles/createPalette' {
  interface PaletteOptions {
    incutix: {
      primary: PaletteColorOptions
      secondary: PaletteColorOptions
      warning: PaletteColorOptions
      error: PaletteColorOptions
      black: string
      grey: PaletteColorOptions
      white: string
    }
  }
  interface Palette {
    incutix: PaletteColor | any
  }
}
