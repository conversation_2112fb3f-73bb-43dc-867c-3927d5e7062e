import { NextRequest } from "next/server";
import xior, { Xior<PERSON>rro<PERSON> } from "xior";

const ERRORS: { [key: string]: string } = {
  "User is already verified": "user_already_verified",
};

async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    const res = await xior.post(`/auth/resend-otp`, data, {
      baseURL: process.env.NEXT_PUBLIC_API_BASE,
    });

    return Response.json({ status: "success" }, { status: 200 });
  } catch (error) {
    if (error instanceof XiorError && error.response) {
      const { data, status } = error.response;
      return new Response(ERRORS[data] || "unknown_error", { status });
    }
    return new Response("unknown_error", { status: 500 });
  }
}

export { POST };
