import { IListResponse } from "@/interface/IListResponse";
import { IProductCategory } from "@/interface/IProductCategory";
import {
  authorizedGet,
  authorizedPost,
  authorizedPut,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";

async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const data = await authorizedGet<IListResponse<IProductCategory>>(
      "/product",
      await getAuthHeaders(req),
      {
        page: searchParams.get("page"),
        size: searchParams.get("size"),
        name: searchParams.get("keyword"),
      }
    );
    const productCategories = data.items.map((item) => ({
      ...item,
      children: item.children?.map((child) => ({
        ...child,
        parent: { name: item.name, id: item.id },
      })),
    }));

    return Response.json(
      {
        items: productCategories,
        count: data.count,
      },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error);
  }
}

async function POST(req: NextRequest) {
  try {
    const product = await req.json();
    const data = await authorizedPost<IProductCategory>(
      "/product",
      await getAuthHeaders(req),
      product
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function PUT(req: NextRequest) {
  try {
    const product = await req.json();
    const data = await authorizedPut<IProductCategory>(
      "/product",
      await getAuthHeaders(req),
      product
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET, POST, PUT };
