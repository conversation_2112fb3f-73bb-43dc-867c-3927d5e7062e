import {
    authorizedGet,
    getAuthHeaders,
    authorizedPut,
    handleApiError,
    authorizedPost,
  } from "@/utils/api";
import { NextRequest , NextResponse} from "next/server";
import {ClientOrderDetails} from "@/interface/IClientOrderDetails";

async function GET(req: NextRequest,{ params }: { params: { id: number } }) {
    
    try{

        const data = await authorizedGet<ClientOrderDetails>(
            `/clientorder/${params.id}`,
            await getAuthHeaders(req)
        )

        return Response.json(data, { status: 200 });

    }catch(error){
        return handleApiError(error);
    }

}

export { GET };