"use client";
import ModalContainer from "@/components/ModalContainer";
import AddNewButton from "@/components/buttons/AddNewButton";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import TextField from "@/components/input/TextField";
import { MemberGroupSchema } from "@/schemas/MemberGroupSchema";
import { COLORS } from "@/styles/colors";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Modal, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import * as React from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";
import CloseIcon from '@mui/icons-material/Close';

type FormValue = z.infer<typeof MemberGroupSchema>;

const CreateMemberGroup = () => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const [group, setGroup] = React.useState('')
  const [groups, setGroups] = React.useState([])
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
    clearErrors,
    reset,
  } = useForm<FormValue>();

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (isSubmitting) return;
    setGroup('')
    reset()
    clearErrors()
    setOpen(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSubmitting]);

  const handleOnChange = (event: any) => {
    const {
      value
    } = event.target
    setGroup(value)

    if (value[value.length - 1] === ',') {
      setGroups([... new Set([
        ...groups,
        value.slice(0, -1)
      ])] as never[]);
      setGroup('')
    }
  }

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      let uploadGroup = groups
      if (uploadGroup.length === 0) {
        if (!data || !data.name) {
          setError("name", { type: "custom", message: "at least enter one group" })
          return;
        }
        uploadGroup = [data.name as never]
      }
      const res = await xior.post("/api/member-groups", uploadGroup.map((group) => ({ name: group })));
      queryClient.invalidateQueries({ queryKey: ["member_groups"] });
      const {
        added = []
      } = res.data
      if (added.length !== uploadGroup.length) {
        const addedGroups = added.map((addedItem: any) => addedItem.name)
        setGroups(groups.filter(group => !addedGroups.includes(group)))
        setError("name", {
          type: "custom",
          message: "duplicated_member_group",
        });
      } else {
        reset();
        setOpen(false);
        setGroup('')
        setGroups([])
      }
    } catch (e) {
      setError("name", { type: "custom", message: "duplicated_member_group" });
    }
  };

  return (
    <Box>
      <AddNewButton onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("member_group.title_create_member_group")}
            </Typography>
          </Box>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                value={group}
                onChange={(event) => {
                  handleOnChange(event)
                  field.onChange(event)
                }}
                disabled={isSubmitting}
                label={t("member_group.label_group_name")}
                placeholder={t("member_group.label_split")}
                error={errors?.name?.message}
              />
            )}
          />
          <div>
            {
              groups.map((group) =>
                <span
                  style={{
                    width: 'auto',
                    padding: 8,
                    border: '1px solid black',
                    borderRadius: 20
                  }}
                  key={`group${group}`}
                >
                  {group}
                  <CloseIcon fontSize="small" onClick={() => {
                    setGroups([
                      ...groups.filter((groupItem) => groupItem !== group)
                    ])
                  }} />
                </span>
              )
            }
          </div>
          <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
            <CancelButton
              disabled={isSubmitting}
              onAction={handleClose}
            />
            <SaveButton disabled={isSubmitting} />
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default CreateMemberGroup;
