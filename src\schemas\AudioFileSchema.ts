import { z } from "zod";
import {
  ACCEPTED_AUDIO_TYPES,
  MAX_AUDIO_FILE_SIZE,
  SCHEMA_ERRORS,
} from "@/utils/constants";

export const AudioFileSchema = z
  .instanceof(File)
  .optional()
  .refine(
    (file) =>
      file && file.size > 0 ? ACCEPTED_AUDIO_TYPES.includes(file.type) : true,
    SCHEMA_ERRORS.invalidAudioType
  )
  .refine(
    (file) => (file ? file.size <= MAX_AUDIO_FILE_SIZE : true),
    SCHEMA_ERRORS.audioFileSizeExceed
  );
