import {
  batchUpdateOrderShippingStatusRequestSchema,
  BatchUpdateShippingStatusResponseType,
} from "../_schema/saleOrder.schema";
import { fetchData } from "./api";

const URL = `${process.env.NEXT_PUBLIC_API_BASE}/api/admin/v1/order/shipping-status/batch`;

export const batchUpdateShippingStatus = async (data: any) => {
  const requestData = batchUpdateOrderShippingStatusRequestSchema.parse(data);

  await fetchData<BatchUpdateShippingStatusResponseType>(URL, {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(requestData),
  });

  return true;
};
