"use client"
import ImagesSlider from '@/components/ImagesSlider';
import React, { useEffect, useState } from 'react';
import { Grid, Paper, Typography,Box } from '@mui/material';
import LeftSplitPart from './LeftSplitPart';
import RightSplitPart from './RightSplitPart';
import xior from "xior";
import {Event} from '@/interface/IEventList';

interface Props {
    params: { id: string };
  }
  

const FeaturedEventDetails = ({ params }: Props) =>{

    const [eventDetails,setEventDtails] = useState<Event | undefined>(undefined);

    console.log("get event details",eventDetails)

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const getEventDetailsApi = async () =>{
        try{

            const response = await xior.get(`/api/featured-event/${params.id}`)

            setEventDtails(response.data);
            console.log("get event details successfully")
        }catch(error){
            console.error('Error fetching event details:', error);
        }

    }

    useEffect(() => {
        getEventDetailsApi()
    }, [getEventDetailsApi]);

    const images = [
        'https://s3.ap-southeast-1.amazonaws.com/portal-dev.fun-verse.io/productThumbnail/tmp/51656209e3952cbd393753000c3713b7a6cc9386.jpeg',
        'https://s3.ap-southeast-1.amazonaws.com/portal-dev.fun-verse.io/productThumbnail/tmp/1e0fdb9db6d47e726d9ea69a7a31476892c65c2c.jpeg',
        'https://assets.incutix.com/events/one_piece_tour_thailand/OPTH_KV_Horizontal.jpg',
        'https://assets.incutix.com/events/JuJuTsu_Kaisen_Exhibition_my/jjk_my_kv_20240813.jpg',
      ];

    return(
        <>
        <ImagesSlider
            images={images}
            interval={5000} 
            height="400px" 
            blurIntensity={100} 
        />
        <Box sx={{
            paddingLeft:2,
            paddingRight:2,
            paddingTop:2
        }}>
            <Grid container spacing={2}>
            {/* 左邊部分 */}
            <Grid item xs={8}>
                {/* <Paper elevation={3} style={{ padding: '20px' }}>

                </Paper> */}
                {  
                    eventDetails &&
                    <LeftSplitPart event={eventDetails}/>
                }
            </Grid>

            {/* 右邊部分 */}
            <Grid item xs={4}>
                {/* <Paper elevation={3} style={{ padding: '20px' }}>

                </Paper> */}
                <RightSplitPart/>
            </Grid>
            </Grid>
        </Box>   
        </>
    )
}

export default FeaturedEventDetails;