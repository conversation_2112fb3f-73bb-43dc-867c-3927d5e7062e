import React from "react";
import { Box, Breadcrumbs, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";

interface Props {
  title: string | string[];
  children?: React.ReactNode;
}

const PageHeader = ({ title, children }: Props) => (
  <Box
    display={"flex"}
    flexDirection={"row"}
    height={56}
    alignItems={"center"}
    paddingLeft={"34px"}
    paddingRight={"48px"}
    borderBottom={`4px solid ${COLORS.DIVIDER}`}
  >
    {typeof title === "string" ? (
      <Typography fontSize={15} fontWeight={700}>
        {title}
      </Typography>
    ) : (
      <Breadcrumbs
        separator={
          <NavigateNextIcon htmlColor={COLORS.BLACK} sx={{ mx: -1 }} />
        }
        aria-label="breadcrumb"
      >
        {title.map((item, index) => (
          <Typography key={`breadcrumb-${index}`} color="text.primary">
            {item}
          </Typography>
        ))}
      </Breadcrumbs>
    )}
    <Box flex={1} />
    {children}
  </Box>
);

export default PageHeader;
