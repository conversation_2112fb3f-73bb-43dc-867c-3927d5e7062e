"use client";
import { formatCurrency, getCurrencySymbol } from "@/utils/currency";
import { Box, Divider, Paper, Stack, styled } from "@mui/material";
import { useTranslations } from "next-intl";
import { SaleOrderDetailType } from "../../../_schema/saleOrder.schema";
import dayjs from "dayjs";

type Props = {
  saleOrderDetail?: SaleOrderDetailType;
};

const StyledBox = styled(Paper)(({ theme }) => ({
  width: 320,
  borderRadius: 16,
  border: `1px solid ${theme.palette.incutix.grey[400]}`,
  padding: "20px",
  backgroundColor: theme.palette.incutix.white,
}));
const DetailTitle = styled(Box)(({ theme }) => ({
  marginBottom: "24px",
  fontSize: 18,
  fontWeight: 700,
  color: "#000000",
}));
const DetailItemLabel = styled(Box)(({ theme }) => ({
  minHeight: 20,
  display: "flex",
  width: "100%",
  justifyContent: "left",
  alignItems: "center",
  fontSize: 14,
  fontWeight: 400,
  color: theme.palette.incutix.grey[600],
}));
const DetailItemValue = styled(Box)(({ theme }) => ({
  minHeight: 20,
  display: "flex",
  width: "100%",
  justifyContent: "right",
  alignItems: "center",
  fontSize: 14,
  fontWeight: 400,
  color: "#000000",
  textAlign: "right",
}));

const SalesOrderOrderSummary = ({ saleOrderDetail }: Props) => {
  const t = useTranslations("sales_orders.detail.order_summary");

  const data = {
    orderNo: saleOrderDetail?.orderNo ?? "",
    txnRefNo: saleOrderDetail?.txnRefNo ?? "",
    createAt: saleOrderDetail?.createAt
      ? dayjs.unix(saleOrderDetail?.createAt).format("YYYY/MM/DD HH:mm:ss")
      : "",
    paymentMethod: saleOrderDetail?.paymentMethod ?? "",
    orderBilling: {
      address: saleOrderDetail?.orderBilling.address,
    },
    currency: saleOrderDetail?.currency ?? "",
    orderSummary: {
      orderSubTotal: formatCurrency(saleOrderDetail?.orderSummary.orderSubTotal ?? 0),
      orderShippingFee: formatCurrency(saleOrderDetail?.orderSummary.orderShippingFee ?? 0),
      orderDiscountAmount: formatCurrency(saleOrderDetail?.orderSummary.orderDiscountAmount ?? 0),
      orderTotalQuantity: formatCurrency(saleOrderDetail?.orderSummary.orderTotalQuantity ?? 0),
      orderTotalAmount: formatCurrency(saleOrderDetail?.orderSummary.orderTotalAmount ?? 0),
    },
  };
  const currencySymbol = getCurrencySymbol(data.currency);

  return (
    <StyledBox elevation={0}>
      <DetailTitle>{t("title")}</DetailTitle>
      <Stack spacing={1}>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("order_no")}</DetailItemLabel>
          <DetailItemValue>{data.orderNo}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("transaction_no")}</DetailItemLabel>
          <DetailItemValue>{data.txnRefNo}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("order_created")}</DetailItemLabel>
          <DetailItemValue>{data.createAt}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("payment_method")}</DetailItemLabel>
          <DetailItemValue>{data.paymentMethod}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("billing_address")}</DetailItemLabel>
          <DetailItemValue>{data.orderBilling.address}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("currency")}</DetailItemLabel>
          <DetailItemValue>{data.currency}</DetailItemValue>
        </Box>
      </Stack>
      <Divider sx={{ marginTop: "16px", marginBottom: "16px" }} />
      <Stack spacing={1}>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("sub_total")}</DetailItemLabel>
          <DetailItemValue>{`${currencySymbol} ${data.orderSummary.orderSubTotal}`}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("shipping_cost")}</DetailItemLabel>
          <DetailItemValue>{`${currencySymbol} ${data.orderSummary.orderShippingFee}`}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("discount")}</DetailItemLabel>
          <DetailItemValue sx={{ color: (theme) => theme.palette.incutix.error[100] }}>
            {`${currencySymbol} ${data.orderSummary.orderDiscountAmount}`}
          </DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("promo_code", { code: "" })}</DetailItemLabel>
          <DetailItemValue sx={{ color: (theme) => theme.palette.incutix.error[100] }}>
            {`${currencySymbol} ${"0.00"}`}
          </DetailItemValue>
        </Box>
      </Stack>
      <Divider sx={{ marginTop: "16px", marginBottom: "16px" }} />
      <Stack spacing={1}>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>
            {t("summary", { itemCount: data.orderSummary.orderTotalQuantity })}
          </DetailItemLabel>
          <DetailItemValue sx={{ fontSize: "18px", fontWeight: 700 }}>
            {`${currencySymbol} ${data.orderSummary.orderTotalAmount}`}
          </DetailItemValue>
        </Box>
      </Stack>
    </StyledBox>
  );
};

export default SalesOrderOrderSummary;
