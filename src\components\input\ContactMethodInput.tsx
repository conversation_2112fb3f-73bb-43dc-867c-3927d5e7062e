"use client";
import * as React from "react";
import {
  TextField as Mu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TextFieldProps,
  InputAdornment,
} from "@mui/material";
import Box from "@mui/material/Box";
import InputContainer, { InputContainerProps } from "./InputContainer";
import { CONTACTS_METHODS } from "@/utils/constants";

interface Props extends InputContainerProps {
  value: string;
  onChange: (value: string) => void;
  contactMethod: "whatsapp" | "telegram" | "other";
}

export default function ContactMethodInput({
  contactMethod,
  label,
  description,
  required,
  value,
  onChange,
  error,
  disabled,
  ...otherProps
}: Props & Omit<TextFieldProps, "error">) {
  return (
    <InputContainer label={label} error={error}>
      <Box display={"flex"} flexDirection={"row"}>
        <MuiTextField
          disabled
          value={CONTACTS_METHODS[contactMethod].label}
          sx={{
            mr: 1,
            width: 220,
            "& .MuiInputBase-input.Mui-disabled": {
              color: "#000",
              WebkitTextFillColor: "unset",
              fontWeight: 400,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: "#000 !important",
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Box
                  component="img"
                  src={CONTACTS_METHODS[contactMethod].src}
                  sx={{ height: 16, width: 16, objectFit: "contain" }}
                />
              </InputAdornment>
            ),
          }}
        />
        <MuiTextField
          sx={{ width: 480 }}
          disabled={disabled}
          value={value}
          onChange={onChange}
          placeholder={CONTACTS_METHODS[contactMethod].placeholder}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Box
                  component="img"
                  src={"/images/link.png"}
                  sx={{ height: 16, width: 16, objectFit: "contain" }}
                />
              </InputAdornment>
            ),
          }}
        />
      </Box>
    </InputContainer>
  );
}
