export const orderStatus = {
  inProgress: "inProgress",
  complete: "complete",
  cancel: "cancel",
};
export const orderShippingStatus = {
  pending: "pending",
  delivered: "delivered",
};
export const orderShippingType = {
  storePickup: "storePickup",
  express: "express",
};
export const orderType = {
  product: "product",
  ticket: "ticket",
};

export type OrderStatusType =
  | typeof orderStatus.cancel
  | typeof orderStatus.complete
  | typeof orderStatus.inProgress;

export type OrderShippingStatusType =
  | typeof orderShippingStatus.pending
  | typeof orderShippingStatus.delivered;

export type OrderShippingType =
  | typeof orderShippingType.storePickup
  | typeof orderShippingType.express;

export type OrderType = typeof orderType.product | typeof orderType.ticket;
