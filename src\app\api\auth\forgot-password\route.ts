import { handleApiError } from "@/utils/api";
import { NextRequest } from "next/server";
import xior from "xior";

async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    const res = await xior.post(`/auth/forgot-password`, data, {
      baseURL: process.env.NEXT_PUBLIC_API_BASE,
    });

    return Response.json({ status: "success", ...data }, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { POST };
