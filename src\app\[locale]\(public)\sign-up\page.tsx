"use client";
import type { NextPage } from "next";
import { Box, Typography } from "@mui/material";
import Link from "next/link";
import { useTranslations } from "next-intl";
import PublicPageContainer from "@/components/PublicPageContainer";
import SubmitButton from "@/components/buttons/SubmitButton";
import GoogleButton from "@/components/buttons/GoogleButton";
import TextField from "@/components/input/TextField";
import Image from "next/image";
import { ROUTES } from "@/utils/constants";
import { useRouter, useSearchParams } from "next/navigation";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { SignUpSchema } from "@/schemas/SignUpSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import xior, { XiorError } from "xior";
import { useEffect, useState } from "react";

type FormValue = z.infer<typeof SignUpSchema>;

const SignUp: NextPage = () => {
  const t = useTranslations("signup");
  const router = useRouter();
  const searchParams = useSearchParams();
  const [locked, setLocked] = useState(false);

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
    setValue
  } = useForm<FormValue>({ resolver: zodResolver(SignUpSchema) });

  useEffect(() => {
    const email = searchParams.get('email')
    if (email) {
      setValue("email", email)
      setLocked(true)
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [])

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      await xior.post("/api/auth/register", data);
      router.push(`${ROUTES.VERIFY}?email=${encodeURIComponent(data.email)}`);
    } catch (e: unknown) {
      setError("email", { message: (e as XiorError)?.response?.data });
    }
  };

  return (
    <PublicPageContainer>
      <Typography variant="h1" sx={{ marginBottom: 1 }}>
        {t("title")}
      </Typography>
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{ marginBottom: 2.5 }}
      >
        {t("message")}
      </Typography>
      <GoogleButton fullWidth variant="outlined" />
      <Typography
        variant="body1"
        sx={{ marginTop: 2.5, marginBottom: 2.5, textDecoration: "underline" }}
      >
        {t("signup_by_email")}
      </Typography>
      <Box
        component={"form"}
        style={{ width: "100%" }}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting || locked}
              placeholder={t("placeholder_email")}
              error={errors?.email?.message}
            />
          )}
        />
        <SubmitButton
          sx={{ marginTop: 1.25, marginBottom: 2.75 }}
          disabled={isSubmitting}
        >
          {t("button_signup")}
        </SubmitButton>
      </Box>

      <Typography
        component={"span"}
        variant="body1"
        sx={{ marginBottom: 4.75, textAlign: "center" }}
      >
        {t.rich("agree_to_tos_and_pp", {
          tos: (chunks) => (
            <Link href={ROUTES.HOME} className="link-grey">
              {chunks}
            </Link>
          ),
        })}
      </Typography>
      <Typography variant="body1" component={"span"}>
        {t.rich("already_have_an_account", {
          arrow: () => (
            <Box
              sx={{
                height: 20,
                width: 20,
                position: "relative",
                mx: 0.5,
                display: "inline-block",
                top: 4,
              }}
            >
              <Image src={"/images/arrow-right.svg"} layout="fill" alt="" />
            </Box>
          ),
          login: (chunks) => (
            <Link href={ROUTES.LOGIN} className="link-black">
              {chunks}
            </Link>
          ),
        })}
      </Typography>
    </PublicPageContainer>
  );
};

export default SignUp;
