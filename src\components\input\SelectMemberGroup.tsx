"use client";
import * as React from "react";
import { useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { Autocomplete } from "@mui/material";
import { IMemberGroup } from "@/interface/IMemberGroup";
import Text<PERSON>ield from "./TextField";
import { InputContainerProps } from "./InputContainer";

interface Props extends InputContainerProps {
  placeholder?: string;
  disabled?: boolean;
  value?: IMemberGroup[] | any;
  onChange: (newValue: IMemberGroup[]) => void;
}

export default function SelectMemberGroup({
  label,
  placeholder,
  error,
  disabled,
  value = [],
  onChange,
}: Props) {

  const dataQuery = useQuery({
    queryKey: ["member_groups"],
    queryFn: async () =>
      xior
        .get<IListResponse<IMemberGroup>>("/api/member-groups")
        .then((res) => ({
          items: res.data.items as IMemberGroup[],
          total: res.data.count,
        })),
  });

  const getItems = () => {
    if (dataQuery.data?.items) {
      const {
        items
      } = dataQuery.data
      const parsedValue: Array<any> = value.map((item: number | string) => {
        if (typeof item !== 'object') {
          return items.find((options: any) => options.id === item)
        }
        return item
      })
      return parsedValue
    }
    return []
  }

  return (
    <Autocomplete
      multiple
      disabled={disabled}
      value={getItems()}
      options={dataQuery.data?.items || []}
      onChange={(event, newValue) => {
        onChange(newValue);
      }}
      getOptionLabel={(option) => option?.name}
      isOptionEqualToValue={(option, value) => option?.id === value?.id}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          error={error}
        />
      )}
    />
  );
}
