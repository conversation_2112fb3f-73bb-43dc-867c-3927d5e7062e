import CustomButton from "@/components/buttons/CustomButton";
import { useTranslations } from "next-intl";
import { useState } from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import { Checkbox, FormControlLabel, IconButton, Link } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import CustomTextField from "@/components/input/CustomTextField"
import { signIn } from 'next-auth/react';
import { ROUTES } from "@/utils/constants";
import {showErrorPopUp,showSuccessPopUp} from "@/utils/toast";
import { MuiOtpInput } from 'mui-one-time-password-input';
import {
    checkPasswordLength,
    checkUppercase,
    checkLowercase,
    checkSpecialCharacter,
    checkNumber,
  } from "@/utils/validators";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

type Props = {
    openRegister:boolean
    handleCloseRegister:() => void
    handleOpenLoginInRegisterModel:() => void
    registeredEmail:string
    handleRegisteredEmailChange:(event:any) => void
    handleRegisterAccountApi: () => void
    isValidatedEmail:boolean
    isVerifiedEmail:boolean
    otp:string
    handleOtpChange:(value: string) => void
    countdown:number
    handleResnetOtpApi:() => void
    handleVerifyOtpApi:() => void
    userName:string
    newPassword:string
    confirmNewPassword:string
    handleUserNameChange:(event:any) => void
    handleNewPassword:(event:any) => void
    handleConfirmNewPassword:(event:any) => void
    isCheckTermsOfService:boolean
    isCheckReceivePromotions:boolean
    handleTermsOfServiceChange:(event:any) => void
    handleReceivePromotionsChange:(event:any) => void
    handleSetupProfileApi:() => void
}
 
const linkStyles = {
    color:"rgba(79, 183, 71, 0.8)",
    }
    
const passwordValidation = {
    display: "flex",
    color:"rgba(189, 189, 189, 1)"
    }

const textStyles = {
    color:"rgba(189, 189, 189, 1)",
    }

const RegisterModal = ({openRegister,
                        handleCloseRegister,
                        handleOpenLoginInRegisterModel,
                        registeredEmail,
                        handleRegisteredEmailChange,
                        handleRegisterAccountApi,
                        isValidatedEmail,
                        isVerifiedEmail,
                        otp,
                        handleOtpChange,
                        handleResnetOtpApi,
                        handleVerifyOtpApi,
                        countdown,
                        userName,
                        newPassword,
                        confirmNewPassword,
                        handleUserNameChange,
                        handleNewPassword,
                        handleConfirmNewPassword,
                        isCheckTermsOfService,
                        isCheckReceivePromotions,
                        handleTermsOfServiceChange,
                        handleReceivePromotionsChange,
                        handleSetupProfileApi
                    }:Props) =>{

    // const [otp, setOtp] = useState<string>("");

    // // 修改這裡的事件處理器
    // const handleOtpChange = (value: string) => {
    //     setOtp(value);
    // };
    const t = useTranslations('signup'); 

    const [validations, setValidations] = useState({
        length: false,
        uppercase: false,
        lowercase: false,
        specialChar: false,
        number: false,
      });

    const handleNewPasswordTextFieldChange = (event: React.ChangeEvent<HTMLInputElement>) =>{
        const newPassword = event.target.value;
        handleNewPassword(newPassword)
        setValidations({
            length: checkPasswordLength(newPassword),
            uppercase: checkUppercase(newPassword),
            lowercase: checkLowercase(newPassword),
            specialChar: checkSpecialCharacter(newPassword),
            number: checkNumber(newPassword),
          });
    }  

    const isFormValid = () => {
    return (
        validations.length &&
        validations.uppercase &&
        validations.lowercase &&
        validations.specialChar &&
        validations.number 
    );
    };

    const style = {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 400,
        bgcolor: 'background.paper',
        boxShadow: 24,
        p: 4,
        maxHeight: "70vh", 
        overflowY: "auto",  
    };

    // console.log("registeredEmail",registeredEmail)

    const renderRegisterPopup = () =>{
        if (isVerifiedEmail && isValidatedEmail) {
            // 顯示完成註冊的內容
            return(
                <>
                <Modal
                    open={openRegister}
                    aria-labelledby="register-modal-title"
                    aria-describedby="register-modal-description"
                    sx={{
                        backdropFilter: 'blur(4px)', 
                        backgroundColor: 'rgba(122, 122, 123, 0.5)',
                    }}
                >
                    <Box sx={{ ...style, paddingTop: 2, paddingRight: 2, borderRadius:"24px" }}>
                        <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            justifyContent: 'end'              
                            }}>
                            <IconButton onClick={handleCloseRegister}>
                                <CloseIcon />
                            </IconButton>
                        </Box>
                        <Typography id="register-modal-title" variant="h2" component="h2">
                            完成註冊
                        </Typography>
                        <Box sx={{
                        display:'flex',
                        flexDirection:"column",
                        minWidth: 120,
                        gap: 2,
                        mt:2
                        }}>
                        {/* 在此添加註冊的內容 */}
                        <CustomTextField
                        namespace="signup"
                        label="user_name"
                        placeholder="placeholder_name"
                        value={userName}
                        onChange={handleUserNameChange}
                        />
                        <CustomTextField
                        namespace="signup"
                        label="label_password"
                        type="password"
                        placeholder="placeholder_password"
                        value={newPassword}
                        onChange={handleNewPasswordTextFieldChange}
                        />
                        <CustomTextField
                        namespace="signup"
                        label="label_confirm_password"
                        type="password"
                        placeholder="placeholder_confirm_password"
                        value={confirmNewPassword}
                        onChange={handleConfirmNewPassword}
                        />
                        <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.length ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography >
                        8-20 characters
                        </Typography>
                    </Box>
                    <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.uppercase ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography sx={textStyles}>
                        An UPPERCASE letter
                        </Typography>
                    </Box>
                    <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.lowercase ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography sx={textStyles}>
                        A lowercase letter
                        </Typography>
                    </Box>
                    <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.specialChar ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography sx={textStyles}>
                            A special character
                        </Typography>
                    </Box>
                    <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.number ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography sx={textStyles}>
                            A number
                        </Typography>
                    </Box>
                        <FormControlLabel
                        control={
                            <Checkbox
                            checked={isCheckTermsOfService}
                            onChange={handleTermsOfServiceChange}
                            sx={{
                                color: 'rgba(189, 189, 189, 1)',
                                '&.Mui-checked': {
                                color: 'rgba(79, 183, 71, 0.5)', // Color when checked
                                },
                                '&.Mui-checked:hover': {
                                backgroundColor: 'rgba(165, 62, 255, 0.1)', // Optional: change background on hover
                                }
                            }}
                            />
                        }
                        label={<Typography >{t("terms_of_service")}</Typography>}
                        />
                        <FormControlLabel
                        control={
                            <Checkbox
                            checked={isCheckReceivePromotions}
                            onChange={handleReceivePromotionsChange}
                            sx={{
                                color: 'rgba(189, 189, 189, 1)',
                                '&.Mui-checked': {
                                color: 'rgba(79, 183, 71, 0.5)', // Color when checked
                                },
                                '&.Mui-checked:hover': {
                                backgroundColor: 'rgba(165, 62, 255, 0.1)', // Optional: change background on hover
                                }
                            }}
                            />
                        }
                        label={<Typography >{t("dont_receive_promotion_message")}</Typography>}
                        />
                         <CustomButton disabled={!isCheckTermsOfService || !isFormValid()} onClick={handleSetupProfileApi} namespace="signup" label="signup_confirm"  />
                        </Box>
                    </Box>
                </Modal>
                </>
            )
           
        } else if (isValidatedEmail) {
            // 顯示 OTP 驗證的內容
            return(
                <>
                <Modal
                    open={openRegister}
                    aria-labelledby="register-modal-title"
                    aria-describedby="register-modal-description"
                    sx={{
                        backdropFilter: 'blur(4px)', 
                        backgroundColor: 'rgba(122, 122, 123, 0.5)',
                    }}
                >
                    <Box sx={{ ...style, paddingTop: 2, paddingRight: 2, borderRadius:"24px" }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end' }}>
                            <IconButton onClick={handleCloseRegister}>
                                <CloseIcon />
                            </IconButton>
                        </Box>
                        <Box sx={{
                            display:'flex',
                            flexDirection:"column",
                            minWidth: 120,
                            gap: 2,
                            mt:2
    
                         }}>
                        <Typography id="register-modal-title" variant="h2" component="h2">
                            電郵驗證
                        </Typography>
                        <Typography id="register-modal-title" variant="body2" component="h2" >
                            請輸入收到的電郵地址驗證碼
                        </Typography>
                        <MuiOtpInput 
                        value={otp} 
                        onChange={handleOtpChange} 
                        length={6}
                        />
                        <CustomButton onClick={handleVerifyOtpApi} namespace="signup" label="next_step"/>
                        <Box sx={{
                                display:"ruby"
                            }}>
                            <Typography sx={{
                                    color:"black"
                            }}>Didn&apos;t receive the link?</Typography>
                            &nbsp;&nbsp; {/* 保留空格 */}
                                <Link 
                                href="#" 
                                variant="subtitle1" 
                                sx={{
                                    ...linkStyles,
                                    fontSize: '1rem',
                                    pointerEvents: countdown > 0 ? 'none' : 'auto',
                                }} 
                                onClick={handleResnetOtpApi}
                                            >
                                    {countdown > 0 ? `Resend (${countdown}s)` : "Resend"}
                                </Link>
                            </Box>
                        </Box>
                    </Box>
                </Modal>
                </>
            )
        } else {
            // 顯示初始註冊的內容
            return(
                <>
                    <Modal
                    open={openRegister}
                    aria-labelledby="register-modal-title"
                    aria-describedby="register-modal-description"
                    sx={{
                        backdropFilter: 'blur(4px)', 
                        backgroundColor: 'rgba(122, 122, 123, 0.5)',
                    }}
                >
                    <Box sx={{ ...style, paddingTop: 2, paddingRight: 2, borderRadius:"24px" }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end' }}>
                            <IconButton onClick={handleCloseRegister}>
                                <CloseIcon />
                            </IconButton>
                        </Box>
                        <Typography id="register-modal-title" variant="h2" component="h2">
                            註冊
                        </Typography>
                        {/* 在此添加註冊的內容 */}
                        <Box sx={{
                            display:"flex",
                            mt:2,
                            mb:2
                        }}>
                            <Typography id="modal-modal-title" variant="body1" component="h2">
                                已有帳戶?
                            </Typography>&nbsp;
                            <Link onClick={handleOpenLoginInRegisterModel} sx={{ cursor: 'pointer' }}>按此登入</Link>
                        </Box>
                        <Box sx={{
                            display:'flex',
                            flexDirection:"column",
                            minWidth: 120,
                            gap: 2,
                            mt:2
    
                         }}>
                        <CustomTextField
                            namespace="login"
                            label="login_email"
                            placeholder="placeholder_email"
                            value={registeredEmail}
                            onChange={handleRegisteredEmailChange}
                        />
                        <CustomButton onClick={handleRegisterAccountApi} namespace="signup" label="send_verification_code"/>
                        </Box>
                    </Box>
                </Modal>
                </>
            )

        }
    }
    return(
        <>
            {renderRegisterPopup()}
        </>
    )
}

export default RegisterModal;