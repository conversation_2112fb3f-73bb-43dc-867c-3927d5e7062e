export interface IInventory {
    id:          number;
    productId:   number;
    quantity:    number;
    currentQuantity:    number;
    createdAt:   number;
    updatedAt:   number;
    sku:         null | string;
    unavailable: number | null;
    committed:   number | null;
    currentCommitted:number;
    returned:    number | null;
    name:        string;
    thumbnail:   string;
    productType: number;
}