"use client";

import * as React from "react";
import { styled } from "@mui/material/styles";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { Box, TableSortLabel } from "@mui/material";
import { useTranslations } from "next-intl";
import { useSaleOrderListSort } from "../../../_hooks";

type Order = "asc" | "desc";
interface EnhancedTableProps {
  numSelected: number;
  onRequestSort: (event: React.MouseEvent<unknown>, property: any) => void;
  onSelectAllClick: (event: React.ChangeEvent<HTMLInputElement>) => void;
  order: Order;
  orderBy: string;
  rowCount: number;
}
interface HeadCell {
  id: string;
  label: string;
}

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.incutix.grey[300],
    border: 0,
    whiteSpace: "nowrap",
    width: "1%",
    padding: "0px 16px",
    textAlign: "left",
    ["& span.MuiTableSortLabel-root"]: {
      color: theme.palette.incutix.grey[600],
      fontSize: 16,
      fontWeight: 400,
    },
    [`&:first-child`]: {
      padding: 0,
      minWidth: "10px",
      textAlign: "center",
    },
  },
}));

const useHeaderCells = (): HeadCell[] => {
  const t = useTranslations("sales_orders.table.header");

  return [
    {
      id: "orderStatus",
      label: t("payment_status"),
    },
    {
      id: "orderNo",
      label: t("order_number"),
    },
    {
      id: "userName",
      label: t("customer"),
    },
    {
      id: "userEmail",
      label: t("customer_email"),
    },
    {
      id: "orderAmount",
      label: t("total"),
    },
    {
      id: "shippingStatus",
      label: t("delivery_status"),
    },
    {
      id: "create_at",
      label: t("create_at"),
    },
  ];
};

const EnhancedTableHead = (props: any) => {
  const headCells = useHeaderCells();

  const [{ sortOrder, sortOrderBy }, setTableSort] = useSaleOrderListSort();

  const handleTableSort = (property: string) => (event: React.MouseEvent<unknown>) => {
    const isAsc = sortOrderBy === property && sortOrder === "asc";
    setTableSort(property, isAsc ? "desc" : "asc");
  };

  return (
    <TableHead>
      <TableRow sx={{ height: "52px" }}>
        <StyledTableCell padding="checkbox" />
        {headCells.map((headCell) => (
          <StyledTableCell
            key={headCell.id}
            align={"left"}
            padding={"none"}
            sortDirection={sortOrderBy === headCell.id ? sortOrder : false}
          >
            <TableSortLabel
              active={sortOrderBy === headCell.id}
              direction={sortOrderBy === headCell.id ? sortOrder : "asc"}
              onClick={handleTableSort(headCell.id)}
            >
              {headCell.label}
            </TableSortLabel>
          </StyledTableCell>
        ))}
      </TableRow>
    </TableHead>
  );
};

export default EnhancedTableHead;
