"use client";
import * as React from "react";
import { styled } from "@mui/material/styles";
import Button from "@mui/material/Button";
import { Box, ButtonBaseProps, IconButton, Typography } from "@mui/material";
import Image from "next/image";
import CloseRoundedIcon from "@mui/icons-material/CloseRounded";
import { useTranslations } from "next-intl";
import {
  ACCEPTED_AUDIO_TYPES,
  ACCEPTED_IMAGE_TYPES,
  ACCEPTED_VIDEO_TYPES,
} from "@/utils/constants";
import { InputContainerProps } from "./InputContainer";

const ROUND_ITEM_DIMENSION = { width: 132, height: 132 };
const RECT_ITEM_DIMENSION = { width: "100%", height: 196 };

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

const RoundButton = styled(Button)<ButtonBaseProps>({
  height: ROUND_ITEM_DIMENSION.height,
  width: ROUND_ITEM_DIMENSION.width,
  borderRadius: ROUND_ITEM_DIMENSION.width / 2,
  background: "#fff",
  border: "0.5px solid #000000",
  display: "flex",
  flexDirection: "column",
  whiteSpace: "pre-line",
  overflow: "hidden",
  marginBottom: 1,
});

const RectButton = styled(Button)<ButtonBaseProps>({
  height: RECT_ITEM_DIMENSION.height,
  width: "100%",
  borderRadius: 10,
  background: "#fff",
  border: "0.5px solid #000000",
  display: "flex",
  flexDirection: "column",
  whiteSpace: "pre-line",
  overflow: "hidden",
  marginBottom: 1,
  padding: 0,
});

interface Props extends InputContainerProps {
  metadata?: string;
  name?: string;
  onChange: (file: File | undefined) => void;
  value?: File | string | any;
  disabled?: boolean;
  size?: "small" | "medium";
  type?: "image" | "video" | "image_video" | "audio";
  dimension?: number;
  multiple?: Boolean;
}

export default function FileInput({
  label,
  metadata,
  name,
  error,
  dimension,
  multiple = false,
  onChange,
  value,
  disabled,
  size = "medium",
  type = "image",
}: Props) {
  const t = useTranslations("error");
  const inputRef = React.useRef<HTMLInputElement>(null);

  const removeFile = React.useCallback(() => {
    onChange(undefined);
    if (inputRef.current) {
      inputRef.current.value = "";
    }
  }, [onChange]);

  const acceptFileTypes = React.useMemo(() => {
    let fileTypes = [];
    switch (type) {
      case "image":
        fileTypes = ACCEPTED_IMAGE_TYPES;
        break;
      case "audio":
        fileTypes = ACCEPTED_AUDIO_TYPES;
        break;
      case "video":
        fileTypes = ACCEPTED_VIDEO_TYPES;
        break;
      case "image_video":
        fileTypes = [...ACCEPTED_IMAGE_TYPES, ...ACCEPTED_VIDEO_TYPES];
        break;
    }

    return fileTypes.join(", ");
  }, [type]);

  const isSmall = size === "small";
  const InputButton = isSmall ? RoundButton : RectButton;
  const imageDimension = isSmall ? ROUND_ITEM_DIMENSION : RECT_ITEM_DIMENSION;

  const renderValue = React.useCallback(() => {
    if (!value) return null;
    if (type === "audio") {
      let audioFileName = "";
      if (typeof value === "string") {
        const pathSplit = value.split("/");
        audioFileName = pathSplit[pathSplit.length - 1];
      } else {
        audioFileName = value.name;
      }
      return <Typography>{audioFileName}</Typography>;
    }

    let component: "img" | "video" = "img";
    let src;
    if (typeof value === "string") {
      src = value;
      component = value.endsWith(".mp4") ? "video" : "img";
    } else {
      component = ACCEPTED_VIDEO_TYPES.includes(value.type) ? "video" : "img";
      if (multiple) {
        src = []
        for (let file in value) {
          if (typeof value[file] === 'object') {
            src.push(URL.createObjectURL(value[file]))
          }
        }
      } else {
        src = URL.createObjectURL(value);
      }
    }

    const image = typeof src === 'string' ? (
      <Box
        component={component}
        muted
        // autoPlay={false}
        src={src}
        alt="Upload image"
        height={imageDimension.height}
        width={imageDimension.width}
        sx={{ objectFit: "cover", position: "absolute" }}
      />
    ) : (
      <>
        {
          src.map((imageSrc) => (
            <Box
              key={imageSrc}
              component={component}
              muted
              // autoPlay={false}
              src={imageSrc}
              alt="Upload image"
              height={imageDimension.height}
              width={imageDimension.width}
              sx={{ objectFit: "cover", position: "absolute" }}
            />
          ))
        }
      </>
    );
    return image
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [imageDimension.height, imageDimension.width, type, value]);

  return (
    <Box
      display="flex"
      flexDirection={"column"}
      alignItems={"center"}
      width={isSmall ? "fit-content" : "100%"}
      mb={1.5}
    >
      {label && (
        <Typography
          variant="body1"
          sx={{ marginBottom: 0.5 }}
          alignSelf={"start"}
        >
          {label}
        </Typography>
      )}
      <Box position={"relative"} width={isSmall ? undefined : "100%"}>
        <InputButton
          component="label"
          role={undefined}
          variant="outlined"
          tabIndex={-1}
          disabled={disabled}
        >
          {!!value ? (
            renderValue()
          ) : (
            <>
              <Image
                height={36}
                width={36}
                src="/images/icon-upload.png"
                alt="Upload image"
              />
              <Typography
                fontSize={10}
                fontWeight={300}
                textAlign={"center"}
                lineHeight={"13px"}
                color={"#777777"}
              >
                {metadata}
              </Typography>
            </>
          )}
          <VisuallyHiddenInput
            ref={inputRef}
            type="file"
            multiple={multiple ? true : false}
            accept={acceptFileTypes}
            name={name}
          // onChange={(event) => {
          //   if (multiple) onChange(event.target.files);
          //   else onChange(event.target.files?.[0]);
          // }}
          />
        </InputButton>
        {value && (
          <IconButton
            aria-label="delete"
            size="small"
            sx={{
              background: "#ff3b36",
              height: 18,
              width: 18,
              position: "absolute",
              bottom: isSmall ? -4 : 14,
              right: isSmall ? -8 : 20,
            }}
            disabled={disabled}
            onClick={removeFile}
          >
            <CloseRoundedIcon sx={{ fill: "white", height: 16, width: 16 }} />
          </IconButton>
        )}
      </Box>
      {error && (
        <Typography
          color="error"
          fontSize={12}
          fontWeight={400}
          sx={{
            marginTop: isSmall ? 2 : 0,
            alignSelf: isSmall ? undefined : "flex-start",
          }}
        >
          {t(error)}
        </Typography>
      )}
    </Box>
  );
}
