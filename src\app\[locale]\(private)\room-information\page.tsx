import type { NextPage } from "next";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import AddNewButton from "@/components/buttons/AddNewButton";
import ImportButton from "@/components/buttons/ImportButton";
import ExportButton from "@/components/buttons/ExportButton";
import RoomTable from "./components/RoomTable";
import { ROUTES } from "@/utils/constants";

const RoomInformation: NextPage = () => {
  const t = useTranslations("room_information");

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={t("label_title")}>
        <>
          <AddNewButton href={ROUTES.CREATE_ROOM} />
        </>
      </PageHeader>
      <Box flex={1} padding="26px 34px">
        <RoomTable />
      </Box>
    </Box>
  );
};

export default RoomInformation;
