'use client'
import * as React from "react";
import { 
    Box,
    Drawer,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
} from "@mui/material";
import { useTranslations } from "next-intl";
import MenuIcon from '@mui/icons-material/Menu';
import { HEADER_HEIGHT } from "@/utils/constants";
import { useRouter } from "next/navigation";

const drawerWidth = 240;

const DRAWERS = [{
    label: 'Event',
    url: '/event'
}]

export default function CustomDrawer() {
    const t = useTranslations("menu");
    const [mobileOpen, setMobileOpen] = React.useState(false);
    const [isClosing, setIsClosing] = React.useState(false);
    const router = useRouter()

    const handleDrawerClose = () => {
        setIsClosing(true);
        setMobileOpen(false);
    };

    const handleDrawerTransitionEnd = () => {
        setIsClosing(false);
    };

    const handleDrawerToggle = () => {
        if (!isClosing) {
        setMobileOpen(!mobileOpen);
        }
    };

    const drawer = (
        <div>
            <List>
                {DRAWERS.map(({ label, url }, index) => (
                <ListItem key={label} disablePadding onClick={() => router.push(url)}>
                    <ListItemButton>
                    <ListItemIcon>
                    </ListItemIcon>
                    <ListItemText primary={label} />
                    </ListItemButton>
                </ListItem>
                ))}
            </List>
        </div>
    );

    return (
        <Box
            component="nav"
            sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
            aria-label="incutix"
        >
            <Drawer
                variant="persistent"
                sx={{
                    display: { xs: 'none', sm: 'block' },
                    '& .MuiDrawer-paper': { marginTop: HEADER_HEIGHT, boxSizing: 'border-box', width: drawerWidth },
                }}
                open
            >
                {drawer}
            </Drawer>
        </Box>
    );
}