import {Box, Grid, Typography } from '@mui/material';
import {Event} from '@/interface/IEventList';
import { format } from 'date-fns';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import AccessAlarmIcon from '@mui/icons-material/AccessAlarm';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { IconButton } from '@mui/material';

type Props = {
    event: Event
}

const LeftSplitPart = ({event}:Props) => {

    
    const titleStyle = {
        color:"rgba(91, 170, 100, 1)",
        mb:2
    }
    
    const textDecoration = {
        borderLeft: "5px solid rgba(91, 170, 100, 1)",
        borderRadius: "4px",
        paddingLeft: "10px",
    }  

    const boxStyle ={
        display:"flex",
        alignItems:"center"
    }

    const eventformattedStartDate = event.eventStartDate
    ? format(new Date(event.eventStartDate * 1000), "yyyy/MM/dd")
    : "N/A";

    const eventformattedEndDate = event.eventEndDate
        ? format(new Date(event.eventEndDate * 1000), "yyyy/MM/dd")
        : "N/A";

    const googleMapsUrl = `https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_EMBED_API_KEY}&q=${encodeURIComponent(event.Venue)}`;

    return(
        <>
          <Typography variant="h1" sx={{
            mb:3
          }}>{event.eventName}</Typography>
      <Grid container spacing={2}>
        <Grid item xs={6}>
        <Box sx={textDecoration}>
          <Typography variant="h2" sx={titleStyle}>活動資訊</Typography>
          </Box>
          <Box sx={boxStyle}>
          <IconButton>
            <CalendarTodayIcon/>
          </IconButton>
          <Typography variant="body1">{eventformattedStartDate} - {eventformattedEndDate}</Typography>
          </Box>
          <Box sx={boxStyle}>
          <IconButton>
            <AccessTimeIcon/>
          </IconButton>
          <Typography variant="body1">{event.eventTime}</Typography>
          </Box>
          <Box sx={boxStyle}>
          <IconButton>
            <AccessAlarmIcon/>
          </IconButton>
          <Typography variant="body1">{event.aboutTime} 分鐘</Typography>
          </Box>
          <Box sx={boxStyle}>
          <IconButton>
            <LocationOnIcon/>
          </IconButton>
          <Typography variant="body1">{event.Venue}</Typography>
          </Box>
        </Grid>
        <Grid item xs={6}>
            {/* 顯示 Google 地圖 */}
            <Box sx={{ mt: 2, mb: 2 }}>
                    <iframe
                        width="100%"
                        height="300"
                        style={{ border: 0 }}
                        loading="lazy"
                        allowFullScreen
                        src={googleMapsUrl}
                    ></iframe>
            </Box>
        </Grid>
      </Grid>

          <Box sx={textDecoration}>
          <Typography variant="h2" sx={titleStyle}>門票類型</Typography>
          </Box>
          <Box sx={textDecoration}>
          <Typography variant="h2" sx={titleStyle}>活動簡介</Typography>
          </Box>
          <Typography variant="body1">{event.description}</Typography>
          <Box sx={textDecoration}>
          <Typography variant="h2" sx={titleStyle}>常見問題</Typography>
          </Box>
          <Box sx={textDecoration}>
          <Typography variant="h2" sx={titleStyle}>條款及細則</Typography>
          </Box>
        </>
    )
}

export default LeftSplitPart;