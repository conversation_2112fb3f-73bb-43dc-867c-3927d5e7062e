"use client";
import type { NextPage } from "next";
import { Box, Checkbox, FormControlLabel, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import PublicPageContainer from "@/components/PublicPageContainer";
import SubmitButton from "@/components/buttons/SubmitButton";
import TextField from "@/components/input/TextField";
import FileInput from "@/components/input/FileInput";
import { useRouter, useSearchParams } from "next/navigation";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import xior from "xior";
import { SetupProfileSchema } from "@/schemas/SetupProfileSchema";
import { useEffect } from "react";
import { ROUTES } from "@/utils/constants";
import useFileUpload from "@/hooks/useFileUpload";

type FormValue = z.infer<typeof SetupProfileSchema>;

const SetupProfile: NextPage = () => {
  const t = useTranslations("setup_profile");
  const { uploadFileForRegister } = useFileUpload();
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get("email");
  const otp = searchParams.get("otp");

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
    setValue,
  } = useForm<FormValue>({
    defaultValues: { receivePromotions: false },
    resolver: zodResolver(SetupProfileSchema),
  });

  useEffect(() => {
    if (!email || !otp) {
      router.push(ROUTES.LOGIN);
    } else {
      setValue("email", email);
      setValue("otp", otp);
    }
  }, [email, otp, router, setValue]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const { profileImageFile, ...otherValues } = data;
      let photoUrl = "";
      if (profileImageFile)
        photoUrl = await uploadFileForRegister(profileImageFile, otp!, email!);

      await xior.post("/api/auth/create-account", { photoUrl, ...otherValues });
      router.push(ROUTES.LOGIN);
    } catch (e) {
      setError("confirmPassword", { message: "unknown_error" });
    }
  };

  return (
    <PublicPageContainer>
      <Typography variant="h1" sx={{ marginBottom: 3 }}>
        {t("title")}
      </Typography>
      <Box
        display={"flex"}
        flexDirection={"column"}
        component="form"
        style={{ width: "100%" }}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Box
          display={"flex"}
          flexDirection={"column"}
          alignSelf={"center"}
          alignItems={"center"}
        >
          <Controller
            name="profileImageFile"
            control={control}
            render={({ field }) => (
              <FileInput
                value={field.value}
                onChange={field.onChange}
                metadata={t("profile_image_metadata")}
                disabled={isSubmitting}
                error={errors.profileImageFile?.message}
                size="small"
              />
            )}
          />
          <Typography variant="body1" sx={{ marginBottom: 4 }}>
            {t("label_profile_image")}
          </Typography>
        </Box>
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting}
              placeholder={t("placeholder_name")}
              error={errors?.name?.message}
            />
          )}
        />
        <Controller
          name="password"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting}
              type="password"
              placeholder={t("placeholder_password")}
              error={errors?.password?.message}
            />
          )}
        />
        <Controller
          name="confirmPassword"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting}
              type="password"
              placeholder={t("placeholder_confirm_password")}
              error={errors?.confirmPassword?.message}
            />
          )}
        />
        <Controller
          name="receivePromotions"
          control={control}
          render={({ field }) => (
            <FormControlLabel
              value={field.value}
              onChange={field.onChange}
              label={t("subscribe_for_updates")}
              control={<Checkbox />}
            />
          )}
        />

        <SubmitButton sx={{ marginTop: 1.25, marginBottom: 3.5 }}>
          {t("button_continue")}
        </SubmitButton>
      </Box>
    </PublicPageContainer>
  );
};

export default SetupProfile;
