import React from "react";
import { Button, ButtonProps } from "@mui/material";
import { useTranslations } from "next-intl";
import DeleteIcon from "../icons/DeleteIcon";

const DeleteButton = (props: ButtonProps) => {
  const t = useTranslations("common");
  const { sx, ...otherProps } = props;
  return (
    <Button
      sx={{
        height: 36,
        width: 36,
        borderRadius: 18,
        minWidth: 0,
        // background: COLORS.GRAY,
        ["& .MuiButton-startIcon"]: {
          mx: 0,
        },
        ...sx,
      }}
      variant="contained"
      color="secondary"
      startIcon={<DeleteIcon sx={{ height: 16, width: 16 }} />}
      {...otherProps}
    />
  );
};

export default DeleteButton;
