import { encode, getToken } from "next-auth/jwt";
import { withAuth } from "next-auth/middleware";
import createIntlMiddleware from "next-intl/middleware";
import { NextRequest, NextResponse } from "next/server";
import { ROUTES } from "./utils/constants";
import { jwtDecode } from "jwt-decode";
import xior from "xior";

const locales = ["en", "zh"];
const publicPages = [
  "/login",
  "/sign-up",
  "/verify",
  "/forgot-password",
  "/reset-password",
  "/setup-profile",
  "/checkout",
  "/thankyou",
  "/featured-events",
  "/virtual-room",
  "/about-incutix",
  "/faqs",
  "/incutix/reset-password",
  "/event",
  "/section",
  "/sales-orders"
];

const SESSION_COOKIE = process.env.NEXTAUTH_URL?.startsWith("https://")
  ? "__Secure-next-auth.session-token"
  : "next-auth.session-token";

const isPublicPage = (pathname: string) => {
  const publicPathnameRegex = RegExp(
    `^(/(${locales.join("|")}))?(${publicPages
      .flatMap((p) => (p === "/" ? ["", "/"] : p))
      .join("|")})(/[^/]+)?/?$`,
    "i"
  );
  if (!pathname.includes('/api')) return true;
  return publicPathnameRegex.test(pathname);
};

const intlMiddleware = createIntlMiddleware({
  locales: locales,
  localePrefix: "as-needed",
  defaultLocale: "en",
});

const authMiddleware = withAuth(async (req) => intlMiddleware(req), {
  callbacks: {
    authorized: ({ token }) => token != null,
  },
  pages: {
    signIn: "/login",
  },
});

export default async function middleware(req: NextRequest) {
  if (req.nextUrl.pathname.startsWith("/ping")) {
    return Response.json({ message: "ok" }, { status: 200 });
  }

  if (req.nextUrl.pathname.startsWith("/api/auth")) {
    return;
  }

  if (isPublicPage(req.nextUrl.pathname)) {
    return intlMiddleware(req);
  }

  const isApiRoute = req.nextUrl.pathname.startsWith("/api");
  const token = await getToken({ req });

  if (token && Date.now() >= token.expiredAt * 1000) {
    // Token expired, try refresh token
    let response = isApiRoute ? NextResponse.next() : intlMiddleware(req);
    try {
      const {
        data: { accessToken, refreshToken },
      } = await xior.post(`${process.env.NEXT_PUBLIC_API_BASE}/auth/refresh-token`, {
        refreshToken: token.refreshToken,
      });

      const newSessionToken = await encode({
        secret: process.env.NEXTAUTH_SECRET!,
        token: {
          ...token,
          accessToken: accessToken,
          refreshToken: refreshToken,
          expiredAt: jwtDecode(accessToken).exp!,
        },
        maxAge: 30 * 24 * 60 * 60,
      });

      req.cookies.set(SESSION_COOKIE, newSessionToken);
      response.cookies.set(SESSION_COOKIE, newSessionToken, {
        httpOnly: true,
        expires: Date.now() + 1000 * 60 * 60 * 24 * 30,
        sameSite: "lax",
      });

      return response;
    } catch (error) {
      response.cookies.set(SESSION_COOKIE, "", { expires: new Date(0) });
      return isApiRoute ? response : NextResponse.redirect(new URL(ROUTES.LOGIN, req.url));
    }
  }

  if (!isApiRoute) {
    return (authMiddleware as any)(req);
  }
}

export const config = {
  // Skip all paths that should not be internationalized
  matcher: ["/((?!_next|.*\\..*).*)"],
};
