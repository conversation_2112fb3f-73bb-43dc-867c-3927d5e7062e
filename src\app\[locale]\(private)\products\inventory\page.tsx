"use client"
import ExportButton from "@/components/buttons/ExportButton";
import ImportButton from "@/components/buttons/ImportButton";
import PageHeader from "@/components/PageHeader";
import { Box, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import InventoryTable from "./components/InventoryTable";
import { IInventory } from "@/interface/IInventory";
import { useEffect, useState } from "react";
import xior from "xior";

const Inventory = () => {
    const t = useTranslations("inventory");
    const [getInventorylist, setGetInventory] = useState<IInventory[] | undefined>(undefined)


    const getAllInventoryApi = async () => {
        const response = await xior.get(`/api/inventory`)
        setGetInventory(response.data);
    }


    const getUpdateInventoryApi = async () => {
        const requestBody = {
        };

        try {

            const response = await xior.put("/api/inventory/getupdateInventory", requestBody);
            console.log("Inventory updated successfully", response.data);
        } catch (err) {
            console.error("Failed to call inventory API", err);

        } finally {
            await getAllInventoryApi();

        }
    };

    const renderGetAllInventoryList = () => {
        if (getInventorylist?.length === 0) {
            return (
                <>
                    <Typography>You haven&#39;t created any inventory record yet.</Typography>
                </>
            )
        }
    }

    console.log(getInventorylist)
    useEffect(() => {
        getUpdateInventoryApi();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);


    return (
        <>
            <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
                <PageHeader title={t("label_title")}>
                    <>
                        <ExportButton />
                        <ImportButton />
                    </>
                </PageHeader>
                <Box flex={1} padding="26px 34px">
                    {getInventorylist &&
                        <InventoryTable getInventorylist={getInventorylist} />
                    }
                    {
                        renderGetAllInventoryList()
                    }
                </Box>
            </Box>
        </>
    )
}

export default Inventory;