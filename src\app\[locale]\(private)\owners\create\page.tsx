"use client";
import PageHeader from "@/components/PageHeader";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import FileInput from "@/components/input/FileInput";
import SelectCountry from "@/components/input/SelectCountry";
import SelectGender from "@/components/input/SelectGender";
import SocialMediaInput from "@/components/input/SocialMediaInput";
import TextField from "@/components/input/TextField";
import useFileUpload from "@/hooks/useFileUpload";
import { OwnerSchema } from "@/schemas/OwnerSchema";
import { ROUTES, SOCIAL_MEDIAS } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import xior, { XiorError } from "xior";
import { NextPage } from "next";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import SocialNetworkInput from "@/components/input/SocialNetworkInput";
import AddNewButton from "@/components/buttons/AddNewButton";

type FormValue = z.infer<typeof OwnerSchema>;
const defaultValues = {
  gender: "",
  countryCode: "hk",
  introduction: "",
  photoUrl: "",
  name: "",
  email: "",
  socialMedias: [],
};

const CreateOwner: NextPage = () => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const router = useRouter();
  const { uploadFile } = useFileUpload();

  const [socialNetwork, setSocialNetwork] = useState<Array<any>>([])

  const {
    handleSubmit,
    control,
    formState: { isDirty, isSubmitting, errors },
    setError,
  } = useForm<FormValue>({
    // resolver: zodResolver(OwnerSchema),
    defaultValues,
  });

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const { photoUrl, socialMedias, ...otherData } = data;
      await xior.post("/api/owners", {
        ...otherData,
        gender: otherData?.gender || null,
        email: otherData?.email || null,
        photoUrl: await uploadFile(photoUrl, "owners"),
        socialMedias: socialMedias.map((item) => ({ method: item.method, url: item.value }))
      });
      await queryClient.invalidateQueries({ queryKey: ["owners"] });
      router.push(ROUTES.OWNER);
    } catch (e) {
      if (e instanceof XiorError) {
        switch (e.response?.data) {
          case "email_is_duplicated":
            setError("email", { message: t("email_is_duplicated") });
            break;
        };

        return;
      }

      setError("name", { message: "unknown_error" });
    }
  };

  const handleSocialNetworkOnChange = (value: any, field: any, idx: number, setter: Function) => {
    const cloneSocialNetwork: Array<any> = [...socialNetwork]

    cloneSocialNetwork[idx] = {
      ...cloneSocialNetwork[idx],
      [field]: value
    }

    setter(cloneSocialNetwork)
    setSocialNetwork(cloneSocialNetwork)
  }

  const removeSocialNetwork = (idx: number, setter: Function) => {
    const cloneSocialNetwork: Array<any> = [...socialNetwork]

    cloneSocialNetwork.splice(idx, 1)

    setter(cloneSocialNetwork)
    setSocialNetwork(cloneSocialNetwork)
  }

  const socialNetworkInputs = useMemo(() => {
    return (
      <>
        <label>{t("profile.label_social_media")}</label>
        <Controller
          key={`socialMedias`}
          name={`socialMedias` as any}
          control={control}
          render={({ field }) => (
            <>
              {
                socialNetwork.map((item, index) => (
                  <SocialNetworkInput
                    key={item.id}
                    socialNetwork={item as any}
                    value={field?.value?.[index] ?? {}}
                    index={index}
                    setter={field.onChange}
                    // onChange={handleSocialNetworkOnChange}
                    remove={removeSocialNetwork}
                    disabled={isSubmitting}
                    error={(errors?.socialMedias as any)?.[item]}
                  />
                ))
              }
            </>
          )}
        />
        <AddNewButton sx={{ width: 100 }} onClick={() => setSocialNetwork([...socialNetwork, {}])} />
      </>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [socialNetwork, control, errors?.socialMedias, isSubmitting, t]);

  return (
    <Box
      sx={{ height: "100%" }}
      display={"flex"}
      flexDirection={"column"}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
    >
      <PageHeader
        title={[t("owner.label_title"), t("owner.title_add_new_owner")]}
      >
        <>
          <CancelButton
            disabled={isSubmitting}
            onAction={() => router.push(ROUTES.OWNER)}
          />
          <SaveButton disabled={isSubmitting || !isDirty} />
        </>
      </PageHeader>
      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
        maxWidth={450}
      >
        <Controller
          name="photoUrl"
          control={control}
          render={({ field }) => (
            <FileInput
              value={field.value}
              onChange={field.onChange}
              metadata={t("file_upload.icon_metadata")}
              disabled={isSubmitting}
              error={errors.photoUrl?.message}
              type="image"
              size="small"
            />
          )}
        />
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              fullWidth={false}
              disabled={isSubmitting}
              label={t("owner.label_name")}
              error={errors?.name?.message}
              sx={{ width: 360 }}
            />
          )}
        />
        <Controller
          name="gender"
          control={control}
          render={({ field }) => (
            <SelectGender
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("owner.label_gender")}
              error={errors?.gender?.message}
              sx={{ width: 360 }}
            />
          )}
        />
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              fullWidth={false}
              disabled={isSubmitting}
              label={t("owner.label_email")}
              error={errors?.email?.message}
              sx={{ width: 360 }}
            />
          )}
        />
        <Controller
          name="introduction"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("owner.label_owner_introduction")}
              error={errors?.introduction?.message}
              multiline
              rows={8}
            />
          )}
        />
        <Controller
          name="countryCode"
          control={control}
          render={({ field }) => (
            <SelectCountry
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("owner.label_country")}
              error={errors?.countryCode?.message}
              width={360}
              fullWidth
            />
          )}
        />
        {socialNetworkInputs}
      </Box>
    </Box>
  );
};

export default CreateOwner;
