import { IOwner } from "@/interface/IOwner";
import { IListResponse } from "@/interface/IListResponse";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";
import {
  authorizedGet,
  authorizedPost,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";

async function GET(req: NextRequest) {
  try {
    const token = await getToken({ req });
    if (!token?.accessToken) throw Error("Unauthorized");

    const searchParams = req.nextUrl.searchParams;
    const data = await authorizedGet<IListResponse<IOwner>>(
      "/owners",
      await getAuthHeaders(req),
      {
        page: searchParams.get("page"),
        size: searchParams.get("size"),
        name: searchParams.get("keyword"),
        sortBy: searchParams.get("sortBy"),
        sortDirection: searchParams.get("sortDirection"),
      }
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function POST(req: NextRequest) {
  try {
    const token = await getToken({ req });
    if (!token?.accessToken) throw Error("Unauthorized");

    const { socialMedias, ...body } = await req.json();

    const data = await authorizedPost<IOwner>(
      "/owners",
      await getAuthHeaders(req),
      { ...body, socialUrls: socialMedias }
    );
    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET, POST };
