import {
  Order,
  setOrderStatus,
  setPage,
  setRowPerPage,
  setSearch,
  setSortOrder,
} from "@/redux/store/saleOrderListSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";

export const useSaleOrderListShowPerPage: () => [number, (value: string) => void] = () => {
  const saleOrder = useAppSelector((state) => state.saleOrder);
  const dispatch = useAppDispatch();

  return [Number(saleOrder.rowsPerPage), (value: string) => dispatch(setRowPerPage(value))];
};

export const useSaleOrderListSort: () => [
  { sortOrder: Order; sortOrderBy: string },
  (orderBy: string, dir: Order) => void
] = () => {
  const saleOrder = useAppSelector((state) => state.saleOrder);
  const { sortOrder, sortOrderBy } = saleOrder;
  const dispatch = useAppDispatch();

  const handleDispatch = (orderBy: string, dir: Order) => {
    dispatch(setSortOrder({ sortOrderBy: orderBy, sortOrder: dir }));
  };
  return [{ sortOrder, sortOrderBy }, handleDispatch];
};

// @ts-ignore
export const useSaleOrderListSearch: () => [string, (value: string) => void] = () => {
  const saleOrder = useAppSelector((state) => state.saleOrder);
  const dispatch = useAppDispatch();

  return [saleOrder.search ?? "", (value: string) => dispatch(setSearch(value))];
};

export const useSaleOrderListOrderStatus: () => [string, (value: string) => void] = () => {
  const saleOrder = useAppSelector((state) => state.saleOrder);
  const dispatch = useAppDispatch();

  return [saleOrder.orderStatus, (value: string) => dispatch(setOrderStatus(value))];
};

export const useSaleOrderListPage: () => [number, (value: number) => void] = () => {
  const saleOrder = useAppSelector((state) => state.saleOrder);
  const dispatch = useAppDispatch();

  return [Number(saleOrder.page), (value: number) => dispatch(setPage(value))];
};
