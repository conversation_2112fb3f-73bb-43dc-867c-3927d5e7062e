"use client";

import NavigateNextIcon from "@/components/icons/NavigateNextIcon";
import { Breadcrumbs, Link, styled, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

type SalesOrderBreadcrumbsProps = {
  orderNo: string;
};

// NOTE: Link
const StyledLink = styled(Link)(({ theme }) => ({
  cursor: "pointer",
  fontSize: 14,
  fontWeight: 700,
  color: theme.palette.incutix.grey[600],
}));
// NOTE: Text
const StyleText = styled(Typography)(({ theme }) => ({
  fontSize: 14,
  fontWeight: 700,
  color: theme.palette.incutix.grey[600],
}));

const GoToOrderDetailLink = (props: any) => {
  const router = useRouter();
  const { children } = props;

  return (
    <StyledLink underline="hover" href={"/sales-orders/order-list"} {...props}>
      {children}
    </StyledLink>
  );
};

const SalesOrderBreadcrumbs = ({ orderNo }: SalesOrderBreadcrumbsProps) => {
  const t = useTranslations("sales_orders.detail.breadcrumbs");

  const breadcrumbs = [
    <GoToOrderDetailLink key="1">{t("parent")}</GoToOrderDetailLink>,
    <StyleText key="3">{t("current", { orderNo })}</StyleText>,
  ];

  return (
    <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />} aria-label="breadcrumb">
      {breadcrumbs}
    </Breadcrumbs>
  );
};

export default SalesOrderBreadcrumbs;
