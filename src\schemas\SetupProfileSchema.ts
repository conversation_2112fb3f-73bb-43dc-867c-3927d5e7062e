import {
  ACCEPTED_IMAGE_TYPES,
  MAX_IMAGE_FILE_SIZE,
  SCHEMA_ERRORS,
} from "@/utils/constants";
import { z } from "zod";
import { ImageFileSchema } from "./ImageFileSchema";

const password = z.string().refine((value) => 
  /^(?=.*[A-Z])(?=.*[!@#$&*])(?=.*[0-9])(?=.*[a-z]).{8,20}$/.test(value ?? ""), {
    message: "Password should be 8-20 length and contains a combination of uppercase and lowercase letters, numbers and symbols",
  }
)

export const SetupProfileSchema = z
  .object({
    email: z.string({ required_error: SCHEMA_ERRORS.required }),
    otp: z.string({ required_error: SCHEMA_ERRORS.required }),
    name: z.string({ required_error: SCHEMA_ERRORS.required }),
    password,
    confirmPassword: password,
    receivePromotions: z.boolean(),
    profileImageFile: ImageFileSchema,
  })
  .refine((values) => values.password === values.confirmPassword, {
    message: SCHEMA_ERRORS.unmatchPassword,
    path: ["confirmPassword"],
  });
