import React from "react";
import { useTranslations } from "next-intl";
import { BaseButton } from "./styled";
import { COLORS } from "@/styles/colors";
import { Box, Typography } from "@mui/material";

type Props = {
  disabled?: boolean;
  label?: string;
  onAction: () => void;
  style?: React.CSSProperties;
};

const UpdateButton = (props: Props) => {
  const { disabled, label, onAction, style } = props;

  const t = useTranslations();
  return (
    <BaseButton
      disabled={disabled}
      onClick={onAction}
      sx={{ padding: "16px 26px", borderRadius: "999px", backgroundColor: COLORS.PRIMARY_1 }}
    >
      <Typography color={COLORS.WHITE}>{label || t("common.button_update")}</Typography>
    </BaseButton>
  );
};

export default UpdateButton;
