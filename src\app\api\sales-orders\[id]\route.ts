import { NextRequest} from "next/server";
import {SalesOrderDetailsDto} from "@/interface/ISalesOrderDtoDetails"
import {
    authorizedGet,
    getAuthHeaders,
    handleApiError,
} from "@/utils/api";

async function GET(req: NextRequest, { params }: { params: { id: string } }){
    try{

        const data = await authorizedGet<SalesOrderDetailsDto>(
            `/sales-orders/${params.id}`,
            await getAuthHeaders(req)
        );
        return Response.json(
            data,
            { status: 200 }
          );
    }catch(error){
        console.error(error); 
        return handleApiError(error);
    }
}

export { GET};