import {ICheckOutDto} from "@/interface/ICheckOutDto"
import {
  authorizedGet,
  handleApiError,
} from "@/utils/api";
import { cookies } from "next/headers";
import { NextRequest } from "next/server";

async function GET(req: NextRequest, { params }: { params: { token: string } }) {
    try{
        const data = await authorizedGet<ICheckOutDto>(
            `/checkout/${params.token}`,
            {}
        )
        if (data?.order?.jwt) {
            cookies().set("jwt", data.order.jwt, { secure: true })
        }
        return Response.json(data, { status: 200 });
    }catch (error) {
        console.log("error >> ", error)
        return handleApiError(error);
    }
}

export { GET };