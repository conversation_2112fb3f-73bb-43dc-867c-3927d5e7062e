"use client";
import type { NextPage } from "next";
import { Box, Divider, styled, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import { useRouter } from "next/router";
import Image from "next/image";
import { COLORS } from "@/styles/colors";
import dayjs from "dayjs";
import { generateDisplayableDate, generateDisplayDateDiff } from "@/utils/date";

const ImageContainer = styled(Box)(({}) => ({
    width: '312px',
    height: '175px',
    borderRadius: '8px',
    background: 'black'
}))

interface EventCardProps {
    eventName: string,
    createdAt: number,
    startDate: number,
    startTime: number,
    endDate: number,
    endTime: number,
    venue: string,
    id: number,
    regionCode: string,
    saleStartDateTime: string,
    saleEndDateTime: string,
    medias: { src: string }[],
    totalTicket: number
    onClick: () => void,
}

const EventCard = ({
    eventName,
    createdAt,
    startDate,
    startTime,
    endDate,
    endTime,
    venue = '-',
    id,
    regionCode,
    saleStartDateTime,
    saleEndDateTime,
    medias = [],
    totalTicket,
    onClick
} : EventCardProps) => {
  const t = useTranslations("event");

  const renderImageDisplayed = (src?: string) => (
    <ImageContainer>
        {
            src && (
                <Image
                    src={src}
                    alt={eventName}
                    width={312}
                    height={175}
                    layout="fixed"
                />
            )
        }
        
    </ImageContainer>
  )
  
  const renderContent = () => (
    <Box display={'flex'} flex={1} flexDirection={'column'} padding={'8px'} gap={'12px'}>
        <Box display={'flex'} flexDirection={'column'} gap={'12px'}>
            <Box
                display={'flex'}
                flexDirection={'row'}
                justifyContent={'space-between'}
                onClick={onClick}
            >
                <Typography color={'black'} fontWeight={'700'} fontSize={'18px'}>{eventName}</Typography>
                <Image
                    src={"/images/option.svg"}
                    width={30}
                    height={30}
                    alt={'more'}
                />
            </Box>
            <Box display={'flex'} flexDirection={'column'} gap={'8px'}>
                {
                    (startDate || endDate) && (
                        <Box display={'flex'} flexDirection={'row'} gap={'8px'}>
                            <Image
                                src={"/images/calendar.svg"}
                                width={24}
                                height={24}
                                alt={'date'}
                            />
                            <Typography variant="body1" color={COLORS.GREY_7}>
                                {
                                    [startDate, endDate]
                                    .filter(date => date)
                                    .map((date) => generateDisplayableDate(date))
                                    .join(" - ")
                                }
                            </Typography>
                        </Box>
                    )
                    
                }
                {
                    venue && (
                        <Box display={'flex'} flexDirection={'row'} gap={'8px'}>
                            <Image
                                src={"/images/pin.svg"}
                                width={24}
                                height={24}
                                alt={'location'}
                            />
                            <Typography variant="body1" color={COLORS.GREY_7}>
                                {venue}
                            </Typography>
                        </Box>
                    )
                }
            </Box>
        </Box>
        <Divider />
        <Box display={'flex'} flexDirection={'row'} justifyContent={'space-between'}>
            <Box display={'flex'} flexDirection={'row'} gap={'24px'}>
                <Typography variant="body2" fontWeight={'400'} color={COLORS.GREY_6}>
                    {t("created_on")}: 
                    {generateDisplayableDate(createdAt)}
                </Typography>
                <Typography variant="body2" fontWeight={'400'} color={COLORS.GREY_6}>
                    {t("ends_in")}: 
                    {generateDisplayDateDiff(endDate)}
                </Typography>
                <Typography variant="body2" fontWeight={'400'} color={COLORS.GREY_6}>
                    {t("selling_tickets")}: 
                    {totalTicket}
                </Typography>
            </Box>
            <Box padding={"3px 10px 5px 10px"} borderRadius={"999px"} color={COLORS.PRIMARY_3}>
                <Typography variant="body2" fontWeight={'bold'} color={COLORS.PRIMARY_1}>{regionCode}</Typography>
            </Box>
        </Box>
    </Box>
  )

  return (
    <Box
        display={'flex'}
        flexDirection={'row'}
        gap={'8px'}
        padding={'8px'}
        border={`1px solid ${COLORS.GREY_4}`}
        borderRadius={'16px'}
    >
        {
            renderImageDisplayed(medias?.[0]?.src)
        }
        {
            renderContent()
        }
    </Box>
  );
};

export default EventCard;
