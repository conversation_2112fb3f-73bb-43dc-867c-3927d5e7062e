import React, { useState } from 'react';
import TextField from '@mui/material/TextField';
import { useTranslations } from 'next-intl';
import InputAdornment from '@mui/material/InputAdornment';
import IconButton from '@mui/material/IconButton';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

interface CustomTextFieldProps {
    label: string;
    namespace: string;
    value?: string;
    name?: string;
    placeholder?: string;
    disabled?:boolean;
    type?: 'text' | 'password'; 
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const CustomTextField: React.FC<CustomTextFieldProps> = ({ label, namespace,placeholder, value, name , type = 'text' ,onChange }) => {
    const t = useTranslations(namespace); 
    const [showPassword, setShowPassword] = useState<boolean>(false);

    const handleClickShowPassword = () => {
        setShowPassword(!showPassword);
    };

    const handleMouseDownPassword = (event: React.MouseEvent<HTMLButtonElement>) => {
        event.preventDefault();
    };

    return (
        <TextField
            id="outlined-multiline-static"
            label={t(label)}
            value={value}
            name={name}
            type={type === 'password' && !showPassword ? 'password' : 'text'}
            placeholder={t(placeholder)}
            onChange={onChange}
            InputLabelProps={{
                shrink: true,
            }}
            InputProps={{
                endAdornment: type === 'password' && (
                    <InputAdornment position="end">
                        <IconButton
                            aria-label="toggle password visibility"
                            onClick={handleClickShowPassword}
                            onMouseDown={handleMouseDownPassword}
                            edge="end"
                        >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                    </InputAdornment>
                ),
            }}
            sx={{
                '& .MuiOutlinedInput-root': {
                    // '& fieldset': {
                    //     borderColor: 'rgba(79, 183, 71, 0.5)', 
                    // },
                    '&:hover fieldset': {
                        borderColor: 'rgba(79, 183, 71, 1)', 
                    },
                    '&.Mui-focused fieldset': {
                        borderColor: 'rgba(79, 183, 71, 1)', 
                    },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                    color: 'rgba(79, 183, 71, 1)', 
                },
            }}
        />
    );
};

export default CustomTextField;