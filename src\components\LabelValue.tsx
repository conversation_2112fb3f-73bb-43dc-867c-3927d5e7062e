import React, { useMemo } from "react";
import { Box, Chip, Switch, Typography } from "@mui/material";
import {
  defaultCountries,
  usePhoneInput,
  FlagImage,
} from "react-international-phone";
import { CONTACTS_METHODS, SOCIAL_MEDIAS } from "@/utils/constants";

interface BaseProps {
  type?: "text" | "country" | "phone";
  label: string;
  value?: string;
}

interface TagsProps {
  type?: "tags";
  label: string;
  value?: string[];
}

interface ImageProps {
  type: "image";
  label: string;
  src?: string;
  height?: number;
  width?: number;
  alt?: string;
}

interface SwitchProps {
  type: "switch";
  label: string;
  value?: boolean;
}

interface SocialMediaProps {
  label: string;
  type: "socialMedia" | "contacts";
  value?: Record<string, string>;
}

type Props =
  | BaseProps
  | TagsProps
  | ImageProps
  | SwitchProps
  | SocialMediaProps;

const CountryValue = ({ value }: { value?: string | null }) => {
  const { country } = usePhoneInput({
    defaultCountry: value || "hk",
    value: "",
    countries: defaultCountries,
  });

  return (
    <Box display={"flex"} flexDirection={"row"} alignItems="center">
      {value ? (
        <>
          <FlagImage iso2={country.iso2} height={16} width={16} />
          <Typography
            fontSize={"13px"}
            fontWeight={700}
            sx={{ color: "#000" }}
            ml={1}
          >
            {country.name}
          </Typography>
        </>
      ) : (
        "-"
      )}
    </Box>
  );
};

const PhoneValue = ({ value }: { value: string }) => {
  const { phone, country } = usePhoneInput({
    value,
  });

  return (
    <Typography fontSize={"13px"} sx={{ color: "#000" }}>
      {value
        ? phone.replace(`+${country.dialCode}`, `+${country.dialCode} `)
        : "-"}
    </Typography>
  );
};

const LabelValue = (props: Props) => {
  const renderValue = useMemo(() => {
    switch (props.type) {
      case "switch":
        return <Switch disabled value={props.value} />;
      case "image":
        const { height, width, src, alt } = props;
        if(!src) {
          return (
            <Typography fontSize={13} fontWeight={300}>
              {"-"}
            </Typography>
          )
        }
        return (
          <Box
            sx={{
              height,
              width,
              border: "0.5px solid #000000",
              borderRadius: "10px",
              overflow: "hidden",
            }}
          >
            <Box
              component={"img"}
              src={src!}
              height={height}
              width={width}
              alt={alt || ""}
              style={{ objectFit: "cover" }}
            />
          </Box>
        );
      case "country":
        return <CountryValue value={props.value || ""} />;
      case "phone":
        return <PhoneValue value={props.value || ""} />;
      case "tags":
        return (
          <Box>
            {props.value?.map((item) => (
              <Chip
                key={item}
                label={item}
                variant="outlined"
                sx={{ marginRight: 1 }}
              />
            ))}
          </Box>
        );
      case "contacts": {
        const values = [];
        for (const key in CONTACTS_METHODS) {
          if (props.value?.[key]) {
            values.push(
              <Box
                key={`contact-${key}`}
                display={"flex"}
                flexDirection={"row"}
                alignItems="center"
                mb={0.5}
              >
                <Box
                  component="img"
                  src={CONTACTS_METHODS[key].src}
                  height={16}
                  width={16}
                  sx={{ objectFit: "contain" }}
                />
                <Typography
                  fontSize={"13px"}
                  fontWeight={700}
                  sx={{ color: "#000" }}
                  ml={1}
                >
                  {props.value[key]}
                </Typography>
              </Box>
            );
          }
        }
        if (values.length === 0) {
          return (
            <Typography fontSize={13} fontWeight={300}>
              {"-"}
            </Typography>
          );
        }
        return values;
      }
      case "socialMedia":
        const values = [];
        for (const key in SOCIAL_MEDIAS) {
          if (props.value?.[key]) {
            values.push(
              <Box
                key={`social-media-${key}`}
                display={"flex"}
                flexDirection={"row"}
                alignItems="center"
                mb={0.5}
              >
                <Box
                  component="img"
                  src={SOCIAL_MEDIAS[key].src}
                  height={16}
                  width={16}
                />
                <Typography
                  fontSize={"13px"}
                  fontWeight={700}
                  sx={{ color: "#000" }}
                  ml={1}
                >
                  {props.value[key]}
                </Typography>
              </Box>
            );
          }
        }
        if (values.length === 0) {
          return (
            <Typography fontSize={13} fontWeight={300}>
              {"-"}
            </Typography>
          );
        }

        return values;
      case "text":
      default:
        return (
          <Typography fontSize={13} fontWeight={300} whiteSpace={"pre-wrap"}>
            {props.value || "-"}
          </Typography>
        );
    }
  }, [props]);

  return (
    <Box marginBottom={2}>
      <Typography
        fontSize={13}
        fontWeight={700}
        marginBottom={0.5}
        marginRight={0.5}
        component={"div"}
        display={"inline-block"}
      >
        {props.label}
      </Typography>
      {renderValue}
    </Box>
  );
};

export default LabelValue;
