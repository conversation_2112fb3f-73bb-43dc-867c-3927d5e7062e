"use client";
import * as React from "react";
import {
  TextField as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TextFieldProps,
  InputAdornment,
} from "@mui/material";
import Box from "@mui/material/Box";
import InputContainer, { InputContainerProps } from "./InputContainer";
import { SOCIAL_MEDIAS } from "@/utils/constants";

interface Props extends InputContainerProps {
  value: string;
  onChange: (value: string) => void;
  socialMedia:
    | "instagram"
    | "facebook"
    | "youtube"
    | "x"
    | "linkedin"
    | "tiktok"
    | "pinterest"
    | "other";
}

export default function SocialMediaInput({
  socialMedia,
  label,
  description,
  required,
  value,
  onChange,
  error,
  disabled,
  ...otherProps
}: Props & Omit<TextFieldProps, "error">) {
  return (
    <InputContainer label={label} error={error}>
      <Box display={"flex"} flexDirection={"row"}>
        <MuiTextField
          disabled
          value={SOCIAL_MEDIAS[socialMedia].label}
          sx={{
            mr: 1,
            width: 220,
            "& .MuiInputBase-input.Mui-disabled": {
              color: "#000",
              WebkitTextFillColor: "unset",
              fontWeight: 400,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: "#000 !important",
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Box
                  component="img"
                  src={SOCIAL_MEDIAS[socialMedia].src}
                  sx={{ height: 16, width: 16, objectFit: "contain" }}
                />
              </InputAdornment>
            ),
          }}
        />
        <MuiTextField
          disabled={disabled}
          sx={{ width: 480 }}
          value={value}
          onChange={onChange}
          placeholder={SOCIAL_MEDIAS[socialMedia].placeholder}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Box
                  component="img"
                  src={"/images/link.png"}
                  sx={{ height: 16, width: 16, objectFit: "contain" }}
                />
              </InputAdornment>
            ),
          }}
        />
      </Box>
    </InputContainer>
  );
}
