import type { NextPage } from "next";
import { Box, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import { COLORS } from "@/styles/colors";
import PageHeader from "@/components/PageHeader";

const Message: NextPage = () => {
  const t = useTranslations("message");

  return (
    <Box sx={{ height: "100%" }}>
      <PageHeader title={t("label_title")}></PageHeader>
    </Box>
  );
};

export default Message;
