import { ICollection } from "@/interface/ICollection";
import { authorizedGet, getAuthHeaders } from "@/utils/api";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";
import xior, { XiorError } from "xior";

async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const data = await authorizedGet<any>(
      `/collections/${params.id}`,
      await getAuthHeaders(req)
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    if (error instanceof XiorError) {
      return new Response(error.response?.data?.message, {
        status: error.response?.status,
      });
    }
    return new Response(`Unknown Error`, { status: 500 });
  }
}

async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const token = await getToken({ req });
    if (!token?.accessToken) throw Error("Unauthorized");

    const data = await req.json();
    const res = await xior.put(`/collections/${params.id}`, data, {
      headers: {
        Authorization: `Bearer ${token?.accessToken}`,
      },
      baseURL: process.env.NEXT_PUBLIC_API_BASE,
    });
    return Response.json({ ...res.data }, { status: 200 });
  } catch (error) {
    if (error instanceof XiorError) {
      return new Response(error.response?.data?.message, {
        status: error.response?.status,
      });
    }
    return new Response(`Unknown Error`, { status: 500 });
  }
}

async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const token = await getToken({ req });
    if (!token?.accessToken) throw Error("Unauthorized");

    await xior.delete(`/collections/${params.id}`, {
      headers: {
        Authorization: `Bearer ${token?.accessToken}`,
      },
      baseURL: process.env.NEXT_PUBLIC_API_BASE,
    });
    return Response.json({ status: "success" }, { status: 200 });
  } catch (error) {
    if (error instanceof XiorError) {
      return new Response(error.response?.data?.message, {
        status: error.response?.status,
      });
    }
    return new Response(`Unknown Error`, { status: 500 });
  }
}

export { GET, PUT, DELETE };
