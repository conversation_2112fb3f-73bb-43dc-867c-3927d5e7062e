import {
  authorizedGet,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { CONTACTS_METHODS, SOCIAL_MEDIAS } from "@/utils/constants";
import { NextRequest } from "next/server";

async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const data = await authorizedGet<any>(
      `/users/${params.id}`,
      await getAuthHeaders(req)
    );
    console.log("data >> ", data)

    const user: Record<string, any> = {
      contacts: {},
      socialMedias: {},
    };

    for (const key in data) {
      switch (key) {
        case "address":
          user.addressLine1 = data.address?.[0] || "";
          user.addressLine2 = data.address?.[1] || "";
          user.addressLine3 = data.address?.[2] || "";
          break;
        case "socialUrls":
          for (const socialMediaKey in SOCIAL_MEDIAS) {
            user.socialMedias[socialMediaKey] =
              data.socialUrls?.[socialMedia<PERSON><PERSON>]?.url || "";
          }
          break;
        case "contacts":
          for (const contact<PERSON>ey in CONTACTS_METHODS) {
            user.contacts[contactKey] =
              data.contacts?.[contactKey]?.value || "";
          }
          break;
        case "receivePromotions":
          user[key] = data[key];
          break;
        default:
          user[key] = data[key] || "";
      }
    }

    return Response.json(user, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET };
