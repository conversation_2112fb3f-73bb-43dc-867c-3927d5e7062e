"use client";
import React, { useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  PaginationState,
  SortingState,
  ColumnDef,
} from "@tanstack/react-table";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IMemberGroup } from "@/interface/IMemberGroup";
import debounce from "lodash.debounce";
import { IListResponse } from "@/interface/IListResponse";
import PageSelector from "@/components/PageSelector";
import DeleteMemberGroup from "./DeleteMemberGroup";
import EditMemberGroup from "./EditMemberGroup";
import PaginationTable from "@/components/PaginationTable";
import { SortOption } from "@/components/SortBySelector";

const MemberGroupTable = () => {
  const t = useTranslations("");
  const [sorting, setSorting] = React.useState<SortOption>({
    labelKey: "",
    id: "name",
    order: "asc",
  });
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const {
    data,
    isLoading
  } = useQuery({
    queryKey: ["member_groups", { pagination, sorting }],
    queryFn: async () =>
      xior
        .get<IListResponse<IMemberGroup>>("/api/member-groups", {
          params: {
            page: pagination.pageIndex + 1,
            size: pagination.pageSize,
            sortby: sorting.id,
            order: sorting.order,
          },
        })
        .then((res) => {
          return res.data;
        }),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  const columns = useMemo<ColumnDef<IMemberGroup>[]>(
    () => [
      {
        accessorKey: "id",
        header: "",
        cell: (data) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"center"}
            gap={1}
            mx={1}
            key={`member-group-${data.row.original.id}-actions`}
          >
            <EditMemberGroup group={data.row.original} />
            <DeleteMemberGroup group={data.row.original} />
          </Box>
        ),
      },
      {
        accessorKey: "name",
        header: t("member_group.label_member_group_name"),
      },
    ],
    [t]
  );

  const defaultData = React.useMemo<IMemberGroup[]>(() => [], []);

  const table = useReactTable({
    data: data?.items ?? defaultData,
    columns,
    rowCount: data?.count,
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
  });

  return (
    <Box display={"flex"} flexDirection={"column"} height={"100%"}>
      <Box display={"flex"} flexDirection={"row"} mb={3}>
        <Box flex={1} />
        <PageSelector
          value={pagination.pageSize}
          onChange={(newPageSize) => {
            table.setPageSize(newPageSize);
          }}
        />
      </Box>
      <Box flex={1}>
        <PaginationTable table={table} isLoading={isLoading} msg={t("member_group.title_empty")}/>
      </Box>
    </Box>
  );
};

export default MemberGroupTable;
