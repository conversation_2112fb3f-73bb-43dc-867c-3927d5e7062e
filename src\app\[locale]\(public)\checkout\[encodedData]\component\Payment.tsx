import { Box, Checkbox, FormControl, FormControlLabel, FormLabel, InputLabel, MenuItem, Radio, RadioGroup, Select, SelectChangeEvent, TextField, Typography } from "@mui/material";
import { useState } from "react";
import { IPaymentCreateDto } from '@/interface/IPaymentGateway';
import Image from "next/image";

type Props = {
  paymentCreateDto: IPaymentCreateDto;
  handlePaymentDto: (paymentCreateDto: IPaymentCreateDto) => void
}

const Payment = ({ paymentCreateDto, handlePaymentDto }: Props) => {

  const handleRadioGroupSelect = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent) => {
    const { name, value } = event.target;


    const updatedBillingAddress = [...paymentCreateDto.billingAddress];


    if (name === "billingAddress1") {
      updatedBillingAddress[0] = value || '';
    } else if (name === "billingAddress2") {
      updatedBillingAddress[1] = value || '';
    }

    handlePaymentDto({
      ...paymentCreateDto,
      [name]: value || '',
      billingAddress: updatedBillingAddress
    });
  }

  const textFieldStyle = {
    textAlign: 'left',
    marginBottom: '5px',
  };

  return (
    <>
      <Box sx={{ width: '100%', mt: "20px" }}>
        <Typography variant="h3">Payment Details</Typography><br />
        <FormControl>
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            defaultValue="Airwallex"
            name="paymentPlatform"
            onChange={handleRadioGroupSelect}
            value={paymentCreateDto.paymentPlatform}
          >
            <FormControlLabel
              value="Airwallex"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography>Credit Card</Typography>&nbsp;&nbsp;
                  <Image
                    src={"/images/Visa.png"}
                    alt="Visa"
                    width={52.5}
                    height={36}
                  />&nbsp;
                  <Image
                    src={"/images/Master.png"}
                    alt="Visa"
                    width={52.5}
                    height={36}
                  />
                </Box>
              }
            />
            <FormControlLabel
              value="AliPay"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography>AliPayHK</Typography>&nbsp;
                  <Image
                    src={"/images/AlipayHK.png"}
                    alt="Other"
                    width={52.5}
                    height={36}
                  />
                </Box>
              }
            />
            <FormControlLabel
              value="WeChatPay"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography>WeChat Pay</Typography>&nbsp;
                  <Image
                    src={"/images/WeChatPay.png"}
                    alt="Other"
                    width={52.5}
                    height={36}
                  />
                </Box>
              }
            />
          </RadioGroup>
        </FormControl>
      </Box>
      <Typography variant="h3">Billing Address</Typography><br />
      <Typography sx={{ mb: "10px", textAlign: 'left' }}>Country / Region</Typography>
      <FormControl fullWidth>
        {/* <InputLabel id="demo-simple-select-label">Select country or region</InputLabel> */}
        <Select
          labelId="demo-simple-select-label"
          id="demo-simple-select"
          name="billingAddressCountry"
          value={paymentCreateDto.billingAddressCountry}
          onChange={handleRadioGroupSelect}
        >
          <MenuItem value="hk">Hong Kong</MenuItem>
          {/* <MenuItem value="taiwan">Taiwan</MenuItem>
            <MenuItem value="singapore">Singapore</MenuItem> */}
        </Select>
      </FormControl><br /><br />
      <Typography sx={textFieldStyle}>Address Line 1</Typography>
      <TextField
        id="outlined-basic"
        // label="Address Line 1" 
        variant="outlined"
        fullWidth
        name="billingAddress1"
        value={paymentCreateDto.billingAddress[0] || ''}
        onChange={handleRadioGroupSelect}
      />
      <br /><br />
      <Typography sx={textFieldStyle}>Address Line 2</Typography>
      <TextField
        id="outlined-basic"
        // label="Address Line 2" 
        variant="outlined"
        fullWidth
        name="billingAddress2"
        value={paymentCreateDto.billingAddress[1] || ''}
        onChange={handleRadioGroupSelect}
      />
      <br /><br />
      <Typography sx={textFieldStyle}>Postal Code</Typography>
      <TextField
        id="outlined-basic"
        // label="Postal Code" 
        variant="outlined"
        fullWidth
        name="billingAddressPostalCode"
        onChange={handleRadioGroupSelect}
        value={paymentCreateDto.billingAddressPostalCode}
      />
    </>
  )
}

export default Payment;