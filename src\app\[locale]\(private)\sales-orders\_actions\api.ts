export const fetchData = async <T>(url: string, requestConfig?: RequestInit): Promise<T | null> => {
  try {
    const res = await fetch(url, requestConfig);

    if (!res.ok) {
      throw new Error(`API error: ${res.status} ${res.statusText}`);
    }
    const response: T = await res.json();

    return response;
  } catch (err) {
    const error = err instanceof Error ? err : new Error("Unknown API error");
    console.error(error);
    return null;
  }
};
