export interface EventList {
    events:        Event[];
    totalPages:    number;
    uniqueRegions: string[];
}

export interface Event {
    id:             number;
    eventName:      string;
    description:    string;
    eventStartDate: number;
    eventEndDate:   number;
    region:         string;
    timeZone:       string;
    eventTime:      string;
    aboutTime:      string;
    thumbnail:      string;
    Venue:          string;
    category:       string;
    createdAt:      number;
    updatedAt:      number | null;
    userId:         number;
}