import { Box, FormControl, FormControlLabel, FormLabel, Radio, RadioGroup, SelectChangeEvent, TextField, Typography } from "@mui/material";
import FileInput from "@/components/input/FileInput";
import { useEffect, useState } from "react";
import { ICollection } from "@/interface/ICollection";
import SelectCollection from "@/components/input/SelectCollection";
import { useTranslations } from "next-intl";
import { IOwner } from "@/interface/IOwner";
import SelectOwner from "@/components/input/SelectOwner";
import SelectCategory from "@/components/input/SelectCategory";
import { ITag } from "@/interface/ITag";
import SelectTag from "@/components/input/SelectTag";
import ProductImageTable from "./ProductImageTable";
import * as React from "react";
import {IProduct} from "@/interface/IProduct";
import useFileUpload from "@/hooks/useFileUpload";
import Image from "next/image";
import { IconButton } from '@mui/material';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
type Props ={
    addProductDto:IProduct
    handleAddProductDtoChange:(addProductDto:IProduct)=>void
}

const Information = ({addProductDto,handleAddProductDtoChange}:Props) => {
    const { uploadFile } = useFileUpload();
    const [file, setFile] = useState<File | string | undefined>(undefined);
    const [error, setError] = useState<string | undefined>(undefined);
    const [selectedCollections, setSelectedCollections] = useState<ICollection[]>([]);
    const [selectedOwner, setSelectedOwner] = useState<IOwner | null>(null);
    const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
    const [selectedSubCategory, setSelectedSubCategory] = useState<number | null>(null);
    const [tags, setTags] = useState<ITag[]>([]); 
    const [image, setImage] = useState<File | string | undefined>(undefined);
    const [productType, setProductType] = useState<number>(1);
    const [audioUrl, setAudioUrl] = useState<string>("");
    const isSmall = false;

    console.log("collection",selectedCollections)
    console.log("tags",tags)
    console.log("selectedOwner",selectedOwner)

    const handleAddProductDtoFormChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent) => {
        const { name, value } = event.target;

        // let updatedValue = value;

        // // 根據 name 更新 thumbnail 和 owner 的值
        // if (name === 'thumbnail') {
        //     updatedValue = image ? (typeof image === 'string' ? image : image.name) : ''; // 假設使用 image 的名稱作為 thumbnail
        // } else if (name === 'owner') {
        //     updatedValue = selectedOwner ? selectedOwner.id.toString() : ''; // 假設需要使用者的 ID 作為 owner
        // }

        handleAddProductDtoChange({
            ...addProductDto,
            [name]: value
          });
    }

    console.log("checking tags",tags)
    console.log("selectedCollections",selectedCollections)
        const handleImageChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
            const selectedFile = event.target.files?.[0];
            if (selectedFile) {
                const reader = new FileReader();
                reader.onload = () =>{
                    setImage(reader.result as string);
                }
                reader.readAsDataURL(selectedFile);
                try {
                    // 直接在文件选择后上传
                    const thumbnailUrl = await uploadFile(selectedFile, "productThumbnail");
                    console.log("Uploaded thumbnail URL:", thumbnailUrl);
                                    handleAddProductDtoChange({
                            ...addProductDto,
                            thumbnail: thumbnailUrl, // 将返回的 URL 设置为 thumbnail
                        });
                } catch (error) {
                    console.error("Upload failed:", error);
                }
            }
        };

        const removeFile = () => {
            setImage("");
            handleAddProductDtoChange({
                ...addProductDto,
                thumbnail: "" 
            });
        };

        const removeAudioFile = () => {
            setFile("");
            handleAddProductDtoChange({
                ...addProductDto,
                audio: ""
            });
        };
    // const handleFileChange = (newFile: File | undefined) => {
    //     if (newFile) {
    //         setFile(newFile);
    //         setError(undefined); 
    //         console.log("Check newFile",newFile)
    //     } else {
    //         setFile(undefined); 
    //     }
    // };

      const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) =>{
        const selectedFile = event.target.files?.[0];
        if (selectedFile) {
            const reader = new FileReader();
            reader.onload = () =>{
                setFile(reader.result as string);
            }
            reader.readAsDataURL(selectedFile);
            try {
                // 直接在文件选择后上传
                const audioUrl = await uploadFile(selectedFile, "audio file");
                console.log("Uploaded audio URL:", audioUrl);
                                handleAddProductDtoChange({
                        ...addProductDto,
                        audio: audioUrl, 
                    });
                    setAudioUrl(audioUrl);
            } catch (error) {
                console.error("Upload failed:", error);
            }
        }
      }

    const handleCollectionChange = (newCollections: ICollection[]) => {
        console.log('Check collection',newCollections)
        setSelectedCollections(newCollections);
        handleAddProductDtoChange({
            ...addProductDto,
            collections: newCollections 
        });
    };

    const handleOwnerChange = (newOwner: IOwner | null) => {
        setSelectedOwner(newOwner); 
        handleAddProductDtoChange({
            ...addProductDto,
            owner: newOwner ? newOwner.id.toString() : '' 
        });
    };

    const handleTagsChange = (newTags: ITag[]) => {
        setTags(newTags);
        handleAddProductDtoChange({
            ...addProductDto,
            tags: newTags  
        });
        }; 

    const handleCategoryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        // handleCategoryChange
        const categoryChange = Number(event.target.value); 
        setSelectedSubCategory(categoryChange);
        handleAddProductDtoChange({
            ...addProductDto,
            category: categoryChange // 直接更新 owner
        });
    };
    
    const handleProductTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newProductType = Number(event.target.value); // Convert to number
        setProductType(newProductType); // Update state with number
        handleAddProductDtoChange({
            ...addProductDto,
            productType: newProductType 
        });
    };
    // console.log(productType)
    const t = useTranslations();


    useEffect(() => {
        if (addProductDto.thumbnail) {
            setImage(addProductDto.thumbnail); 
        }
        if(addProductDto.audio){
            setFile(addProductDto.audio)
        }
        if(addProductDto.productType){
            setProductType(addProductDto.productType)
        }
        if(addProductDto.collections){
            setSelectedCollections(addProductDto.collections)
        }
        if(addProductDto.category){
            setSelectedSubCategory(addProductDto.category)
        }
        if(addProductDto.tags){
            setTags(addProductDto.tags)
        }
    }, [addProductDto]);

    return (
        <>
            <Typography variant="subtitle1">
                {t("product.label_name")}
            </Typography>
            <TextField
                name="name"
                id="outlined-size-small"
                size="small"
                placeholder={t("product.placeholder_name")}
                sx={{ width: '500px' }}
                value={addProductDto.name}
                onChange={handleAddProductDtoFormChange}
            />
            <Typography variant="subtitle1">
                {t("product.label_description")}
            </Typography>
            <TextField
                name="description"
                id="outlined-multiline-static"
                multiline
                rows={8}
                variant="outlined"
                placeholder={t("product.label_placeholder_description")}
                sx={{ width: '500px' }}
                value={addProductDto.description}
                onChange={handleAddProductDtoFormChange}
            />
            <Typography variant="subtitle1">
                {t("product.label_product_type")}
            </Typography>
            <FormControl>
                <RadioGroup
                    aria-labelledby="demo-radio-buttons-group-label"
                    defaultValue="physcialProduct"
                    value={productType}
                    name="productType"
                    onChange={handleProductTypeChange}
                    sx={{ display: "block" }}
                >
                    <FormControlLabel  value={1} control={<Radio />} label={t('product.label_physical_product')} />
                    <FormControlLabel  value={2} control={<Radio />} label={t('product.label_digital_product')} />
                </RadioGroup>
            </FormControl>
            <Typography variant="subtitle1">
                {t("product.label_product_audio")}
            </Typography>
            <Typography fontSize={12} color={'grey'}>
                {t("file_upload.audio_metadata").split("\n").map((item: string, index: number) => (
                    <span key={index}>{item}<br /></span> // 添加 key 属性
                ))}
            </Typography>
            {/* <FileInput
                metadata={t("file_upload.background_music_upload")}
                onChange={handleFileChange}
                value={file}
                error={error}
                type="audio"
                multiple={true}
                name="audio"
            /> */}
                        <Box
            display="flex"
            flexDirection={"column"}
            alignItems={"center"}
            width="50%"
            mb={1.5}
            >
            {
                file ? (
                    <Box
                        display="flex"
                        flexDirection={"column"}
                        alignItems={"center"}
                        width="100%"
                        mb={1.5}
                        sx={{
                            overflow: 'hidden',
                            mt:"10px"
                        }}
                    >
                        <audio controls src={audioUrl} style={{ width: '100%' }}>
                            Your browser does not support the audio element.
                        </audio>
                        <IconButton
                            aria-label="delete"
                            size="small"
                            sx={{
                                background: "#ff3b36",
                                height: 18,
                                width: 18,
                                position: "absolute",
                                right: isSmall ? -8 : 20,
                            }}
                            onClick={removeAudioFile}
                        >
                            <CloseRoundedIcon sx={{ fill: "white", height: 16, width: 16 }} />
                        </IconButton>
                    </Box>
                ) : (
                    <Box
                        display="flex"
                        flexDirection={"column"}
                        alignItems={"center"}
                        width="100%"
                        mb={1.5}
                    >
                        <div>
                            <label htmlFor="file-upload-listAudio" style={{ cursor: 'pointer' }}>
                                <Image
                                    height={36}
                                    width={36}
                                    src={file ? (file as string) : "/images/icon-upload.png"}
                                    alt="Upload audio"
                                />
                            </label>
                            <input
                                id="file-upload-listAudio"
                                type="file"
                                accept="audio/*" // Accept only audio files
                                onChange={handleFileChange}
                                style={{ display: 'none' }} // Hide file input
                            />
                        </div>
                        <Typography>Upload your audio</Typography>
                    </Box>
                )
            }
                                
           </Box> 
            <Typography variant="subtitle1">
                {t("product.label_collection")}
            </Typography>
            <SelectCollection
                placeholder={t("product.collection_description")}
                value={selectedCollections}
                onChange={handleCollectionChange}
                disabled={false}
            />
            <Typography variant="subtitle1">
                {t("product.label_owner")}
            </Typography>
            <SelectOwner
                placeholder={t("product.owner_description")}
                value={selectedOwner}
                onChange={handleOwnerChange}
                disabled={false}
            />
            <Typography variant="subtitle1">
                {t("product.label_category")}
            </Typography>
            <SelectCategory
                placeholder="选择类别"
                value={selectedSubCategory} 
                onChange={handleCategoryChange} 
                displayEmpty={true} 
                disabled={false} 
            />
            <Typography variant="subtitle1">
             {t("product.label_tag")}
            </Typography>
            <SelectTag
                value={tags}
                onChange={handleTagsChange}
                placeholder="Select or add tags"
                disabled={false}
            />
            <Typography variant="subtitle1">
            {t("product.label_list_of_thumbnail")}
            </Typography>
            <Typography fontSize={12} color={'grey'}>
                <i>
                  {
                    t("file_upload.image_video_metadata").split("\n").map((item: string) => (
                      <>{item}<br /></>
                    ))
                  }
                </i>
              </Typography>
              {/* <FileInput
                metadata={t("file_upload.thumbnail_multimedia_upload")}
                onChange={handleImageChange}
                value={image}
                error={undefined}
                type="image_video"
                multiple={true}
                name="thumbnail"
            /> */}
            <Box
            display="flex"
            flexDirection={"column"}
            alignItems={"center"}
            width="50%"
            mb={1.5}
            >
            {
               image? (<Box
               display="flex"
               flexDirection={"column"}
               alignItems={"center"}
               width="100%"
               mb={1.5}
               sx={{
                backgroundImage: `url(${image})!important`, // 替换成你的图片路径
                backgroundSize: 'cover', // 适应盒子大小
                backgroundPosition: 'center', // 图片居中
                height: '300px',
                overflow: 'hidden',
            }}
               >             <IconButton
               aria-label="delete"
               size="small"
               sx={{
                   background: "#ff3b36",
                   height: 18,
                   width: 18,
                   position: "absolute",
                //    bottom: isSmall ? -4 : 14,
                   right: isSmall ? -8 : 20,
               }}
               onClick={removeFile}
           >
               <CloseRoundedIcon sx={{ fill: "white", height: 16, width: 16 }} />
           </IconButton>
               </Box>):        ( <Box
                  display="flex"
                  flexDirection={"column"}
                  alignItems={"center"}
                  width="100%"
                  mb={1.5}
                >
              <div>
              <label htmlFor="file-upload-listImage" style={{ cursor: 'pointer' }}>
                  <Image
                      height={36}
                      width={36}
                      src={image ? (image as string) : "/images/icon-upload.png"}
                      alt="Upload image"
                  />
              </label>
              <input
                  id="file-upload-listImage"
                  type="file"
                  onChange={handleImageChange}
                  style={{ display: 'none' }} // 隐藏文件输入
              />
          </div>
          <Typography>Upload your Thumbnail image/video</Typography>
          </Box>)
            }
                    
           </Box>                 
          <Typography fontSize={14}>
            {t("product.label_product_asset")}
          </Typography>
          <Typography fontSize={12} color={'grey'}>
            <i>
              {
                t("file_upload.image_video_metadata").split("\n").map((item: string) => (
                  <>{item}<br /></>
                ))
              }
            </i>
          </Typography>
          {/* {
            addProductDto.productImage?.map((value)=>(
                <ProductImageTable addProductDto={value}/>
            ))
          } */}

        <ProductImageTable 
        addProductDto={addProductDto}
        handleAddProductDtoChange={handleAddProductDtoChange}
        />
        </>
    );
}

export default Information;