"use client";
import ModalContainer from "@/components/ModalContainer";
import AddNewButton from "@/components/buttons/AddNewButton";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import TextField from "@/components/input/TextField";
import { MemberGroupSchema } from "@/schemas/MemberGroupSchema";
import { COLORS } from "@/styles/colors";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Modal, Typography, Select, MenuItem } from "@mui/material";
import { useQueryClient, useQuery, keepPreviousData } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import * as React from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";
import CloseIcon from '@mui/icons-material/Close';
import { IListResponse } from "@/interface/IListResponse";
import { IMemberGroup } from "@/interface/IMemberGroup";
import { ChangePasswordSchema } from "@/schemas/ChangePasswordSchema";
import { InvitationSchema } from "@/schemas/InvitationSchema";

type FormValue = z.infer<typeof MemberGroupSchema>;

const SendInvitation = () => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const [groups, setGroups] = React.useState<Array<any>>([])
  const [selectedGroups, setSelectedGroups] = React.useState<Array<any>>([])
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
    reset,
  } = useForm<FormValue>({
    resolver: zodResolver(InvitationSchema),
    defaultValues: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const dataQuery = useQuery({
    queryKey: ["member_groups"],
    queryFn: async () =>
      xior
        .get<IListResponse<IMemberGroup>>("/api/member-groups",)
        .then((res) => {
          return res.data;
        }),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  React.useEffect(() => {
    if (dataQuery?.data?.items) {
      const {
        items = []
      } = dataQuery.data
      setGroups(items.map((item) => ({
        label: item.name,
        value: item.id
      })))
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataQuery?.data?.items, open])

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (isSubmitting) return;
    setOpen(false);
    setSelectedGroups([])
  }, [isSubmitting]);

  const handleOnChange = (event: any) => {
    const {
      value
    } = event.target
    setSelectedGroups([...new Set([
      ...selectedGroups,
      ...value
    ])])
  }

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const body = {
        ...data,
        groups: groups.map((group) => group.value)
      }
      await xior.post("/api/members/invitation", body);
      queryClient.invalidateQueries({ queryKey: ["member_groups"] });
      reset();
      setOpen(false);
      setSelectedGroups([])
    } catch (e) {
      setError("name", { type: "custom", message: "duplicated_member_group" });
    }
  };

  return (
    <Box>
      <AddNewButton onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("invitation.title_create_invitation")}
            </Typography>
          </Box>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                required
                value={field.value}
                onChange={field.onChange}
                disabled={isSubmitting}
                label={t("invitation.label_new_member_name")}
                error={errors?.name?.message}
              />
            )}
          />
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <TextField
                required
                value={field.value}
                onChange={field.onChange}
                disabled={isSubmitting}
                label={t("invitation.label_email")}
                error={errors?.email?.message}
              />
            )}
          />
          <Controller
            name="groups"
            control={control}
            render={({ field }) => (
              <Select
                multiple={true}
                disabled={isSubmitting}
                error={!!errors?.groups?.message}
                value={selectedGroups}
                onChange={handleOnChange}
              >
                {
                  groups.map((group) => (
                    <MenuItem key={group.id} value={group.value}>{group.label}</MenuItem>
                  ))
                }
              </Select>
            )}
          />
          <br />
          <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
            <CancelButton
              disabled={isSubmitting}
              onAction={handleClose}
            />
            <SaveButton disabled={isSubmitting} />
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default SendInvitation;
