import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { addToSelected, removeAtSelected, clearSelected } from "@/redux/store/saleOrderListSlice";

type SelectorSelectOrderType = [
  number[],
  {
    addTo: (orderId: number) => void;
    removeAt: (orderId: number) => void;
    clear: () => void;
  }
];

export const useSelectedOrder = (): SelectorSelectOrderType => {
  const selectedOrders = useAppSelector((state) => state.saleOrder.selectedOrders);
  const dispatch = useAppDispatch();

  const addTo = (orderId: number) => dispatch(addToSelected(orderId));
  const removeAt = (orderId: number) => dispatch(removeAtSelected(orderId));
  const clear = () => dispatch(clearSelected());

  return [selectedOrders, { addTo, removeAt, clear }];
};
