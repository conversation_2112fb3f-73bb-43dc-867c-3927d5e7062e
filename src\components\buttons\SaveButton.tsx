import React from "react";
import { useTranslations } from "next-intl";
import { BaseButton } from "./styled";

type Props = {
  disabled?: boolean;
  type?: "button" | "submit";
  label?: string;
  onAction?: () => void;
  style?: React.CSSProperties;
};

const SaveButton = (props: Props) => {
  const { disabled, type = "submit", label, onAction, style } = props;

  const t = useTranslations();
  return (
    <BaseButton
      disabled={disabled}
      variant="contained"
      type={type}
      onClick={onAction}
      sx={style}
    >
      {label || t("common.button_save")}
    </BaseButton>
  );
};
export default SaveButton;
