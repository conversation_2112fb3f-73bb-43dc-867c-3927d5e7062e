import { SCHEMA_ERRORS } from "@/utils/constants";
import { z } from "zod";

const password = z.string().refine((value) => 
  /^(?=.*[A-Z])(?=.*[!@#$&*])(?=.*[0-9])(?=.*[a-z]).{8,20}$/.test(value ?? ""), {
    message: "Password should be 8-20 length and contains a combination of uppercase and lowercase letters, numbers and symbols",
  }
)

export const ResetPasswordSchema = z
  .object({
    password,
    confirmPassword: password
  })
  .refine((values) => values.password === values.confirmPassword, {
    message: SCHEMA_ERRORS.unmatchPassword,
    path: ["confirmPassword"],
  });
