import { z } from "zod";
import { SCHEMA_ERRORS } from "@/utils/constants";
import { ImageFileSchema } from "./ImageFileSchema";
import { AudioFileSchema } from "./AudioFileSchema";
import { ImageVideoFileSchema } from "./ImageVideoFileSchema";

export const ProductSchema = z.object({
  name: z
    .string({ required_error: SCHEMA_ERRORS.required })
    .min(1, SCHEMA_ERRORS.required),
  description: z
    .string({ required_error: SCHEMA_ERRORS.required })
    .min(1, SCHEMA_ERRORS.required),
  isDigital: z.number(),
  audio: z.union([AudioFileSchema, z.string().nullish().optional()]),
  // thumbnail: z.object(z.union([ImageVideoFileSchema, z.string().nullish().optional()])),
  thumbnail: z.union([
    ImageVideoFileSchema,
    z.string().nullable().optional(), // Use nullable instead of nullish if you want to allow null
  ]),
  collections: z.number().array().nullish(),
  owner: z.number(),
  category: z.number(),
  tags: z
    .object({
      id: z.number(),
      name: z.string(),
    })
    .array()
    .nullish(),
  images: z.union([ImageVideoFileSchema, z.string().nullish().optional()]),
  status: z.number(),
  productType: z.number(),
  currency: z.string(),
  price: z.string(),
  bidUnit: z.string(),
  startDate: z.number(),
  endDate: z.number(),
  compareAt: z.string().optional(),
  taxIncluded: z.number(),
  tax: z.string().optional(),
  inventory: z.number(),
  weight: z.string().optional(),
  weightUnit: z.string().optional(),
  roomExcluded: z
    .object({
      id: z.number(),
      name: z.string(),
    })
    .array(),
  openToAllMembers: z.number(),
});
