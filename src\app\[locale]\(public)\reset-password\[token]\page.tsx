"use client";
import { useEffect, useState } from "react";
import { Box, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import PublicPageContainer from "@/components/PublicPageContainer";
import SubmitButton from "@/components/buttons/SubmitButton";
import TextField from "@/components/input/TextField";
import { ROUTES } from "@/utils/constants";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { ResetPasswordSchema } from "@/schemas/ResetPasswordSchema";
import { useRouter } from "next/navigation";

type FormValue = z.infer<typeof ResetPasswordSchema>;

interface Props {
  params: { token: string };
}

const ResetPassword = ({ params }: Props) => {
  const router = useRouter();
  const t = useTranslations("reset_password");
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    xior
      .post("/api/auth/verify-passcode", { resetPasswordToken: params?.token })
      .catch((e) => {
        router.push(ROUTES.LOGIN);
      });
  }, [params?.token, router]);

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
  } = useForm<FormValue>({ resolver: zodResolver(ResetPasswordSchema) });

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const res = await xior.post("/api/auth/reset-password", {
        resetPasswordToken: params?.token,
        password: data.password,
      });

      setSuccess(true);
    } catch (e) {
      setError("password", {
        type: "custom",
        message: "unknown_error",
      });
    }
  };

  if (success) {
    return (
      <PublicPageContainer>
        <Typography variant="h1" sx={{ marginBottom: 1 }}>
          {t("title")}
        </Typography>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ marginBottom: 2.75, textAlign: "center" }}
        >
          {t("reset_password_success")}
        </Typography>
        <SubmitButton href={ROUTES.LOGIN}>
          {t("button_continue_to_login")}
        </SubmitButton>
      </PublicPageContainer>
    );
  }

  return (
    <PublicPageContainer>
      <Typography variant="h1" sx={{ marginBottom: 1 }}>
        {t("title")}
      </Typography>
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{ marginBottom: 2.5, textAlign: "center" }}
      >
        {t("message")}
      </Typography>
      <Box
        component={"form"}
        style={{ width: "100%" }}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="password"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting}
              type="password"
              placeholder={t("placeholder_password")}
              error={errors?.password?.message}
            />
          )}
        />
        <Controller
          name="confirmPassword"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting}
              type="password"
              placeholder={t("placeholder_confirm_password")}
              error={errors?.confirmPassword?.message}
            />
          )}
        />
        <SubmitButton
          sx={{ marginTop: 1.25, marginBottom: 3.5 }}
          disabled={isSubmitting}
        >
          {t("button_update_password")}
        </SubmitButton>
      </Box>
    </PublicPageContainer>
  );
};

export default ResetPassword;
