"use client";
import * as React from "react";
import { useTranslations } from "next-intl";
import {
  CountrySelector,
  defaultCountries,
  usePhoneInput,
} from "react-international-phone";
import {
  TextField as MuiTextField,
  TextFieldProps,
  IconButton,
} from "@mui/material";
import Box from "@mui/material/Box";
import InputContainer, { InputContainerProps } from "./InputContainer";
import "react-international-phone/style.css";

interface Props extends InputContainerProps {
  fullWidth?: boolean;
  defaultCountry?: string;
  value: string;
  // onChange: (value: string) => void;
  onChange: TextFieldProps["onChange"];
}

export default function PhoneInput({
  label,
  description,
  required,
  defaultCountry = "hk",
  value,
  onChange,
  width,
  fullWidth,
  error,
  ...otherProps
}: Props & Omit<TextFieldProps, "error">) {
  // const { inputValue, handlePhoneValueChange, inputRef, country, setCountry } =
  //   usePhoneInput({
  //     defaultCountry,
  //     value,
  //     countries: defaultCountries,
  //     onChange: (data) => {
  //       onChange(data.phone);
  //     },
  //   });
  
  const t = useTranslations("profile");
  const { inputValue, handlePhoneValueChange, inputRef, country, setCountry } =
    usePhoneInput({
      defaultCountry,
      value,
      countries: defaultCountries,
      onChange: (data) => {
        if (onChange) {
          const event = {
            target: {
              value: data.phone,
            },
          } as React.ChangeEvent<HTMLInputElement>;
          onChange(event); // 传递事件对象
        }
      },
    });
    
    

  return (
    <InputContainer
      label={label}
      description={description}
      required={required}
      width={width}
      error={error}
      
    >
      <Box display={"flex"} flexDirection={"row"}>
        <CountrySelector
          selectedCountry={country.iso2}
          onSelect={(country) => setCountry(country.iso2)}
          renderButtonWrapper={({ children, rootProps }) => (
            <IconButton
              {...rootProps}
              color="primary"
              sx={{ mr: 2, height: "36px", width: "36px" }}
            >
              {children}
            </IconButton>
          )}
          dropdownStyleProps={{
            style: {
              zIndex: 2, 
            },
          }}
        />
        <MuiTextField
          label={t('label_phone_number')}
          value={inputValue}
          onChange={handlePhoneValueChange}
          inputRef={inputRef}
          fullWidth={fullWidth}
          error={!!error}
          {...otherProps}
          InputLabelProps={{
            shrink: true, 
          }}
          sx={{
                          '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                    borderColor: 'rgba(79, 183, 71, 1)', 
                },
                '&.Mui-focused fieldset': {
                    borderColor: 'rgba(79, 183, 71, 1)', 
                },
            },
            '& .MuiInputLabel-root.Mui-focused': {
                color: 'rgba(79, 183, 71, 1)', 
            },
          }}
        />
      </Box>
    </InputContainer>
  );
}
