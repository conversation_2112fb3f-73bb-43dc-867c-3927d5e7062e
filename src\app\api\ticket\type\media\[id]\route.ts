import { IListResponse } from "@/interface/IListResponse";
import { IMember } from "@/interface/IMember";
import {
  authorizedPut,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { FILE_FIELD_NAMES } from "@/utils/constants";
import { NextRequest } from "next/server";

async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const formData = await req.formData() // this is the formData from the client
    
    const data = await authorizedPut<IListResponse<IMember>>(
      `/api/admin/v1/ticket/type/media/${params.id}`,
    //   await getAuthHeaders(req),,
      {},
      formData
    );
    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { PUT };
