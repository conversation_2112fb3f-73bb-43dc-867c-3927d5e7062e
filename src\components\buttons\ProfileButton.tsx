import React from "react";
import { Button, ButtonProps } from "@mui/material";
import { useTranslations } from "next-intl";
import ProfileIcon from "../icons/ProfileIcon";

interface Props {
  iconOnly?: boolean;
}

const ProfileButton = (props: Props & ButtonProps) => {
  const t = useTranslations("common");
  const { iconOnly, sx, ...otherProps } = props;

  if (iconOnly) {
    return (
      <Button
        sx={{
          height: 36,
          width: 36,
          borderRadius: 18,
          minWidth: 0,
          ["& .MuiButton-startIcon"]: {
            mx: 0,
          },
          ...sx,
        }}
        variant="contained"
        startIcon={<ProfileIcon sx={{ height: 16, width: 16 }} />}
        {...otherProps}
      />
    );
  }
  return (
    <Button
      variant="contained"
      endIcon={<ProfileIcon sx={{ height: 14, width: 14 }} />}
      sx={sx}
      {...otherProps}
    >
      {t("button_edit")}
    </Button>
  );
};

export default ProfileButton;
