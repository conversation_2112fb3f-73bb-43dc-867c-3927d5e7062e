import { z } from "zod";
import {
  ACCEPTED_IMAGE_TYPES,
  MAX_IMAGE_FILE_SIZE,
  SCHEMA_ERRORS,
} from "@/utils/constants";

// export const ImageFileSchema = z
//   .instanceof(File)
//   .optional()
//   .refine(
//     (file) =>
//       file && file.size > 0 ? ACCEPTED_IMAGE_TYPES.includes(file.type) : true,
//     SCHEMA_ERRORS.invalidImageType
//   )
//   .refine(
//     (file) => (file ? file.size <= MAX_IMAGE_FILE_SIZE : true),
//     SCHEMA_ERRORS.imageFileSizeExceed
//   );

// export const ImageFileSchema = z.instanceof(File);
export const ImageFileSchema = z.string();
