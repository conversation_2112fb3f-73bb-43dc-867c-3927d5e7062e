"use client";
import styled from "@emotion/styled";
import { Button, ButtonProps } from "@mui/material";

const StyledButton = styled(Button)`
  height: 54px;
  background: #24378c;
  color: #fff;
  fontweight: 500;
  border-radius: 8px;
  text-transform: none;
`;

interface CustomProps extends ButtonProps {
  backgroundColor?: string
}

const SubmitButton = (props: CustomProps) => {
  return (
    <StyledButton
      fullWidth
      type="submit"
      variant="contained"
      color="primary"
      {...props}
      style={props.backgroundColor? {backgroundColor: props.backgroundColor}: {}}
    />
  );
};

export default SubmitButton;
