"use client";
import * as React from "react";
import { useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { Autocomplete } from "@mui/material";
import { IProductCategory } from "@/interface/IProductCategory";
import TextField from "./TextField";
import { InputContainerProps } from "./InputContainer";

interface Props extends InputContainerProps {
  placeholder?: string;
  helperText?: string;
  disabled?: boolean;
  value?: IProductCategory | null;
  onChange: (newValue: IProductCategory | null) => void;
  blacklist?: IProductCategory[];
}

export default function SelectParentCategory({
  label,
  helperText,
  placeholder,
  error,
  disabled,
  value,
  onChange,
  blacklist,
}: Props) {
  const dataQuery = useQuery({
    queryKey: ["productCategory"],
    queryFn: async () =>
      xior
        .get<IListResponse<IProductCategory>>("/api/product-category")
        .then((res) => ({
          items: res.data.items as IProductCategory[],
          total: res.data.count,
        })),
  });

  const data = React.useMemo(() => {
    return (
      dataQuery.data?.items?.filter(
        (item) => !blacklist?.find((bItem) => bItem.id === item.id)
      ) || []
    );
  }, [blacklist, dataQuery.data?.items]);

  return (
    <Autocomplete
      disabled={disabled}
      value={value}
      options={data}
      onChange={(event, newValue) => {
        onChange(newValue);
      }}
      getOptionLabel={(option) => option.name}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          helperText={helperText}
          error={error}
        />
      )}
    />
  );
}

// GET /product-category
// {
//   page: number;
//   size: size;
//   name: string;
// }

// {
//   count: number;
//   items: {
//     id: number;
//     name: string;
//     children: {
//       id: number;
//       name: string;
//       createdAt: string;
//     }[];
//     createdAt: string;
//   }[];
// }

// GET /product-category/:id
// {
//     id: number;
//     name: string;
//     children?: {
//       id: number;
//       name: string;
//       createdAt: string;
//     }[];
//     createdAt: string;
// }
