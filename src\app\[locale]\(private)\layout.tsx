import * as React from "react";
import { Box, Container } from "@mui/material";
import Header from "@/components/Header";
import SideBar from "@/components/Sidebar";

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <Box
      display={"flex"}
      flexDirection={"column"}
      minHeight={"100vh"}
      minWidth={"100vw"}
      sx={{ backgroundColor: "#FCFCFD" }}
    >
      <Header />
      <Box
        display={"flex"}
        flexDirection={"row"}
        overflow={"scroll"}
        sx={{ height: "calc(100vh - 65px)"}}
      >
        <SideBar />
        <Container maxWidth={false} disableGutters sx={{ maxWidth: "calc(100vw - 200px)", padding: "20px" }}>
          {children}
        </Container>
      </Box>
      {/* <Footer /> */}
    </Box>
  );
}
