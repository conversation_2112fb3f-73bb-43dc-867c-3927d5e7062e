"use client";
import * as React from "react";
import TextField from "./TextField";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { InputContainerProps } from "./InputContainer";
import { ICollection } from "@/interface/ICollection";

interface CollectionOption {
  inputValue?: string;
  name: string;
  id?: number;
}

const filter = createFilterOptions<CollectionOption>();

interface Props extends InputContainerProps {
  placeholder?: string;
  disabled?: boolean;
  onChange: (value: ICollection[]) => void;
  value?: ICollection[];
  defaultValue?: ICollection[];
}

export default function SelectCollection({ onChange, value, ...otherProps }: Props) {
  const dataQuery = useQuery({
    queryKey: ["collections"],
    queryFn: async () =>
      xior
        .get<IListResponse<ICollection>>("/api/collections")
        .then((res) => ({
            items: res.data.items as CollectionOption[],
            total: res.data.count,
        })),
  });

  return (
    <React.Fragment>
      <Autocomplete
        multiple
        freeSolo
        disableCloseOnSelect
        selectOnFocus
        clearOnBlur
        handleHomeEndKeys
        options={dataQuery.data?.items || []}
        // value={value}
        // onChange={(event, newValue) => {
        //     onChange(event)
        // }}
        value={value || []}
        onChange={(event, newValue) => {
          const filteredValues = newValue.filter((item): item is ICollection => typeof item !== "string");
          onChange(filteredValues);
        }}
        filterOptions={(options, params) => {
          const filtered = filter(options, params);
          if (params.inputValue !== "") {
            filtered.push({
              inputValue: params.inputValue,
              name: `Add "${params.inputValue}"`,
            });
          }

          return filtered;
        }}
        getOptionLabel={(option) =>
          typeof option === "string" ? option : option.inputValue || option.name
        }
        isOptionEqualToValue={(option, value) => option.name === value.name}
        renderOption={(props, option) => <li {...props}>{option.name}</li>}
        renderInput={(params) => <TextField {...params} {...otherProps} />}
      />
    </React.Fragment>
  );
}
