import {
  authorizedPut,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";

async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {

    const formDataFlag = await req.formData() // this is the formData from the client
    if (!formDataFlag) return;

    const files = formDataFlag.getAll('event');
    const removeIds = formDataFlag.getAll('removeIds') ?? [];
    const formData = new FormData()
    for (let x = 0; x < files.length; x++) {
      formData.append('event', files[x])
    }
    for (let x = 0; x < removeIds.length; x++) {
      formData.append('removeIds', removeIds[x])
    }
    const data = await authorizedPut<any>(
        `/api/admin/v1/event/media/${params.id}`,
        //   await getAuthHeaders(req)
        {
        },
        formData
    );
    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { PUT };
