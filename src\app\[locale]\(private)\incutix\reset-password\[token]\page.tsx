"use client"
import Typography from '@mui/material/Typography';
import CustomButton from "@/components/buttons/CustomButton";
import { useTranslations } from "next-intl";
import { ChangeEvent, useEffect, useState } from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Modal from '@mui/material/Modal';
import { IconButton, Link } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import CustomTextField from "@/components/input/CustomTextField";
import { ROUTES } from "@/utils/constants";
import {showErrorPopUp,showSuccessPopUp} from "@/utils/toast";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import xior from "xior";
import { useRouter } from "next/navigation";
import {
    checkPasswordLength,
    checkUppercase,
    checkLowercase,
    checkSpecial<PERSON>haracter,
    checkNumber,
  } from "@/utils/validators";



interface Props {
    params: { token: string };
  }

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 4,
};  

const passwordValidation = {
    display: "flex",
    color:"rgba(189, 189, 189, 1)"
  }

const textStyles = {
    color:"rgba(189, 189, 189, 1)",
  }


const ResetPassword = ({ params }: Props) =>{

    const t = useTranslations("reset_password");
    const router = useRouter();
    const [newPassword, setNewPassword] = useState<string>('');
    const [passwordConfirmation, setPasswordConfirmation] = useState<string>('');
    const { showToast: showSuccess } = showSuccessPopUp();
    const { showToast: showError } = showErrorPopUp();
    const [validations, setValidations] = useState({
        length: false,
        uppercase: false,
        lowercase: false,
        specialChar: false,
        number: false,
      });


    const handleBackToHomeBtn = () =>{
        router.push(ROUTES.HOME);
    }

    const handleNewPasswordChange = (event: ChangeEvent<HTMLInputElement>) =>{
        const newPassword = event.target.value;
        setNewPassword(newPassword);
        setValidations({
            length: checkPasswordLength(newPassword),
            uppercase: checkUppercase(newPassword),
            lowercase: checkLowercase(newPassword),
            specialChar: checkSpecialCharacter(newPassword),
            number: checkNumber(newPassword),
          });

    }

    const isFormValid = () => {
        return (
          validations.length &&
          validations.uppercase &&
          validations.lowercase &&
          validations.specialChar &&
          validations.number 
        );
      };


    const handlePasswordConfirmationChange = (event: ChangeEvent<HTMLInputElement>) =>{
        setPasswordConfirmation(event.target.value);
    }

    // console.log("newPassword",newPassword)
    // console.log("passwordConfirmation",passwordConfirmation)
    console.log("Token:", params.token);
    useEffect(() => {
        xior
          .post("/api/auth/verify-passcode", { resetPasswordToken: params?.token })
          .catch((e) => {
            router.push(ROUTES.HOME);
          });
      }, [params?.token, router]);

    
    const handleResetPasswordApi = async (event: any) =>{

        event.preventDefault();

        if(!newPassword){
            showError("New password is required.");
            return;
        }

        if(!passwordConfirmation){
            showError("Confirm new password is required.");
            return;
        }

        if (newPassword !== passwordConfirmation) {
            showError("Passwords do not match.");
            return;
        }

        try{

        const data = await xior.post('/api/auth/reset-password',{
            resetPasswordToken: params?.token,
            password: newPassword,
        }) 
        console.log("Password Reset Successfully!",data)
        showSuccess("Password Reset Successfully!");
        window.location.href = (ROUTES.HOME)
        }catch(err){
            console.log("reset password error",err)
            showError("reset password error.");
        }
    }  

    return(
        <>
        <Modal
        open={true}
        aria-labelledby="register-modal-title"
        aria-describedby="register-modal-description"
        sx={{
            backdropFilter: 'blur(4px)', 
            backgroundColor: 'rgba(122, 122, 123, 0.5)',
        }}
    >
        <Box sx={{ ...style, paddingTop: 2, paddingRight: 2, borderRadius:"24px" }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end'}}>
                        <IconButton onClick={handleBackToHomeBtn}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                    <Typography id="register-modal-title" variant="h2" component="h2">
                        {t('title')}
                    </Typography>
                    {/* <Typography>
                        {params.token}
                    </Typography> */}
                    <Box sx={{
                        display:'flex',
                        flexDirection:"column",
                        minWidth: 120,
                        gap: 2,
                        mt:2
                    }}>
                    <CustomTextField
                        namespace="reset_password"
                        label="label_password"
                        type="password"
                        placeholder="placeholder_password"
                        value={newPassword}
                        onChange={handleNewPasswordChange}
                    />
                    <CustomTextField
                        namespace="reset_password"
                        label="label_confirm_password"
                        type="password"
                        placeholder="placeholder_confirm_password"
                        value={passwordConfirmation}
                        onChange={handlePasswordConfirmationChange}
                    />
                    <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.length ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography >
                        8-20 characters
                        </Typography>
                    </Box>
                    <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.uppercase ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography sx={textStyles}>
                        An UPPERCASE letter
                        </Typography>
                    </Box>
                    <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.lowercase ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography sx={textStyles}>
                        A lowercase letter
                        </Typography>
                    </Box>
                    <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.specialChar ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography sx={textStyles}>
                            A special character
                        </Typography>
                    </Box>
                    <Box sx={passwordValidation}>
                        <CheckCircleIcon sx={{ color: validations.number ? '#ff7802' : 'gray' }} />&nbsp;
                        <Typography sx={textStyles}>
                            A number
                        </Typography>
                    </Box>
                    <CustomButton  disabled={!isFormValid()} onClick={handleResetPasswordApi} namespace="reset_password" label="button_confirm_and_return_to_home"/>
                    </Box>
                </Box>
                </Modal>
        </>
    )
}

export default ResetPassword;