"use client";
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { Button, Menu, MenuItem } from "@mui/material";
import { KeyboardArrowDown } from '@mui/icons-material';

export enum LANGUAGE {
  EN='en',
  ZH='zh'
}

const LanguageSwitcher = () => {
  const router = useRouter();
  const [currentLocale, setCurrentLocale] = useState('en');
  const [loading, setLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  useEffect(() => {
    const locale = window.location.pathname.startsWith('/zh') ? 'zh' : 'en';
    setCurrentLocale(locale);
  }, []);

  const LANGUAGE_OPTIONS: { label: string, value: LANGUAGE }[] = [{
    label: 'EN',
    value: LANGUAGE.EN
  }, {
    label: 'ZH',
    value: LANGUAGE.ZH
  }]

  const switchToOtherLanguage = (language: LANGUAGE) => {
    setLoading(true);
    handleClose()
    const pathWithoutLocale = window.location.pathname.replace(`/${currentLocale}`, '');
    window.location.href = `/${language}${pathWithoutLocale}`;
  }

  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Button
        onClick={handleClick}
        disabled={loading}
        endIcon={<KeyboardArrowDown />}
      >
        {loading ? 'Loading...' : currentLocale.toUpperCase()}
      </Button>
      <Menu
        id="simple-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
        >
          {
            LANGUAGE_OPTIONS.map(({ label, value }) => (
              <MenuItem key={label} onClick={() => switchToOtherLanguage(value)}>{label}</MenuItem>
            ))
          }
      </Menu>
    </>
  );
};

export default LanguageSwitcher;