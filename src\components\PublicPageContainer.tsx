import { Box } from "@mui/material";
import Image from "next/image";

const PublicPageContainer = (props: { children: React.ReactNode }) => {
  return (
    <Box display={"flex"} flexDirection={{ xs: 'column', sm: 'row' }}>
      <Box
        flex={1}
        display={"flex"}
        justifyContent={"center"}
        alignItems={"center"}
        sx={{
          minHeight: { xs: '50vh', sm: '100vh' },
          backgroundImage: "url('/images/bg-login.png')",
          backgroundSize: "cover",
        }}
        
      >
        <Image
          src="/images/logo-funverse.png"
          width={331}
          height={124}
          alt="Funverse logo"
        />
      </Box>
      <Box
        flex={1}
        display={"flex"}
        justifyContent={"center"}
        alignItems={"center"}
        sx={{
          padding: { xs: 2, sm: 0 }, 
        }}
      >
        <Box
          display={"flex"}
          flexDirection={"column"}
          justifyContent={"center"}
          alignItems={"center"}
          sx={{ maxWidth: "406px", width: "100%" }}
        >
          {props.children}
        </Box>
      </Box>
    </Box>
  );
};

export default PublicPageContainer;
