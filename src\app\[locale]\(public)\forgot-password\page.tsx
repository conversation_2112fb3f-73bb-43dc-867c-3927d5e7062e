"use client";
import type { NextPage } from "next";
import { useState } from "react";
import { Box, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import PublicPageContainer from "@/components/PublicPageContainer";
import TextField from "@/components/input/TextField";
import SubmitButton from "@/components/buttons/SubmitButton";
import { SignUpSchema } from "@/schemas/SignUpSchema";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";

type FormValue = z.infer<typeof SignUpSchema>;

const ForgotPassword: NextPage = () => {
  const t = useTranslations("forgot_password");
  const [email, setEmail] = useState("");

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
  } = useForm<FormValue>({ resolver: zodResolver(SignUpSchema) });

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      await xior.post("/api/auth/forgot-password", data);
      setEmail(data.email);
    } catch (e) {
      setError("email", { message: "unknown_error" });
    }
  };

  if (email) {
    return (
      <PublicPageContainer>
        <Typography variant="h1" sx={{ marginBottom: 1 }}>
          {t("check_inbox")}
        </Typography>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ marginBottom: 2.5, textAlign: "center" }}
        >
          {t.rich("reset_email_sent", {
            email: () => (
              <Typography component="span" color={"text.primary"}>
                {email}
              </Typography>
            ),
          })}
        </Typography>
      </PublicPageContainer>
    );
  }

  return (
    <PublicPageContainer>
      <Typography variant="h1" sx={{ marginBottom: 1 }}>
        {t("title")}
      </Typography>
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{ marginBottom: 2.5, textAlign: "center" }}
      >
        {t("message")}
      </Typography>
      <Box
        component={"form"}
        style={{ width: "100%" }}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting}
              placeholder={t("placeholder_email")}
              error={errors?.email?.message}
            />
          )}
        />
        <SubmitButton sx={{ marginTop: 1.25, marginBottom: 3.5 }}>
          {t("button_send_email")}
        </SubmitButton>
      </Box>
    </PublicPageContainer>
  );
};

export default ForgotPassword;
