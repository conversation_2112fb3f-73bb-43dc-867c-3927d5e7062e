import xior, { <PERSON>or<PERSON>rror } from "xior";
import { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
import { cookies } from "next/headers";

export async function getAuthHeaders(req: NextRequest, optional: boolean = false) {
  const token = await getToken({ req });

  let returnObj = {};
  if (token?.accessToken) returnObj = { Authorization: `Bearer ${token.accessToken}` }

  if (!token?.accessToken && !optional) {
    throw new Error("Unauthorized");
  }

  return returnObj
}

export async function getPaymentAuthHeaders(req: NextRequest) {
  const jwt = await cookies().get("jwt")
  if (!jwt) {
    throw new Error("Unauthorized")
  }

  return {
    Authorization: `Bearer ${jwt.value}`,
  }
}

export function handleApiError(error: unknown) {
  if (error instanceof XiorError) {
    return new Response(error.response?.data?.messages || "unknown_error", {
      status: error.response?.status,
    });
  }
  return new Response("unknown_error", { status: 500 });
}

async function request<T>(
  method: "get" | "post" | "put" | "delete",
  url: string,
  headers: Record<string, string>,
  body?: any,
  params?: Record<string, any>
): Promise<T> {
  try {
    const { data } = await xior.request<T>({
      method,
      url,
      params,
      data: body,
      headers,
      baseURL: process.env.NEXT_PUBLIC_API_BASE,
      cache: "no-cache",
    });
    return data;
  } catch (error) {
    if (error instanceof XiorError) {
      throw error; // Throw the XiorError to be handled by the caller
    }
    throw new Error("Unknown Error");
  }
}

export async function authorizedGet<T>(
  url: string,
  headers: Record<string, string>,
  params?: Record<string, any>
) {
  return request<T>("get", url, headers, undefined, params);
}

export async function authorizedPost<T>(
  url: string,
  headers: Record<string, string>,
  body: any
) {
  return request<T>("post", url, headers, body);
}

export async function authorizedPut<T>(
  url: string,
  headers: Record<string, string>,
  body: any
) {
  return request<T>("put", url, headers, body);
}

export async function authorizedDelete<T>(
  url: string,
  headers: Record<string, string>,
  body: any = {}
) {
  return request<T>("delete", url, headers, body);
}
