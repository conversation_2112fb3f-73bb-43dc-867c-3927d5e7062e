"use client";
import * as React from "react";
import { Box, Button, Modal, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { ICollection } from "@/interface/ICollection";
import { useRouter } from "next/navigation";
import { LANGUAGE_CODE, ROUTES } from "@/utils/constants";
import EditButton from "@/components/buttons/EditButton";
import TextEditor from "@/components/RichEditor";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";

interface Props {
    id?: number;
    content: string;
    language: LANGUAGE_CODE
}

const buttonStyle = {
  flex: 1,
  fontSize: "1rem",
  fontWeight: 700,
  minWidth: "132px",
  minHeight: "46px",
};

const EditTerms = ({ id, content, language }: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const t = useTranslations("event");
  const tCommon = useTranslations("common");
  const [open, setOpen] = React.useState(false);
  const [terms, setTerms] = React.useState('');

  React.useEffect(() => {
    setTerms(content)
  }, [content])

  const mutation = useMutation({
    mutationFn: () => {
        const url = ['/api/event/terms']
        if (id) url.push(`/${id}`)
        return xior.put(url.join(""), { content: terms, language })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["event"]});
      setOpen(false);
    },
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (mutation.isPending) return;
    setOpen(false);
  }, [mutation.isPending]);

  const handleUpdate = () => {
    mutation.mutate()
  }

  return (
    <Box>
      <EditButton
        onClick={handleOpen}
        isSmall={true}
        isPrimay={false}
      />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer sx={{ padding: '40px', gap: '32px' }}>
            <Typography variant="h3">{t("terms")}</Typography>
            <TextEditor
                value={terms}
                setValue={setTerms}
            />

            <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginTop: "30px" }}>
                <CancelButton onAction={handleClose} />
                <UpdateButton onAction={handleUpdate} />
            </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default EditTerms;
