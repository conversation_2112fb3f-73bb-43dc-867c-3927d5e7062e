"use client";
import * as React from "react";
import { <PERSON>, <PERSON><PERSON>, Card, Modal, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { keepPreviousData, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { ICollection } from "@/interface/ICollection";
import { useParams, useRouter } from "next/navigation";
import { AVAILABLE_DAY, CURRENCY, ROUTES, SALES_THRESHOLD, SUPPORT_CURRENCY } from "@/utils/constants";
import EditButton from "@/components/buttons/EditButton";
import TextEditor from "@/components/RichEditor";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";
import { DatePicker, DateTimePicker } from "@mui/x-date-pickers";
import Select from "@/components/input/Select";
import SelectDay from "@/components/input/SelectDay";
import SelectDate from "@/components/input/SelectDate";
import AddNewButton from "@/components/buttons/AddNewButton";
import Image from "next/image";
import TimeSlotTable, { TimeSlotData } from "../../component/TimeSlotTable";
import dayjs from "dayjs";
import _ from "lodash";
import { removeNull } from "@/utils/common";

export type TicketSection = (TicketSectionInfo & SectionAndQuantity)

type TicketSectionInfo = {
    day?: number|string,
    date?: number|string,
    seq?: number
}

export type TicketInventory = {
    id?: number,
    total: number,
    available: number,
    on_hold: number,
    updated_at: number,
    timestamp?: number
}

export type ThirdParty = {
    id: number,
    name: string
}   

export type TicketType = {
    third_party?: ThirdParty
}

type SectionAndQuantity = {
    id?: number,
    start_time: number,
    end_time: number,
    quantity: number,
    seq: number,
    ticket_date_inventory?: TicketInventory[],
    ticket_distribution?: {
        ticket_date_inventory: {
            id: number
            timestamp: number
        }
        ticket_distribution_adjustment: {
            amount: number
        }[]
        ticket_type: TicketType
    }[]
}

type SeqSection = {
    isDay?: boolean,
    isDate?: boolean,
    daySeq?: number,
    dateSeq?: number
}

enum SectionType {
    DAY= 'DAY',
    DATE= 'DATE'
}

const Section = () => {
    const queryClient = useQueryClient();
    const router = useRouter();
    const params = useParams();
    const t = useTranslations("ticket");
    const tCommon = useTranslations("common");
    const [open, setOpen] = React.useState(false);
    const [ticketSeq, setTicketSeq] = React.useState<{
        [seq: string]: number[]
    }>({
        1: []
    });
    const [updatedSeq, setUpdatedSeq] = React.useState<{
        [seq: string]: number[]
    }>({
        1: []
    });
    const [seqSection, setSeqSection] = React.useState<{
        [seq: string]: SeqSection
    }>({
        1: { isDay: true }
    });
    const [sectionTimeslot, setSectionTimeslot] = React.useState<{
        [seq: string]: TimeSlotData[]
    }>({
        1: []
    })
    const [dayOptions, setDayOptions] = React.useState(AVAILABLE_DAY)
    const [deleteTimeslot, setDeleteTimeslot] = React.useState<number[]>([]);

    const {
        data: eventInfo,
        refetch,
        isLoading
    } = useQuery({
        queryKey: ["event"],
        queryFn: async () => {
            return xior
            .get(`/api/event/${params.id}`)
        },
        placeholderData: keepPreviousData,
        retry: 3,
    });

    const {
        ticket_setting: ticketSetting = {},
    } = (eventInfo?.data?.data?.event ?? {})

    const {
        ticket_section: ticketSection = [],
    }: { ticket_section: TicketSection[] } = (ticketSetting ?? {})

    React.useEffect(() => {
        let parsedSeq: {[seq: string]: number[]} = {}
        let parsedSeqSecion: {[seq: string]: SeqSection} = {}
        let parsedSectionSlot: {[seq: string]: TimeSlotData[]} = {}
 
        if (ticketSection && ticketSection.length > 0) {
            let daySeq = 1, dateSeq = 1
            const sectionGroup = Object.groupBy(ticketSection, (item) => item.seq) as { [x: number]: TicketSection[]}
            Object.keys(sectionGroup).map((seqKey) => {
                sectionGroup[Number(seqKey)].map((section, idx) => {
                    const {
                        seq,
                        day,
                        date,
                        start_time: startTime,
                        end_time: endTime,
                        quantity,
                        id
                    } = section
                    const isDay = !!day || day === 0
                    const isDate = !!date

                    const sectionAndQuantity: TimeSlotData = {
                        startTime,
                        endTime,
                        quantity,
                        seq,
                        id,
                        idx
                    }
                    const value = isDay? Number(day): isDate? Number(date): undefined
                    const key = day || date as number
                    if (!value && value !== 0) return;
                    if (parsedSeq[`${seq}`]) {
                        const found = parsedSectionSlot[seq]
                            .findIndex((item) => item.startTime === startTime && item.endTime === endTime)
                        if (parsedSeq[seq].indexOf(value) === -1) parsedSeq[seq].push(value);
                        if (found > -1) {
                            if (parsedSectionSlot[seq]?.[found]?.id) {
                                parsedSectionSlot[seq][found].id[key] = id
                            }
                        } else {
                            parsedSectionSlot[seq].push({...sectionAndQuantity, id: { [key]: id as number }})
                        }
                        
                    } else {
                        const sectionInfo: SeqSection = {
                            isDay,
                            isDate,
                        }
                        if (seq !== 1 && day) {
                            sectionInfo.daySeq = daySeq
                            daySeq++
                        } 
                        if (seq !== 1 && date) {
                            sectionInfo.dateSeq = dateSeq
                            dateSeq++
                        }
                        parsedSeqSecion[seq] = sectionInfo
                        parsedSectionSlot[seq] = [{...sectionAndQuantity, id: { [key]: id as number }}]
                        parsedSeq[seq] = [value]
                    }
                })
            })
            setTicketSeq(parsedSeq)
            setUpdatedSeq(parsedSeq)
            setSeqSection(parsedSeqSecion)
            setSectionTimeslot(parsedSectionSlot)
        }
    }, [ticketSection])

    const handleSelectOptionControl = (target: {[x: string|number]: number[]}) => {
        const allValue = [...Object.values(target).flat(1)]
        const availableDay = AVAILABLE_DAY.filter((item) => !allValue.includes(item.value))
        setDayOptions(availableDay)
    }

    const mutation = useMutation({
        mutationFn: () => {
            const sections: any[] = []
            Object.keys(updatedSeq).map((seqKey: number| string) => {
                const {
                    isDate,
                    isDay
                } = seqSection[seqKey]
                sectionTimeslot[seqKey].map((timeslot) => {
                    const {
                        id: allIds = [],
                        startTime,
                        endTime,
                        quantity,
                        seq,
                    } = timeslot

                    let idToBeDeleted: number[] = []
                    if (typeof allIds === 'object') {
                        idToBeDeleted = Object.keys(allIds).map((day: any) => {
                            if (!updatedSeq[seqKey].includes(Number(day))) {
                                return allIds[day]
                            }
                            return undefined
                        }).filter(item => item !== undefined)
                    }
                    let parsedSection: any[] = []
                    if (allIds && typeof allIds === 'object' && Object.keys(allIds).length > 0) {
                        parsedSection = _.uniq([
                            ...Object.keys(allIds).map((val) => Number(val)),
                            ...updatedSeq[seqKey]
                        ]).map((val: any) => {
                            const day = Number(val)
                            const id = allIds[day]
                            return {
                                id,
                                startTime,
                                endTime,
                                quantity: Number(quantity),
                                seq,
                                ...(isDay? { day: day}: isDate? { date: day }: {}),
                                deleteSection: id && idToBeDeleted.includes(id)
                            }
                        })
                    } else {
                        parsedSection = updatedSeq[seqKey].map((val) => {
                            return {
                                val,
                                startTime,
                                endTime,
                                quantity: Number(quantity),
                                seq,
                                ...(isDay? { day: val}: isDate? { date: val }: {})
                            }
                        })
                    }
                    sections.push(...parsedSection)
                })
            })

            sections.push(...deleteTimeslot.map((item) => {
                const found = ticketSection.find((section) => section.id === item)
                if (!found) return
                const {
                    start_time: startTime,
                    end_time: endTime,
                    ...rest
                } = found
                const detail = removeNull(rest)
                return {
                    ...detail,
                    startTime,
                    endTime,
                    deleteSection: true
                }
            }).filter(item => item))

            const body = {
                ticketSettingId: Number(ticketSetting.id),
                sections
            }
            return xior.post('/api/ticket/section', body)
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["event"]});
            setOpen(false);
        },
    });

    const handleOpen = () => setOpen(true);

    const handleClose = React.useCallback(() => {
        if (mutation.isPending) return;
        router.back()
    }, [router, mutation.isPending]);

    const handleUpdate = () => {
        mutation.mutate()
    }

    const handleAddCustom = (type: SectionType) => {
        const maxSeq = Math.max(...Object.keys(ticketSeq).map(num => Number(num)))
        let isDay, isDate, daySeq, dateSeq;
        if (type === SectionType.DAY) {
            isDay = true
            const maxDaySeq = Math.max(...Object.values(seqSection).map(item => item.daySeq || 0))
            daySeq = (maxDaySeq || 0) + 1
        }
        if (type === SectionType.DATE) {
            isDate = true
            const maxDateSeq = Math.max(...Object.values(seqSection).map(item => item.dateSeq || 0))
            dateSeq = (maxDateSeq || 0) + 1
        }
        setTicketSeq({
            ...ticketSeq,
            [maxSeq + 1]: []
        })
        setUpdatedSeq({
            ...updatedSeq,
            [maxSeq + 1]: []
        })
        setSeqSection({
            ...seqSection,
            [maxSeq + 1]: {
                isDay,
                isDate,
                daySeq,
                dateSeq
            }
        })
        setSectionTimeslot({
            ...sectionTimeslot,
            [maxSeq + 1]: []
        })
    }

    const handleUpdateSection = (type: SectionType, seq: number|string, val: number[]) => {
        setUpdatedSeq({
            ...ticketSeq,
            ...updatedSeq,
            [Number(seq)]: val
        })
        handleSelectOptionControl({
            ...ticketSeq,
            ...updatedSeq,
            [Number(seq)]: val
        })
    }

    const addRow = (seqKey: number) => {
        const nextIdx = sectionTimeslot[seqKey]?.length || 0
        setSectionTimeslot({
            ...sectionTimeslot,
            [seqKey]: [
                ...sectionTimeslot[seqKey],
                {
                    idx: nextIdx,
                    seq: seqKey
                }
            ]
        })
    }

    const removeRow = (seqKey: number, idx: number, ids?: number[]) => {
        setSectionTimeslot({
            ...sectionTimeslot,
            [seqKey]: sectionTimeslot[seqKey].filter((item) => item.idx !== idx)
        })
    
        if (ids && typeof ids === 'object') {
            const idsToBeDeleted = Object.values(ids)
            setDeleteTimeslot([
                ...deleteTimeslot,
                ...idsToBeDeleted
            ])
        }
    }

    const handleOnTimeSlotChange = (seq: number, idx: number, key: string, val: any) => {
        let parsedValue = val
        if (key === 'startTime' || key === 'endTime') {
            const hour = dayjs(val).hour()
            const min = dayjs(val).minute()
            parsedValue = min * 60 + hour * 60 * 60
        }
        const clone = [...sectionTimeslot[seq]].map((item) => {
            if (item.idx === idx) {
                return {
                    ...item,
                    [key]: parsedValue
                }
            }
            return item
        })
        setSectionTimeslot({
            ...sectionTimeslot,
            [seq]: clone
        })
    }

    const renderDayTable = (seqKey: number) => (
        <Card key={`timeslot_${seqKey}`} sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <Box sx={{ display: 'flex', padding: '20px 20px 0px 20px', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    <Typography variant="body1" fontWeight={'bold'}>
                        {seqSection[seqKey].isDay && t("available_day") + `${
                            seqSection[seqKey].dateSeq? seqSection[seqKey].dateSeq: ""
                        }`}
                    </Typography>
                    <Typography variant="body3" color={COLORS.GREY_6}>
                        {
                            updatedSeq[seqKey]
                            ?.map((dateNum: number) => AVAILABLE_DAY.find(item => item.value === dateNum)?.label)
                            .join(", ")
                        }
                    </Typography>
                </Box>
                <Box
                    sx={{ padding: '14px 20px', border: `2px solid ${COLORS.GREY_3}`, borderRadius: '999px'}}
                    onClick={() => addRow(seqKey)}
                >
                    <Typography variant="body2" fontWeight={'bold'}>{tCommon("button_add")}</Typography>
                </Box>
            </Box>
            <TimeSlotTable
                rows={sectionTimeslot[seqKey]}
                handleOnTimeSlotChange={handleOnTimeSlotChange}
                removeRow={removeRow}
            />
            <Box sx={{ padding: "0px 20px 20px 20px"}}>
                <Typography variant="body2" fontWeight={"bold"} color={COLORS.GREY_6}>
                    {
                        `${t("total_qty")}: 
                        ${sectionTimeslot[seqKey].reduce((acc, next) => {
                            return acc + Number((next.quantity || 0))
                        }, 0) * (updatedSeq[seqKey]?.length ?? 0) }pcs
                        `
                    }
                </Typography>
            </Box>
        </Card>
    )

    const renderDateTable = (seqKey: number) => (
        <Card key={`timeslot_${seqKey}`} sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <Box sx={{ display: 'flex', padding: '20px 20px 0px 20px', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    <Typography variant="body1" fontWeight={'bold'}>
                        {seqSection[seqKey].isDate && t("custom_day") + `${
                            seqSection[seqKey].dateSeq? seqSection[seqKey].dateSeq: ""
                        }`}
                    </Typography>
                    <Typography variant="body3" color={COLORS.GREY_6}>
                        {
                            updatedSeq[seqKey]
                            ?.map((dateNum: number) => dayjs(dateNum* 1000).format('YYYY-MM-DD'))
                            .join(", ")
                        }
                    </Typography>
                </Box>
                <Box
                    sx={{ padding: '14px 20px', border: `2px solid ${COLORS.GREY_3}`, borderRadius: '999px'}}
                    onClick={() => addRow(seqKey)}
                >
                    <Typography variant="body2" fontWeight={'bold'}>{tCommon("button_add")}</Typography>
                </Box>
            </Box>
            <TimeSlotTable
                rows={sectionTimeslot[seqKey]}
                handleOnTimeSlotChange={handleOnTimeSlotChange}
                removeRow={removeRow}
            />
            <Box sx={{ padding: "0px 20px 20px 20px"}}>
                <Typography variant="body2" fontWeight={"bold"} color={COLORS.GREY_6}>
                    {
                        `${t("total_qty")}: 
                        ${
                            sectionTimeslot[seqKey].reduce((acc, next) => {
                                return acc + Number((next.quantity || 0))
                            }, 0)* (updatedSeq[seqKey]?.length ?? 0)
                        }pcs
                        `
                    }
                </Typography>
            </Box>
        </Card>
    )

    const removeSeq = (seqKey: number) => {
        const allIds = ticketSection
        .filter((item) => item.seq === seqKey && item.id)
        .map((item) => item.id)
        setDeleteTimeslot([
            ...deleteTimeslot,
            ...allIds.filter(item => item !== undefined) as number[]
        ])
        const clone = {...updatedSeq}
        delete clone[seqKey]
        setUpdatedSeq(clone)
    }

    return (
        <Box sx={{ display: 'flex', gap: '32px', flexDirection: 'row' }}>
            <Box sx={{ flex: 1 }}>
                <Typography variant="h3">{t("edit_section")}</Typography>

                <Card sx={{ display: 'flex', flexDirection: 'column', gap: '24px', padding: '20px' }}>
                    <Typography variant="h3">{t("available_day")}</Typography>
                    {
                        Object.keys(updatedSeq).map((seqKey) => {
                            if (seqSection && seqSection[seqKey]?.isDay) {
                                return (
                                    <SelectDay
                                        key={`day_selection_${seqKey}`}
                                        label={t("available_day") + `${
                                            seqSection[seqKey].daySeq? seqSection[seqKey].daySeq: ""
                                        }`}
                                        value={ticketSeq[seqKey]}
                                        options={dayOptions}
                                        customChange={(val) => handleUpdateSection(SectionType.DAY, seqKey, val)}
                                        seq={Number(seqKey)}
                                        removeDay={() => removeSeq(Number(seqKey))}
                                    />
                                )
                            }
                            if (seqSection && seqSection[seqKey]?.isDate) {
                                return (
                                    <SelectDate
                                        key={`date_selection_${seqKey}`}
                                        label={t("custom_day") + `${
                                            seqSection[seqKey].dateSeq? seqSection[seqKey].dateSeq: ""
                                        }` + ` (${t("custom_day_format")})`}
                                        value={ticketSeq[seqKey]}
                                        customChange={(val) => handleUpdateSection(SectionType.DATE, seqKey, val)}
                                        seq={Number(seqKey)}
                                        removeDate={() => removeSeq(Number(seqKey))}
                                    />
                                )
                            }
                        })
                    }
                    <Box sx={{ display: 'flex', gap: '12px' }}>
                        <AddNewButton
                            sx={{
                                padding: '14px 20px',
                                borderRadius: '999px',
                                border: `2px solid ${COLORS.GREY_3}`,
                                backgroundColor: COLORS.WHITE,
                                color: COLORS.GREY_8
                            }}
                            label={t("add_custom")}
                            onClick={() => handleAddCustom(SectionType.DATE)}
                        >
                            <Image src={"/images/icon-add.svg"} width={20} height={20} alt={"add"}/>
                        </AddNewButton>

                        <AddNewButton
                            sx={{
                                padding: '14px 20px',
                                borderRadius: '999px',
                                border: `2px solid ${COLORS.GREY_3}`,
                                backgroundColor: COLORS.WHITE,
                                color: COLORS.GREY_8
                            }}
                            label={t("add_custom_weekly")}
                            onClick={() => handleAddCustom(SectionType.DAY)}
                        >
                            <Image src={"/images/icon-add.svg"} width={20} height={20} alt={"add"}/>
                        </AddNewButton>
                    </Box>
                </Card>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px'}}>
                    <Typography variant="h3">{t("timeslot_and_quantity")}</Typography>
                    {
                        Object.keys(seqSection).map((seqKey) => {
                            if (seqSection[seqKey].isDay) {
                                return renderDayTable(Number(seqKey))
                            }
                            if (seqSection[seqKey].isDate) {
                                return renderDateTable(Number(seqKey))
                            }
                        })
                    }

                </Box>
            </Box>
            

            <Box sx={{ display: 'flex', flexDirection: 'column', width: '30%', maxWidth: '320px', gap: '32px' }}>
                <Box 
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        width: '100%', 
                        maxWidth: '280px',
                        padding: "16px 20px",
                        textAlign: 'center',
                        backgroundColor: COLORS.PRIMARY_4
                    }}
                >
                    <Typography
                        variant="body1"
                        fontWeight={"bold"}
                        color={COLORS.BLACK}
                        sx={{ marginTop: 'auto', marginBottom: 'auto' }}
                    >
                        {
                            `${t("total_qty")}:
                            `
                        }
                    </Typography>
                    <Typography variant="h3" fontWeight={"bold"} color={COLORS.PRIMARY_1}>
                        {
                            `${
                                Object.keys(updatedSeq).map((seq) => {
                                    const quantity = sectionTimeslot[seq].map((slot) => {
                                        const {
                                            quantity
                                        } = slot
                                        return quantity * updatedSeq[seq].length
                                    }).reduce((acc, next) => acc + next, 0)
                                    return quantity
                                }).reduce((acc, next) => acc + next, 0)
                            } pcs
                            `
                        }
                    </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    <CancelButton onAction={handleClose} />
                    <UpdateButton onAction={handleUpdate} />
                </Box>
            </Box>
        </Box>
    );
};

export default Section;
