import { NextRequest, NextResponse } from "next/server";
import { IPaymentCreateDto } from '@/interface/IPaymentGateway';
import {
    authorizedPost,
    getAuthHeaders,
    getPaymentAuthHeaders,
    handleApiError,
} from "@/utils/api";

 async function POST(req: NextRequest){
    try{
        const productDetail: IPaymentCreateDto = await req.json();
        const headers = {
            ...await getPaymentAuthHeaders(req),
            'Accept-Language': 'en' 
        };
        const response = await authorizedPost<IPaymentCreateDto>(
            "/payment",
            headers,
            productDetail
        )
    
        return NextResponse.json(response, { status: 200 });

    }catch(error){
        console.error(error); 
        return handleApiError(error);
    }
}

export {POST};