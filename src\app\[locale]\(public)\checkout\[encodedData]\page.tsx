"use client";
import { Container, Typography } from "@mui/material";
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Grid from '@mui/material/Grid';
import CheckoutStepper from "./component/CheckoutStepper"
import { Box } from '@mui/material';
import OrderSection from './component/OrderSection';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { ChekoutDto } from "@/interface/ILatestCheckOutResult"
import { useEffect, useState } from 'react';
import { IPaymentCreateDto } from '@/interface/IPaymentGateway';
import { ICheckOutDto } from "@/interface/ICheckOutDto";
import Accordion from '@mui/material/Accordion';
import AccordionActions from '@mui/material/AccordionActions';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import xior from "xior";

interface Props {
  params: {
    encodedData: string
  };
}

interface OrderInfo {
  product: {
    id: number
  },
  user: {
    id: number
  }
  quantity: number
}

const EncodedData = ({ params }: Props) => {

  // const [latestResulteDto, setLatestResulteDto] = useState<ChekoutDto| undefined>(undefined);
  const [finalAmount, setFinalAmount] = useState<number>(0);
  const [getPromoCode, setGetPromoCode] = useState<string>("");
  const [orderCode, setOrderCode] = useState<string>("");
  const [order, setOrder] = useState<OrderInfo>()
  const [paymentCreateDto, setPaymentCreateDto] = useState<IPaymentCreateDto | undefined>({
    product: [],
    paymentType: 'REDIRECT',
    paymentPlatform: 'Airwallex',
    feeType: 'USD',
    totalFee: 0,
    description: null,
    clientName: '',
    clientEmail: '',
    clientContactNumber: '',
    billingAddressCountry: 'hk',
    billingAddress: [],
    billingAddressPostalCode: '',
    deliveryAddressCountry: 'hk',
    deliveryAddress: [],
    deliveryAddressPostalCode: '',
    promoCode: '',
  });
  const [checkoutDto, setCheckoutDto] = useState<ICheckOutDto | undefined>(undefined);

  const getCheckOutTokenApi = async () => {
    try {
      const response = await xior.get(`/api/checkout/${orderCode}`)

      setCheckoutDto(response.data)

    } catch (error) {
      console.log('Response Data:', error);
    }
  }
  console.log("checkout order",orderCode)
  const getOrder = async () => {
    const order: any = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/checkout/${orderCode}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    if (order.status === 200) {
      setOrder(order.body)
    }
  }

  useEffect(() => {
    const code = decodeURIComponent(params.encodedData)
    if (code && code.length > 1) {
      setOrderCode(code)
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [])

  useEffect(() => {
    if (orderCode) {
      console.log("decod3d order code >> ", orderCode)
      getOrder()
      getCheckOutTokenApi();
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [orderCode])


  const handleFinalAmountChange = (finalAmount: number) => {
    setFinalAmount(finalAmount);
  }

  const handlePromoCodeChange = (getPromoCode: string) => {
    setGetPromoCode(getPromoCode);
  }

  if (!params || !orderCode) {
    return <Typography>Error: encodedData is missing</Typography>;
  }

  const handlePaymentDto = (paymentCreateDto: IPaymentCreateDto) => {
    setPaymentCreateDto(paymentCreateDto)
  }

  return (
    <>
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        flexDirection="column"
        sx={{
          height: {
            xs: 'auto', 
            sm: '40vh', 
          },
            mr:"50px",
            ml:"50px",
            mt: {
              xs: '0', 
              sm: '200px', 
            },
        }}
        >
        <Grid container spacing={2} sx={{
          mt: "2px"
        }}>
          <Grid item
            xs={12}
            sm={6}
            sx={{
              backgroundColor: {
                xs: "white", // 在移动设备上使用此背景色
                sm: "rgba(246, 249, 255, 1)", // 在 PC 上使用白色背景
              },
            }}>
            <Accordion
              sx={{
                display: {
                  xs: 'block', // 在移动设备上显示
                  sm: 'none',  // 在 PC 上隐藏
                },
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls="panel1-content"
                id="panel1-header"
              >
                <ShoppingCartIcon/> &nbsp; 1
              </AccordionSummary>
              <AccordionDetails
                sx={{
                  display: {
                    xs: 'block', // 在移动设备上显示
                    sm: 'block',  // 在 PC 上隐藏
                  },
                }}
              >
              {
                  paymentCreateDto && checkoutDto && (
                    <OrderSection 
                      checkoutDto={checkoutDto} 
                      handlePaymentDto={handlePaymentDto} 
                      paymentCreateDto={paymentCreateDto} 
                      handleFinalAmountChange={handleFinalAmountChange}
                      handlePromoCodeChange={handlePromoCodeChange}
                    />
                  )
              }
              </AccordionDetails>
            </Accordion>
            <Box
            sx={{
              display: {
                xs: 'none',  // 在移动设备上隐藏
                sm: 'block', // 在 PC 上显示
              },
            }}
          >
            {/* 你的内容 */}
            {paymentCreateDto && checkoutDto && (
              <OrderSection 
                checkoutDto={checkoutDto} 
                handlePaymentDto={handlePaymentDto} 
                paymentCreateDto={paymentCreateDto} 
                handleFinalAmountChange={handleFinalAmountChange}
                handlePromoCodeChange={handlePromoCodeChange}
              />
            )}
          </Box>
            </Grid>
            <Grid item 
            xs={12} 
            sm={6} 
            >
              {
                paymentCreateDto && checkoutDto && (
                <CheckoutStepper 
                checkoutDto={checkoutDto!} 
                handlePaymentDto={handlePaymentDto} 
                paymentCreateDto={paymentCreateDto} 
                finalAmount={finalAmount}
                getPromoCode={getPromoCode}
                />
              )
            }
          </Grid>
        </Grid>
      </Box>
    </>
  )
}

export default EncodedData;