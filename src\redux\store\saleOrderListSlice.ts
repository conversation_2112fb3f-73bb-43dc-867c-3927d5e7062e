import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export type Order = "asc" | "desc";

interface SaleOrderListState {
  rowsPerPage: string;
  sortOrder: Order;
  sortOrderBy: string;
  search?: string;
  orderStatus: string;
  page: number;
  selectedOrders: number[];
}

const initialState: SaleOrderListState = {
  rowsPerPage: "10",
  sortOrder: "desc",
  sortOrderBy: "orderId",
  orderStatus: "All",
  page: 1,
  selectedOrders: [],
};

export const saleOrderListSlice = createSlice({
  name: "sale-order",
  initialState,
  reducers: {
    setRowPerPage: (state, action: PayloadAction<string>) => {
      state.rowsPerPage = action.payload as string;
    },
    setSortOrder: (state, action: PayloadAction<{ sortOrder: Order; sortOrderBy: string }>) => {
      const { sortOrder, sortOrderBy } = action.payload;
      state.sortOrder = sortOrder;
      state.sortOrderBy = sortOrderBy;
    },
    setSearch: (state, action: PayloadAction<string>) => {
      state.search = action.payload;
    },
    setOrderStatus: (state, action: PayloadAction<string>) => {
      state.orderStatus = action.payload;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    addToSelected: (state, action: PayloadAction<number>) => {
      state.selectedOrders.push(action.payload);
    },
    removeAtSelected: (state, action: PayloadAction<number>) => {
      const set = new Set(state.selectedOrders);
      set.delete(action.payload);
      state.selectedOrders = [...set];
    },
    clearSelected: (state) => {
      state.selectedOrders = [];
    },
  },
});

export const {
  setRowPerPage,
  setSortOrder,
  setSearch,
  setOrderStatus,
  setPage,
  addToSelected,
  removeAtSelected,
  clearSelected,
} = saleOrderListSlice.actions;
export default saleOrderListSlice.reducer;
