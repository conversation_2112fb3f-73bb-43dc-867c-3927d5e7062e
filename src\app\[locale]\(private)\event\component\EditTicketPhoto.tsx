"use client";
import * as React from "react";
import { <PERSON>, Modal, TextField, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { useRouter } from "next/navigation";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";
import AddNewButton from "@/components/buttons/AddNewButton";
import MyButton from '@/components/buttons/CustomButton';
import EditButton from "@/components/buttons/EditButton";
import FileInput from "@/components/input/FileInput";
import { ACCEPTED_IMAGE_TYPES, ACCEPTED_VIDEO_TYPES } from "@/utils/constants";
import styled from "styled-components";

interface Props {
    handleChange: (files: File) => void
}

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  display: 'none',
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

const EditTicketPhoto = ({ handleChange }: Props) => {
    const inputRef = React.useRef<HTMLInputElement>(null);
    const t = useTranslations('common')

    const handleButtonClick = () => {
        if (!inputRef?.current) return;
        inputRef.current.click(); // Programmatically trigger the file input
    };

    const handleSetFiles = (file: File) => {
        if (!inputRef?.current) return;
        handleChange(file)
        inputRef.current.value = ''
    }

    return (
        <Box>
            <EditButton
                variant="outlined"
                isSmall={true}
                isPrimay={false}
                title={t("button_upload")}
                onClick={handleButtonClick}
            />
             <VisuallyHiddenInput
                ref={inputRef}
                type="file"
                multiple={false}
                onChange={(event: any) => handleSetFiles(event?.target?.files?.[0])}
                accept={[...ACCEPTED_IMAGE_TYPES, ...ACCEPTED_VIDEO_TYPES].join(", ")}
            />
        </Box>
    );
};

export default EditTicketPhoto;
