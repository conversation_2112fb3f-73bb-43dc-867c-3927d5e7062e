import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Modal from '@mui/material/Modal';
import CloseIcon from '@mui/icons-material/Close';
import { Checkbox, Divider, FormControlLabel, IconButton, Link } from "@mui/material";
import { Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import Accordion from '@mui/material/Accordion';
import AccordionActions from '@mui/material/AccordionActions';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {OrderDetail} from "@/interface/IClientOrderDetails";
import {  useState } from 'react';
import CircularProgress from '@mui/material/CircularProgress';
import xior from 'xior';
import { Chip, Stack } from '@mui/material';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 600,
    bgcolor: 'background.paper',
    // border: '2px solid #000',
    boxShadow: 24,
    p: 4,
    maxHeight: "70vh", 
    overflowY: "auto",  
  };

  const Item = styled(Paper)(({ theme }) => ({
    backgroundColor: '#fff',
    ...theme.typography.body2,
    padding: theme.spacing(1),
    // textAlign: 'center',
    color: 'dark',
    boxShadow: 'none',
    ...theme.applyStyles('dark', {
      backgroundColor: '#1A2027',
    }),
  }));  

 type Props ={
    orderId:number
 } 

const RecordOrderDetailsModal = ({orderId}:Props) =>{
    const [open, setOpen] = React.useState(false);
    const handleClose = () => setOpen(false);
    const [orderDetailsDto,setOrderDetailsDto] = useState<OrderDetail | undefined>(undefined);

    const handleOpen = () => {
        setOpen(true)
        handleGetOrderDetailsApi()
    };

    const handleGetOrderDetailsApi = async () =>{

        try{

            const response = await xior.get(`/api/clientorder/${orderId}`)

            setOrderDetailsDto(response.data)

            console.log("get client order details successfully", response.data)
        }catch(err){
            console.log("get client order details ",err);
        }
    }

    // console.log("client order details", orderDetailsDto)
  
    const textStyles = {
        fontSize: '0.8rem',
        mb:1,
        color:"rgba(119, 126, 144, 1)"
    }

    const orderDetailsStyle ={
        backgroundColor: 'rgba(244, 245, 246, 1)',
        padding:1,
        mb:1,
        borderRadius: "4px"
    }

    const boldBlackText = {
        color:"black",
        fontWeight: 'bold'
    }

    const blackText ={
        color:"black",
    }

    const boxSpaceBetween = {
        display: "flex",
        justifyContent: "space-between"
    }

    const redText = {
        color:"rgba(245, 34, 45, 1)",
    }

    const renderDeliveryStatus = () =>{
        if(orderDetailsDto?.deliveryStatus?.includes("pending")){
            return(
                <>
                <Stack direction="row" spacing={1}>
                <Chip label="處理中"  
                    sx={{
                        backgroundColor: 'rgba(255, 240, 228, 1)', 
                        color: 'rgba(244, 128, 31, 1)', 
                        borderColor:"rgba(255, 240, 228, 1)"
                      }}
                />
                </Stack>
                </>
            )
        }else if(orderDetailsDto?.deliveryStatus?.includes("delivered")){
            return(
                <>
                <Stack direction="row" spacing={1}>
                <Chip label="已派發"  
                    sx={{
                        backgroundColor: 'rgba(244, 245, 246, 1)', 
                        color: 'rgba(119, 126, 144, 1)', 
                        borderColor:"rgba(244, 245, 246, 1)"
                      }}
                />
                </Stack>
                </>
            )
        }
    }

    const renderOrderBalance = () =>{

        const totalPrice = Number(orderDetailsDto?.totalPrice);
        const discount = Number(orderDetailsDto?.promoCode.discount);
        
        if(orderDetailsDto?.promoCode.code){
            const finalPrice = totalPrice - discount;
            return(
                <>
                {finalPrice.toFixed(2)}
                </>
            )
        }else{
            return(
                <>
                {totalPrice.toFixed(2)}
                </>
            )
        }
    }

    return (
      <div>
        <Button onClick={handleOpen}>查看詳情</Button>
        <Modal
          open={open}
        //   onClose={handleClose}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
          sx={{
            backdropFilter: 'blur(4px)', 
            backgroundColor: 'rgba(122, 122, 123, 0.5)',
        }}
        >
     
          <Box sx={{...style, borderRadius:"24px" }}>
          {  orderDetailsDto ? 
          <Box id="loadingProgress">
          <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'end'              
                }}>
                <IconButton onClick={handleClose}>
                    <CloseIcon />
                </IconButton>
            </Box>
            <Typography variant="h2">訂單詳情</Typography>
            <Box sx={{ flexGrow: 1,mt:2 }}>
                <Grid container >
                <Grid item xs={4}>
                    <Item>
                        <Box>
                            <Typography sx={textStyles}>訂單號碼: <span style={boldBlackText}>{orderDetailsDto.orderNumber}</span></Typography>
                        </Box>
                    </Item>
                </Grid>
                <Grid item xs={4}>
                    <Item>
                        <Box>
                            <Typography sx={textStyles}>下單時間: <span style={boldBlackText}>{orderDetailsDto.deliveryDetails.paidTime}</span></Typography>
                        </Box>
                    </Item>
                </Grid>
                <Grid item xs={4}>
                    <Item>
                        <Box>
                            <Typography sx={textStyles}>{renderDeliveryStatus()}</Typography>
                        </Box>
                    </Item>
                </Grid>
                <Grid item xs={4}>
                    <Item>
                        <Box>
                            <Typography sx={textStyles}>支付方式: <span style={boldBlackText}>{orderDetailsDto.paymentMethod}</span></Typography>
                        </Box>
                    </Item>
                </Grid>
                <Grid item xs={4}>
                    <Item>
                        <Box>
                            <Typography sx={textStyles}>共 {orderDetailsDto.quantity} 件貨品, 訂單總額 &nbsp; <span style={boldBlackText}>{orderDetailsDto.currency}{orderDetailsDto.totalPrice}</span></Typography>
                        </Box>
                    </Item>
                </Grid>
                <Grid item xs={4}>
                    <Item></Item>
                </Grid>
                </Grid>
           </Box>
           <Divider sx={{mt:2,mb:2}}></Divider>
           <Accordion>
                <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls="panel1-content"
                id="panel1-header"
                >
                <Typography component="span"><span style={boldBlackText}>商品詳情</span></Typography>
                </AccordionSummary>
                <AccordionDetails>
                <Typography variant="subtitle1" sx={{mb:2}}><span style={boldBlackText}>鏈鋸人動畫展 - 香港</span></Typography>
                    <Box sx={orderDetailsStyle}>
                        <Typography sx={textStyles}><span style={blackText}>2025/1/18 - {orderDetailsDto.productName}</span></Typography>
                        <Typography sx={textStyles}>時間段:10:00-12:00, 指定入場時間段: 11:00-11:30</Typography>
                        <Box sx={boxSpaceBetween}>
                        <Typography sx={textStyles}><span style={boldBlackText}>{orderDetailsDto.currency}${orderDetailsDto.price}</span></Typography>
                        <Typography sx={textStyles}><span style={boldBlackText}>x{orderDetailsDto.quantity}</span></Typography>
                        </Box>
                    </Box>
                    <Box sx={orderDetailsStyle}>
                        <Typography sx={textStyles}><span style={blackText}>2025/1/18 - 展期特典票</span></Typography>
                        <Typography sx={textStyles}>時間段:10:00-12:00, 指定入場時間段: 11:00-11:30</Typography>
                        <Box sx={boxSpaceBetween}>
                        <Typography sx={textStyles}><span style={boldBlackText}>HK$168</span></Typography>
                        <Typography sx={textStyles}><span style={boldBlackText}>x2</span></Typography>
                        </Box>
                    </Box>
                    <Box sx={orderDetailsStyle}>
                        <Typography sx={textStyles}><span style={blackText}>2025/1/18 - 展期特典票</span></Typography>
                        <Typography sx={textStyles}>時間段:10:00-12:00, 指定入場時間段: 11:00-11:30</Typography>
                        <Box sx={boxSpaceBetween}>
                        <Typography sx={textStyles}><span style={boldBlackText}>HK$168</span></Typography>
                        <Typography sx={textStyles}><span style={boldBlackText}>x2</span></Typography>
                        </Box>
                    </Box>
                <Typography variant="subtitle1" sx={{mb:2}}>KAMEN RIDER 50TH ANNIVERSARY EXHIBITION - 香港</Typography>
                    <Box sx={orderDetailsStyle}>
                        <Typography sx={textStyles}><span style={blackText}>2025/1/18 - 展期特典票</span></Typography>
                        <Typography sx={textStyles}>時間段:10:00-12:00, 指定入場時間段: 11:00-11:30</Typography>
                        <Box sx={boxSpaceBetween}>
                        <Typography sx={textStyles}><span style={boldBlackText}>HK$168</span></Typography>
                        <Typography sx={textStyles}><span style={boldBlackText}>x2</span></Typography>
                        </Box>
                    </Box>
                    <Box sx={orderDetailsStyle}>
                        <Typography sx={textStyles}><span style={blackText}>2025/1/18 - 展期特典票</span></Typography>
                        <Typography sx={textStyles}>時間段:10:00-12:00, 指定入場時間段: 11:00-11:30</Typography>
                        <Box sx={boxSpaceBetween}>
                        <Typography sx={textStyles}><span style={boldBlackText}>HK$168</span></Typography>
                        <Typography sx={textStyles}><span style={boldBlackText}>x2</span></Typography>
                        </Box>
                    </Box>
                    <Box sx={orderDetailsStyle}>
                        <Typography sx={textStyles}><span style={blackText}>2025/1/18 - 展期特典票</span></Typography>
                        <Typography sx={textStyles}>時間段:10:00-12:00, 指定入場時間段: 11:00-11:30</Typography>
                        <Box sx={boxSpaceBetween}>
                        <Typography sx={textStyles}><span style={boldBlackText}>HK$168</span></Typography>
                        <Typography sx={textStyles}><span style={boldBlackText}>x2</span></Typography>
                        </Box>
                    </Box>
                </AccordionDetails>
            </Accordion>
            <Divider sx={{mt:2,mb:2}}></Divider>
                <Box sx={{
                    ml:2
                }}>
                    <Typography sx={textStyles}>商品總額: <span style={boldBlackText}>HK${orderDetailsDto.totalPrice}</span></Typography>
                    <Typography sx={textStyles}>優惠金額: <span style={redText}>-HK$20</span></Typography>
                    <Typography sx={textStyles}>優惠碼: {orderDetailsDto.promoCode.code ? orderDetailsDto.promoCode.code : 'N/A'}  <span style={redText}> {orderDetailsDto.promoCode.discount ? '-HK$' + orderDetailsDto.promoCode.discount : 'N/A'}</span></Typography>
                    <Typography sx={textStyles}>訂單總額: <span style={boldBlackText}>HK${renderOrderBalance()}</span></Typography>
                </Box>
             </Box> : 
             <Box sx={{
                display:"flex",
                justifyContent:"center"
             }}>
             <CircularProgress /> 
             </Box>
             }
          </Box> 
        </Modal>
      </div>
    );
}

export default RecordOrderDetailsModal;