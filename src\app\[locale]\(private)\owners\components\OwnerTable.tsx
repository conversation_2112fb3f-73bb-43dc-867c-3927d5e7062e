"use client";
import React, { useEffect, useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  PaginationState,
  ColumnDef,
} from "@tanstack/react-table";
import { Box, Button, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import PaginationTable from "@/components/PaginationTable";
import { useRouter } from "next/navigation";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { format } from "date-fns";
import { ROUTES } from "@/utils/constants";
import { IOwner } from "@/interface/IOwner";
import PageSelector from "@/components/PageSelector";
import SearchInput from "@/components/input/SearchInput";
import SortBySelector, { SortOption } from "@/components/SortBySelector";

import EditButton from "@/components/buttons/EditButton";
import DeleteOwner from "./DeleteOwner";

const sortByOptions: SortOption[] = [
  {
    labelKey: "owner.sortby_owner_id_desc",
    id: "id",
    order: "desc",
  },
  {
    labelKey: "owner.sortby_owner_name_asc",
    id: "name",
    order: "asc",
  },
  {
    labelKey: "owner.sortby_owner_name_desc",
    id: "name",
    order: "desc",
  },
  {
    labelKey: "owner.sortby_created_at_desc",
    id: "createdAt",
    order: "desc",
  },
  {
    labelKey: "owner.sortby_created_at_asc",
    id: "createdAt",
    order: "asc",
  },
];

const OwnerTable = () => {
  const t = useTranslations("");
  const router = useRouter();
  const [keyword, setKeyword] = React.useState("");
  const [sorting, setSorting] = React.useState<SortOption>(sortByOptions[0]);
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const {
    data,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ["owners", { pagination, sorting, keyword }],
    queryFn: async () =>
      xior
        .get<IListResponse<IOwner>>("/api/owners", {
          params: {
            page: pagination.pageIndex + 1,
            size: pagination.pageSize,
            sortBy: sorting.id,
            sortDirection: sorting.order,
            keyword,
          },
        })
        .then((res) => res.data),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  useEffect(() => {
    if (!isLoading) refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router])

  const columns = useMemo<ColumnDef<IOwner>[]>(
    () => [
      {
        accessorKey: "ownerId",
        header: "",
        cell: (data: any) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"center"}
            gap={1}
          >
            <EditButton
              
              onClick={() => router.push(`${ROUTES.OWNER}/${data.row.original.id}/edit`)}
            />
            <DeleteOwner owner={data.row.original} />
          </Box>
        ),
      },
      {
        accessorKey: "photoUrl",
        header: t("owner.label_thumbnail"),
        cell: (data) =>
          data.getValue() ? (
            <Box
              component={"img"}
              src={data.getValue() as string}
              alt="room-thumbnail"
              height={42}
              sx={{ objectFit: "contain" }}
            />
          ) : (
            <Box
              component={"img"}
              src="/images/image_not_found.png"
              alt="image-not-found"
              height={42}
              sx={{ objectFit: "contain" }}
            />
          )
      },
      {
        accessorKey: "name",
        header: t("owner.label_owner_name"),
      },
      {
        accessorKey: "email",
        header: t("owner.label_owner_email"),
      },
      {
        accessorKey: "updatedAt",
        header: t("owner.label_updated_at"),
        cell: (data) => {
          if (!data?.getValue()) return "-"
          return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
        }
      },
      {
        accessorKey: "createdAt",
        header: t("owner.label_created_at"),
        cell: (data) => {
          if (!data?.getValue()) return "-"
          return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
        }
      },
    ],
    [router, t]
  );

  const defaultData = React.useMemo<IOwner[]>(() => [], []);

  const table = useReactTable({
    data: data?.items ?? defaultData,
    columns,
    rowCount: data?.count,
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    defaultColumn: {
      minSize: 0,
    },
  });

  return (
    <Box display={"flex"} flexDirection={"column"} height={"100%"}>
      <Box display={"flex"} flexDirection={"row"} mb={3}>
        <Box flex={1}>
          <SearchInput
            value={keyword}
            onChange={(value) => {
              setKeyword(value);
            }}
          />
        </Box>
        <SortBySelector
          options={sortByOptions}
          value={sorting}
          onChange={(value) => setSorting(value)}
        />
        <PageSelector
          value={pagination.pageSize}
          onChange={(newPageSize) => {
            table.setPageSize(newPageSize);
          }}
        />
      </Box>
      <Box flex={1}>
        <PaginationTable table={table} fullWidth isLoading={isLoading} msg={t("owner.title_empty")} />
      </Box>
    </Box>
  );
};

export default OwnerTable;
