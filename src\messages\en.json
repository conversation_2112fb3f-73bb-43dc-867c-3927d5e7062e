{"login": {"title": "Log in", "message": "Continue to Funverse", "login_by_email": "or log in using email", "placeholder_email": "Enter your email", "placeholder_password": "Enter your password", "button_login": "Log in", "button_google_login": "Continue to Google", "new_to_funverse": "New to Funverse? <arrow></arrow> <signup>Create an account</signup>", "button_forgot_password": "Forgot your password?", "GO": "GO", "login_email": "email", "login_password": "password"}, "event": {"event_detail": "Event Details", "list_event_title": "Events & Tickets", "create_button": "Create Event", "input_event_detail": "Enter event details", "created_on": "Created on", "ends_in": "Ends in", "selling_tickets": "Selling Tickets", "left": "left", "gallery": "Gallery", "event_introduction": "Event Introduction", "qaa": "Q&A", "question": "Question", "answer": "Answer", "terms": "Terms & Conditions", "event_name": "Event Name", "region": "Region", "timezone": "Time Zone", "event_start": "Event starts", "event_end": "Event ends", "event_time_start": "Event time starts", "event_time_end": "Event time ends", "event_date": "Event Date", "event_time": "Event Time", "about_time": "About Time", "venue": "Venue", "media_remark": "Image Dimensions (W x H) Recommendation: 1360x765px, Upload PNG, JPEG, JPG only (Max. 2MB) Video: Upload MP4 from 720p only (Max. 5MB)", "publish_event": "Publish Event", "ACTIVE": "Active", "INACTIVE": "Inactive", "DRAFT": "Draft", "inactive": "Inactive"}, "ticket": {"tickets": "Tickets", "inventory": "Ticket Inventory", "ticket_detail": "Ticket Details", "currency": "<PERSON><PERSON><PERSON><PERSON>", "ticket_sale_period": "Ticket Sales Period", "stop_selling_ticket": "Stop Selling Tickets", "section_and_quantity": "Sections and Quantity", "max_booking": "Maxmium Bookings", "available_day": "Available Day", "custom_day": "Custom Days", "custom_day_format": "YYYY-MM-DD", "quantity_per_slot": "Quantity of Every Time Slot", "time_slot": "Time Slots", "variation": "Variations", "variation_label": "Variable Label", "values": "Values", "values_with_remind": "Values (separated by comma)", "ticket_type": "Ticket Types", "ticket_name": "Type Name", "description": "Description", "price": "Price", "status": "Status", "pre_sale": "Pre-sale", "edit_ticket": "Edit Ticket <PERSON>", "sale_start_datetime": "Ticket Sales Starts", "sale_end_datetime": "Ticket Sales Ends", "sale_time_thershold": "Stop Selling Tickets", "edit_section": "Edit Sections and Quantity", "edit_variation": "Edit Ticket Variation", "add_variation": "Add Ticket Variation", "add_custom": "Add Custom", "add_custom_weekly": "Add Custom Weekly Repeats", "timeslot_and_quantity": "Time Slots and Quantity", "start_time": "Start Time", "end_time": "End Time", "quantity": "Quantity", "total_qty": "Total Qty", "add_type": "Add Ticket Type", "ticket_cover": "Ticket Cover (optional)", "cover_description": "Image Dimensions (W x H) Recommendation: 1360x765px, Upload PNG, JPEG, JPG only (Max. 2MB)", "selling_location": "Selling Location", "type": "Ticket Type", "type_name": "Type Name", "type_booking": "Tickets can be booked for", "special_selling_period": "Special Sales Period", "special_selling_period_desc": "Tickets will be on sale during a special sales period.", "special_selling_start": "Special Sales Starts", "special_selling_end": "Special Sales Ends", "free_ticket": "Free Ticket ?", "multiple_entry": "Allow multiple entry ?", "gift_included": "Gift included", "gift_included_desc": "The ticket QR code can be scanned twice to enter the event and redeem the gift.", "price_unit_optional": "Price Unit (Optional)", "third_party_name": "Third-Party Name", "third_party": "Third-Party", "filter_by_date": "Filter by Available Days", "export_to_third_party": "Export to Third-Party", "available": "Available", "export_to": "Export to", "avaiable_after_export": "Available After Export", "selected": "selected", "copy_all": "Copy to All", "copy": "Copy", "paste": "Paste", "choose_ticket_type": "Choose Ticket Type", "history": "History", "export_to_third_party_history": "Export to Third-Party History", "export_datetime": "Export Date & Time", "redownload": "Redownload", "toggle": "Toggle"}, "menu": {"featured_events": "Featured Events", "virtual_room": "Virtual Room", "about_incutix": "About Incutix", "faq": "FAQs"}, "footer": {"contact_us": "Contact Us", "follow_us": "Follow Us", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service", "business_negotiations": "Business Negotiations", "only_text_messages_are_supported": "Only text messages are supported"}, "featured_events": {"all_events": "All Events"}, "featured_virtual_room": {"virtual_room": "Virtual Room"}, "about_incutix": {"about_incutix": "About Incutix"}, "faqs": {"faqs": "faqs"}, "my_ticket": {"my_ticket": "My Ticket", "inactive": "inactive", "expired": "Expired"}, "purchase_record": {"purchase_record": "Purchase Record", "event_tickets": "Event Tickets", "product_pre-order": "Product Pre-order"}, "signup": {"title": "Sign up", "message": "Continue to Funverse", "signup_by_email": "or sign up using email", "placeholder_email": "Enter your email", "button_signup": "Sign up", "button_google_login": "Continue to Google", "already_have_an_account": "Already have an account? <arrow></arrow> <login>Log in</login>", "agree_to_tos_and_pp": "By creating an account, you agree to the <tos>Terms of Service and Privacy Policy</tos>", "send_verification_code": "Send Verification Code", "next_step": "Next Step", "user_name": "Name", "placeholder_name": "Please enter your name", "label_password": "Password", "label_confirm_password": "Confirm password again", "placeholder_password": "Please enter password", "placeholder_confirm_password": "Please enter password again", "terms_of_service": "By seleting Agree and continue, I agree to incutix's Terms of Service, Payments Terms of Service and acknowledge the Privacy Policy", "dont_receive_promotion_message": "I don't want to receive promotion message", "signup_confirm": "Confirm"}, "forgot_password": {"title": "Forgot your password?", "message": "Enter your email and we will send your a link to reset your password.", "placeholder_email": "Enter your email", "button_send_email": "Send email", "check_inbox": "Check your inbox", "reset_email_sent": "We just emailed a reset link to <email></email>. Click the link and you'll be prompted to choose a new password."}, "reset_password": {"title": "Reset your password", "message": "Choose a new and secure password to protect your account.", "label_password": "New password", "description_password": "Your password must be at least 6 characters.", "label_confirm_password": "Confirm new password", "placeholder_password": "Please enter new password", "placeholder_confirm_password": "Please enter new password again", "button_update_password": "Update password", "reset_password_success": "Your password has been reset, you can now login using this new password.", "button_continue_to_login": "Continue to login", "button_confirm_and_return_to_home": "Confirm and return to home page"}, "verify": {"title": "Sign up", "message": "Continue to Funverse", "check_inbox": "We just sent you a temporary sign up code.\nPlease check your inbox and paste the sign up code below.", "placeholder_sign_up_code": "Sign up code", "button_resend_sign_up_code": "Resend the sign up code", "button_create_new_account": "Create new account"}, "setup_profile": {"title": "Welcome to Funverse!", "label_profile_image": "Add a photo", "profile_image_metadata": "Resolution: Max. 1024px x 1024px (square)\nSize Limit: 2 MB\nFormat: .png, .jpg, jpeg.", "label_name": "what should we call you?", "placeholder_name": "eg. <PERSON>, Ben", "label_password": "Set a password", "placeholder_password": "Password", "label_confirm_password": "Confirm your password", "placeholder_confirm_password": "Password Confirmation", "subscribe_for_updates": "Please send me updates/ promotions about Funverse.", "button_continue": "Continue"}, "sidebar": {"dashboard": "Dashboard", "room_information": "Room Information", "sales_orders": "Sales Orders", "delivery_note": "Delivery Note", "orders_list": "Orders List", "products": "Products", "product": "Product", "product_list": "Product List", "inventory": "Inventory", "member_management": "Member Management", "member_list": "Member List", "member_group": "Member Group", "invitation": "Invitation", "message": "Message", "product_category": "Product Category", "collection": "Collection", "owner": "Owner", "tags": "Tags", "settings": "Settings", "profile": "Profile", "change_password": "Reset Password", "salesOrder": "Sales Orders"}, "inventory": {"label_title": "Inventory"}, "common": {"hk": "Hong Kong", "my": "Malaysia", "button_add_new": "Add New", "button_import_from_csv": "Import from CSV", "button_export_to_excel": "Export to Excel", "button_edit": "Edit", "button_profile": "Edit", "button_open": "Open", "button_update": "Update", "button_save": "Save", "button_cancel": "Cancel", "button_delete": "Delete", "button_add": "Add", "button_upload": "Upload", "button_stay_in_page": "Stay in Page", "title_confirm_delete": "Are you sure to delete?", "title_edit": "Edit", "label_sort_by": "Sort By:", "button_add_to_group": " Add to Group", "button_remove_from_group": "Remove from Group", "button_send": "Send", "select_placeholder": "Please select", "input_placeholder": "Please enter", "active": "Active", "draft": "Draft", "expired": "Expired", "inactive": "Inactive", "view_all": "View All", "show_less": "Show Less", "selected": "selected", "mark_as_inactive": "<PERSON> as Inactive", "updated": "Updated", "language": "Language", "preview": "Preview Page", "optional": "Optional", "total": "Total"}, "dashboard": {"label_title": "Dashboard"}, "room_information": {"label_title": "Room Information", "button_add_new": "Add New", "label_room_id": "Room ID", "label_room_name": "Room Name", "label_room_url": "Room URL", "label_description": "Description", "label_thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "label_thumbnail_image_video": "Thumbnail image/video", "label_room_bg_music": "Room Background Music", "label_room_status": "Room Status", "label_room_exclusive_to": "Room Exclusive to", "label_created_at": "Created At", "label_updated_at": "Last Updated", "label_visible_to_public": "Visible To Public", "label_tags": "Tags", "label_logo": "Logo", "label_open_to_all_member": "Open To All Member", "title_create_room": "Create", "title_edit_room": "Edit", "placeholder_description": "Tell people about your room.", "placeholder_tags": "Enter tag(s)", "title_empty": "You haven't created any room yet."}, "product_information": {"label_id": "Product ID", "label_name": "Product Name", "label_description": "Product Description", "label_thumbnail": "Product Thumbnail", "label_audio": "Product Introduction Audio", "label_owner": "Product Owner", "label_category": "Category", "created_date": "Created Date", "updated_date": "Updated Date", "label_status": "Status", "label_price": "Price", "label_tax": "Tax", "label_type": "Type", "label_currency": "<PERSON><PERSON><PERSON><PERSON>", "label_weight": "Weight", "label_unit": "Unit", "label_is_digital": "Is Digital?"}, "sales_orders": {"label_title": "Sales Orders", "export_to_excel": "Export to Excel", "search": "Search", "showPerPage": "Show per page", "tab": {"all": "All", "in_progress": "In Progress", "completed": "Completed"}, "table": {"header": {"payment_status": "Payment Status", "order_number": "Order Number", "customer": "Customer", "customer_email": "Customer <PERSON><PERSON>", "total": "Total", "delivery_status": "Delivery Status", "create_at": "Create At"}, "tag": {"payment_status": {"in_progress": "In Progress", "cancel": "Failed", "completed": "Completed"}, "delivery_status": {"pending": "Pending", "express_delivered": "Delivered to Courier", "store_delivered": "Picked Up"}}, "action": {"selected": "{selected} selected", "pick_up": "<PERSON> as Picked Up", "delivered_to_courier": "<PERSON> as Delivered to Courier"}}, "orders_list": "Orders List", "delivery_note": "Delivery Note", "detail": {"breadcrumbs": {"parent": "Sales Orders", "current": "Orders #{orderNo}"}, "title": "Orders #{orderNo}", "download_pdf": "Download PDF", "customer_detail": {"title": "Customer details", "name": "Name", "email": "E-mail", "phone_no": "Phone No.", "region": "Region"}, "shipping_detail": {"title": "Shipping Details", "shipping_method": "Shipping Method", "consignee": "Consignee", "contract_no": "Contract No.", "shipping_address": "Shipping Address", "shipping_method_express": "Delivery", "shipping_method_pickup": "Pick up"}, "order_summary": {"title": "Order summary", "order_no": "Order No.", "transaction_no": "Transaction No.", "order_created": "Order Created", "payment_method": "Payment Method", "billing_address": "Billing Address", "currency": "<PERSON><PERSON><PERSON><PERSON>", "sub_total": "Subtotal", "shipping_cost": "Shipping Cost", "discount": "Discount", "promo_code": "Promo Code: {code}", "summary": "{itemCount} Items, Total"}, "remark": {"title": "Remarks", "edit": "Edit", "dialog": {"title": "Remarks", "word_count": "Word count: {count}", "confirm": "Confirm", "cancel": "Cancel"}}, "status": {"payment_status": "Payment Status", "mark_as_complete": "Mark as Complete", "delivery_status": "Delivery Status", "change_status": "Change Status", "cancel": "Cancel"}, "item_summary": {"title": "<PERSON><PERSON>", "table": {"header": {"items": "Items", "unit_price": "Unit Price", "qty": "Qty", "total": "Total"}}}}}, "member_group": {"label_title": "Member Group", "title_create_member_group": "Add New Member Group", "title_edit_member_group": "Edit Member Group", "label_member_group_name": "Member Group Name", "label_group_name": "Group Name", "title_empty": "You haven't created any member group yet.", "label_split": "Enter group(s), seperate by ','."}, "member_management_list": {"label_title": "Member List", "label_name": "Member Name", "label_email": "Member <PERSON><PERSON>", "label_group": "Member Group", "label_platform": "Register Location", "label_updated_at": "Last Updated", "label_registered_at": "Registered Date", "label_add_to_group": "Add to Group", "label_remove_from_group": "Remove from Group", "title_empty": "Here's no member."}, "product": {"label_type": "Type", "label_room_excluded": "Product Exclusive To", "label_created_at": "Created At", "label_updated_at": "Updated At", "label_thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "label_title": "Product List", "label_product_detail": "Products", "label_product_create": "Create", "label_id": "Product ID", "label_name": "Product Name", "placeholder_name": "Enter product name", "label_description": "Description", "label_placeholder_description": "Product Description", "label_product_type": "Type of Product", "label_physical_product": "Physcial Product", "label_digital_product": "Digital Product", "label_product_audio": "Product Introduction Audio", "label_collection": "Collection", "collection_description": "Select collection(s)", "label_owner": "Owner", "owner_description": "Select Owner", "label_category": "Product Category", "label_tag": "Tags", "label_list_of_thumbnail": "List of Product Thumbnail Image", "label_product_asset": "Product Images/Video", "label_visible": "Visible to public", "placeholder_tags": "Enter tag(s)", "label_picture": "Picture", "label_filename": "File Name", "label_sorting": "Sorting", "add_product_image": "Add Product Image / Video", "label_information": "Information", "label_selling": "Selling", "label_status": "Status", "label_draft": "Draft", "label_listed": "Listed", "label_unlisted": "Unlisted", "label_selling_type": "Selling Type", "selling_type_description": "You cannot change the type if you choose Au<PERSON> before the current bidding section ended.", "label_retail": "Retail", "label_auction": "Auction (Online Bidding)", "label_price": "Price", "label_compare_at_price": "Compare-at price", "compare_at_price_description": "To display a markdown, enter a value higher than your price. (e.g. USD25.00)", "label_charge_tax": "Charge tax on this product", "label_tax_rate": "Tax/VAT Rate (%)", "label_inventory": "Inventory", "label_weight": "Weight", "weight_description": "Shipping cost would be free if weight is set to zero.", "label_open_to_public": "Open To All Member", "label_room_exclusive_to": "Room Exclusive to", "label_start_bidding": "Starting Bid <PERSON>", "label_bidding_increment": "Bidding Increment", "label_duration": "Duration", "label_from": "From", "label_to": "To", "label_auction_status": "Auction Status", "label_not_yet_status": "Not Yet Status", "label_in_progress": "In Progress", "label_auction_success": "Auction Ended - Success", "label_auction_closed": "Auction Ended - Closed", "label_auction_cancel": "Cancelled", "label_member_group": "Member Group", "title_confirm_delete": "Are you sure to delete?", "title_empty": "You haven't created any product yet."}, "invitation": {"label_title": "Invitation List", "label_name": "Member Name", "label_email": "Member <PERSON><PERSON>", "label_group": "Member Group", "label_status": "Member Status", "label_email_sent": "Invitation <PERSON><PERSON>", "label_last_update": "Last Updated", "pending": "Pending", "registered": "Registered", "title_confirm_delete": "Are you sure to delete?", "title_create_invitation": "Invite New Member", "label_new_member_name": "New Member Name", "title_empty": "You haven't created any invitation yet."}, "message": {"label_title": "Message"}, "product_category": {"label_title": "Product Category", "label_name": "Category Name", "title_create_product_category": "Add New Category", "label_new_product_category_name": "New Category Name", "label_parent_category": "Parent Category", "label_parent_category_helper_text": "Leave blank to add as a root category.", "title_edit_product_category": "Edit Category", "title_confirm_delete": "Are you sure to delete?\nThe sub-categories will also be deleted (If any.)\nProducts in this Category would not be affected.", "title_empty": "You haven't created any category yet.", "label_split": "Enter category(s), seperate by ','."}, "collection": {"label_title": "Collection", "label_collection_id": "Collection ID", "label_collection_name": "Collection Name", "label_description": "Description", "placeholder_description": "Collection Description", "label_thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "label_created_at": "Created At", "sortby_collection_id_desc": "Collection ID", "sortby_collection_name_asc": "Collection Name - A to Z", "sortby_collection_name_desc": "Collection Name - Z to A", "sortby_created_at_desc": "Created Date - New to Old", "sortby_created_at_asc": "Created Date - Old to New", "title_edit_collection": "Edit", "title_create_collection": "Add New Collection", "title_confirm_delete": "Are you sure to delete this collection?", "sub_title_confirm_delete": "Products in this collection would not be affected.", "label_product": "Product", "label_last_update": "Last Updated", "title_empty": "You haven't created any collection yet."}, "owner": {"label_title": "Owner", "sortby_owner_id_desc": "Owner ID", "sortby_owner_name_asc": "Owner Name - A to Z", "sortby_owner_name_desc": "Owner Name - Z to A", "sortby_created_at_desc": "Created Date - New to Old", "sortby_created_at_asc": "Created Date - Old to New", "label_thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "label_owner_id": "Owner Id", "label_owner_name": "Owner Name", "label_owner_email": "Owner <PERSON><PERSON>", "label_created_at": "Created At", "label_updated_at": "Updated At", "title_add_new_owner": "Add New Owner", "label_name": "Name", "label_gender": "Gender", "label_email": "Email", "label_owner_introduction": "Owner Introduction", "label_country": "Country / Region", "label_social_media": "Social Network", "title_empty": "You haven't created any owner yet."}, "tags": {"label_title": "Tags", "label_tag_name": "Tag Name", "title_create_tag": "Add New Tag", "label_new_tag_name": "New Tag Name", "title_edit_tag": "Edit Tag", "title_empty": "You haven't created any tag yet.", "label_split": "Enter tag(s), seperate by ','."}, "settings": {"label_title": "Settings"}, "navbar": {"edit": "Account Information", "reset_password": "Reset Password", "terms_of_use": "Terms of Use", "privacy_policy": "Privacy Policy", "help_center": "Help Center", "logout": "Log Out", "login": "Log In / Register", "account_information": "Account information", "my_ticket": "My Ticket", "purchase_record": "Purchase Record"}, "profile": {"label_title": "Profile", "label_identity": "Identity", "label_name": "Name", "label_gender": "Gender", "label_email": "Email", "label_phone_number": "Phone Number", "label_contact_methods": "Contact Method (will be shown in product page)", "label_dob": "Date of Birth", "label_address": "Address", "label_country": "Country / Region", "label_social_media": "Social Network", "label_update_profile": "Update Profile", "account_information": "Account Information", "identity_value_personal": "Personal", "identity_value_company": "Company", "gender_value_female": "Female", "gender_value_male": "Male", "gender_value_transgender_female": "Transgender Female", "gender_value_transgender_male": "Transgender Male", "gender_value_non_binary": "Non-Binary", "gender_value_agender": "Agender/I don't identify with any gender", "gender_value_not_listed": "Gender not listed", "gender_value_not_to_state": "Prefer not to state"}, "change_password": {"label_title": "Reset Password", "label_current_password": "Current Password", "label_change_password": "Change Password", "label_new_password": "New Password", "description_new_password": "Your password must be at least 6 characters.", "label_confirm_password": "Confirm Your Password", "placeholder_current_password": "Please enter the current password", "placeholder_new_password": "Please enter the new password", "placeholder_confirm_password": "Please enter the new password"}, "error": {"required": "Required", "invalid_email": "<PERSON><PERSON><PERSON>", "invalid_credential": "Invalid Credential", "invalid_password": "Your password must be at least 6 characters.", "unmatch_password": "The password confirmation does not match.", "invalid_image_type": ".jpg, .jpeg and .png files are accepted.", "invalid_image_video_type": ".jpg, .jpeg, .png and .mp4 files are accepted.", "invalid_audio_type": ".mp3 and .wav files are accepted.", "image_file_size_exceed": "Max image size is 2MB.", "duplicated_tags": "This tag is already added.", "duplicated_member_group": "The member group is already added.", "duplicated_product_category": "The product category is already added.", "unknown_error": "Unknown error. Please try again later.", "user_already_verified": "User is already verified", "email_is_duplicated": "Email already exists"}, "file_upload": {"logo_upload": "Upload your logo", "thumbnail_upload": "Upload your thumbnail image", "thumbnail_multimedia_upload": "Upload your Thumbnail image/video", "background_music_upload": "Upload the room background music", "icon_metadata": "Max. 1024px x 1024px(square)\n2 MB\n.png, .jpg, jpeg.", "image_metadata_collection": "Image Dimension: Max. W1024px x Max. H1024px\nSize Limit: 10 MB\nFormat: .png, .jpg, jpeg.", "image_metadata": "Image Dimension: Max. 1024px x 1024px(square)\nLimit of each file size: 2 MB\nFormat: .png, .jpg, jpeg.", "image_video_metadata": "Image Dimension: Image: Max. W: 1024px ; Max. H:1024px ; Video: from 720p\nLimit of each file size: Image: 2 MB ; Video: from 720p, Max. 1min\nFormat: .png, .jpg, jpeg., .mp4", "audio_metadata": "Limit of each file size: 10MB\nFormat: .mp3, .wma"}}