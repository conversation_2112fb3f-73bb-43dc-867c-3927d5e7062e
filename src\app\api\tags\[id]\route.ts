import { ITag } from "@/interface/ITag";
import {
  authorizedDelete,
  authorizedGet,
  authorizedPut,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";

async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const data = await authorizedGet<ITag>(
      `/tags/${params.id}`,
      await getAuthHeaders(req)
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const data = await authorizedPut<ITag>(
      `/tags/${params.id}`,
      await getAuthHeaders(req),
      await req.json()
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await authorizedDelete(`/tags/${params.id}`, await getAuthHeaders(req));
    return Response.json({ status: "success" }, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET, PUT, DELETE };
