"use client";
import * as React from "react";
import { <PERSON>, Button, Modal, Switch, Text<PERSON>ield, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { ICollection } from "@/interface/ICollection";
import { useRouter } from "next/navigation";
import { AVAILABLE_DAY, CURRENCY, LANGUAGE_CODE, ROUTES, SALES_THRESHOLD, SELLING_LOCATION, SUPPORT_CURRENCY, TICKET_SELLING_TYPE } from "@/utils/constants";
import EditButton from "@/components/buttons/EditButton";
import TextEditor from "@/components/RichEditor";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";
import { DatePicker, DateTimePicker } from "@mui/x-date-pickers";
import Select from "@/components/input/Select";
import SelectWithAdd from "@/components/input/SelectWithAdd";
import Image from "next/image";
import { generateDisplayableDate } from "@/utils/date";
import { removeNull } from "@/utils/common";
import EditTicketPhoto from "./EditTicketPhoto";
import SelectList from "@/components/input/SelectList";
import _ from "lodash";

interface Props {
    id: number,
    day: number[],
    date: number[],
    variation: {label: string, value: number}[]
    info: ServerTypeInfo,
    products: {
        product_id: number,
        product_translations: {
            name: string,
            language_code: LANGUAGE_CODE
        }[],
        product_media: { url: string }[],
        product_sku: {
            sku_id: number,
            sku_code: string,
            product_media: { url: string }[]
        }[]
    }[]
    language: LANGUAGE_CODE
}

export enum SELLING_PLATFORM {
    INCUTIX=1,
    THIRD_PARTY=2
}

export enum SELLING_TYPE {
    GENERAL = 1,
    BUNDLE = 2
}

type ServerTypeInfo = {
    selling_location: number,
    third_party?: {
        id: number,
        name: string
    },
    selling_type: number,

    multiply?: number,
    name: string,
    description: string,
    ticket_type_to_section: {
        ticket_section: {
            id: number,
            day?: number,
            date?: number,
        }
    }[],
    ticket_type_to_variation: {
        ticket_variation: {
            id: number
        }
    }[],

    pre_sale_startTime?: number,
    pre_sale_endTime?: number,

    price?: number,
    price_unit?: string,

    allow_multiple_entry: boolean,

    have_gift: boolean,
    ticket_to_gift: {product_id: number, sku_id: number}[],
    medias: {url: string, id: number}[],
}

export type TypeInfo = {
    sellingLocation: number,
    thirdParty?: {
        id: number,
        name: string
    },
    sellingType: number,
    thirdPartyName?: string,

    multiply?: number,
    name: string,
    description: string,
    ticketSections: number[],
    ticketVariation: number[],

    specialSalesPeriod: boolean,
    preSaleStartTime?: number,
    preSaleEndTime?: number,

    isFree: boolean,
    price?: number,
    priceUnit?: string,

    allowMultipleEntry: boolean,

    haveGift: boolean,
    products: number[],
    skus: number[],
}

const EditTicketType = ({ 
    id,
    day,
    date,
    variation,
    info,
    products,
    language
}: Props) => {
    const queryClient = useQueryClient();
    const router = useRouter();
    const t = useTranslations("ticket");
    const tCommon = useTranslations("common");
    const [open, setOpen] = React.useState(false);
    const [typeInfo, setTypeInfo] = React.useState<TypeInfo>({
        sellingLocation: 1,
        sellingType: 1,
        multiply: 1,
        name: '',
        description: '',
        ticketSections: [],
        ticketVariation: [],
        specialSalesPeriod: false,
        isFree: false,
        allowMultipleEntry: false,
        haveGift: false,
        products: [],
        skus: []
    });
    const [image, setImage] = React.useState<(File)>()
    const [removedImage, setRemovedImage] = React.useState(false);

    React.useEffect(() => {
        setTypeInfo({
            ...typeInfo,
            sellingLocation: info.third_party? SELLING_PLATFORM.THIRD_PARTY: SELLING_PLATFORM.INCUTIX,
            thirdPartyName: info.third_party? info.third_party.name: "",
            sellingType: info.selling_type,
            multiply: info.multiply,
            name: info.name,
            description: info.description,
            ticketSections: info.ticket_type_to_section.map((item) => item.ticket_section.day || item.ticket_section.date) as number[],
            ticketVariation: info.ticket_type_to_variation.map((item) => item.ticket_variation.id),
            specialSalesPeriod: !!(info.pre_sale_startTime || info.pre_sale_endTime),
            ...(info.pre_sale_startTime? {
                preSaleStartTime: info.pre_sale_startTime * 1000
            }: {}),
            ...(info.pre_sale_endTime? {
                preSaleEndTime: info.pre_sale_endTime * 1000
            }: {}),

            isFree: !!info.price,
            price: info.price,
            priceUnit: info.price_unit,

            allowMultipleEntry: info.allow_multiple_entry,
            haveGift: info.have_gift,
            skus: info.ticket_to_gift.map(({ sku_id }) => sku_id)
        })
    }, [info, typeInfo])

    const [dayOptions, setDayOptions] = React.useState([
        ...day.filter((item) => item!== null).map((val) => AVAILABLE_DAY.find(day => day.value === val)).filter(item => item !== undefined),
        ...date.filter((item) => item!== null).map((val) => {
            const dateStr = generateDisplayableDate(val)
            return {
                label: dateStr,
                value: val
            }
        })
    ])

    const mutation = useMutation({
        mutationFn: async () => {
            const clone = removeNull(typeInfo)
            const gifts: { productId: number, skuId: number }[] = []
            products.map(({ product_id, product_sku }) => {
                if (typeInfo.products.includes(product_id)) {
                    gifts.push(...product_sku.map(({ sku_id }) => ({
                        productId: product_id,
                        skuId: sku_id
                    })))
                } else {
                    const {
                        skus
                    } = typeInfo

                    gifts.push(
                        ...(skus.map((id) => {
                            const found = product_sku.find(({ sku_id }) => sku_id === id )

                            if (!found) return;
                            
                            return {
                                productId: product_id,
                                skuId: found.sku_id
                            }
                        }).filter(item => item !== undefined) as {productId: number, skuId: number}[])
                    )
                }
            })
            const body = {
                ticketSettingId: id,
                ...clone,
                ...(typeInfo.specialSalesPeriod? {
                    preSaleStartTime: (typeInfo.preSaleStartTime || 0)/1000,
                    preSaleEndTime: (typeInfo.preSaleEndTime || 0)/1000
                } : {}),
                multiply: Number(typeInfo.multiply),
                price: Number(typeInfo.price),
                isAllDay: false,
                removedImage,
                products: gifts,
                language
            }

            if (image) {
                const formData = new FormData()
                formData.append('ticket_assets', image)
                const imageUploaded = await xior.put(`/api/ticket/type/media/${id}`, formData)
                // TODO: pop toast message to tell user upload failed
            }
            
            return xior.put(`/api/ticket/type/${id}`, body)
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["event"]});
            setOpen(false);
        },
    });

    const handleOpen = () => setOpen(true);

    const handleClose = React.useCallback(() => {
        if (mutation.isPending) return;
        setOpen(false);
    }, [mutation.isPending]);

    const handleUpdate = () => {
        mutation.mutate()
    }

    const handleOnChange = (key: keyof TypeInfo, value: any) => {
        if (key === 'preSaleEndTime' || key === 'preSaleStartTime') {
            setTypeInfo({
                ...typeInfo,
                [key]: value.valueOf()
            })
            return;
        }
        setTypeInfo({
            ...typeInfo,
            [key]: value
        })
    }

    const renderSectionTitle = (title: string) => (
        <Typography variant="body1" fontWeight={"bold"}>
            {title}
        </Typography>
    )

    const renderDescription = (description: string) => (
        <Typography variant="body3" fontWeight={"400"} color={COLORS.GREY_6}>
            {description}
        </Typography>
    )

    const setFile = (file: File) => {
        setImage(file)
    }

    const removeImage = () => {
        setRemovedImage(true)
        setImage(undefined)
    }

    const renderImage = (src: string) => (
        <Box position={'relative'} width="160px" height="100px">
            <Box position={'absolute'} top={-8} right={-12} onClick={() => removeImage()}>
                <Image src={'/images/icon-close.svg'} width={24} height={24} alt={`remove`} />
            </Box>
            <Image 
                src={src}
                width={160}
                height={100}
                style={{
                    borderRadius: '8px',
                    objectFit: 'cover'
                }}
                alt={`image_${new Date().valueOf()}`}
            />
        </Box>
    )

    return (
        <Box>
            <Box onClick={handleOpen}>
                <Image
                    src={'/images/icon-edit.svg'}
                    width={48}
                    height={48}
                    alt={'edit'}
                />
            </Box>
            <Modal open={open} onClose={handleClose}>
            <ModalContainer sx={{ padding: '40px', gap: '32px', maxHeight: '80vh', overflow: 'scroll' }}>
                <Typography variant="h3">{t("add_type")}</Typography>

                <Box sx={{ display: 'flex', justifyContent: 'space-between'}}>
                    <Box>
                        {renderSectionTitle(t("ticket_cover"))}
                        {renderDescription(t("cover_description"))}
                    </Box>
                    {
                        info.medias.length > 0 && !removedImage? renderImage(info.medias[0].url)
                        :image ? renderImage(URL.createObjectURL(image)) 
                        : (
                            <EditTicketPhoto handleChange={setFile} />
                        )
                    }
                </Box>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                    {renderSectionTitle(t("ticket_detail"))}
                    <Box sx={{ display: 'flex', gap: '16px' }}>
                        <Select
                            fullWidth={true}
                            data={SELLING_LOCATION}
                            label={t("selling_location")}
                            multiple={false}
                            value={typeInfo.sellingLocation}
                            handleOnChange={(val) => handleOnChange('sellingLocation', val)}
                        />

                        {
                            typeInfo.sellingLocation === SELLING_PLATFORM.THIRD_PARTY && (
                                <TextField 
                                    fullWidth={true}
                                    label={t("third_party_name")}
                                    placeholder={tCommon("input_placeholder")}
                                    value={typeInfo.thirdPartyName}
                                    onChange={(event) => handleOnChange('thirdPartyName', event.target.value)}
                                />
                            )
                        }
                    </Box>
                    
                    <Select
                        fullWidth={true}
                        data={TICKET_SELLING_TYPE}
                        label={t("type")}
                        multiple={false}
                        value={typeInfo.sellingType}
                        handleOnChange={(val) => handleOnChange('sellingType', val)}
                    />

                    {
                        typeInfo.sellingType === SELLING_TYPE.BUNDLE && (
                            <TextField 
                                fullWidth={true}
                                type="number"
                                label={t("quantity")}
                                placeholder={tCommon("input_placeholder")}
                                value={typeInfo.multiply}
                                onChange={(event) => handleOnChange('multiply', event.target.value)}
                            />
                        )
                    }

                    <TextField 
                        fullWidth={true}
                        label={t("type_name")}
                        placeholder={tCommon("input_placeholder")}
                        value={typeInfo.name}
                        onChange={(event) => handleOnChange('name', event.target.value)}
                    />
                    <TextField 
                        fullWidth={true}
                        label={t("description")}
                        placeholder={tCommon("input_placeholder")}
                        value={typeInfo.description}
                        onChange={(event) => handleOnChange('description', event.target.value)}
                    />
                    <Select
                        fullWidth={true}
                        multiple={true}
                        value={typeInfo.ticketSections || []}
                        data={dayOptions as {label: string, value: number}[]}
                        label={t("type_booking")}
                        handleOnChange={(val) => handleOnChange('ticketSections', val)}
                    />
                    <Select
                        fullWidth={true}
                        multiple={true}
                        value={typeInfo.ticketVariation || []}
                        data={variation}
                        label={t("variation")}
                        handleOnChange={(val) => handleOnChange('ticketVariation', val)}
                    />
                </Box>
                
                <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between'}}>
                        <Box>
                            {renderSectionTitle(t("special_selling_period"))}
                            {renderDescription(t("special_selling_period_desc"))}
                        </Box>
                        <Switch
                            sx={{ ml: 1 }}
                            checked={typeInfo.specialSalesPeriod}
                            onChange={(event) => handleOnChange("specialSalesPeriod", event.target.checked)}
                        />
                    </Box>
                    
                    {
                        typeInfo.specialSalesPeriod && (
                            <Box sx={{ display: 'flex', gap: '16px', marginTop: '16px'}}>
                                <DateTimePicker
                                    label={t("special_selling_start")}
                                    value={
                                        typeInfo.preSaleStartTime ?
                                        new Date(typeInfo.preSaleStartTime)
                                        : null
                                    }
                                    sx={{ flex: 1 }}
                                    onChange={(date) => handleOnChange("preSaleStartTime", date)} 
                                />

                                <DateTimePicker
                                    label={t("special_selling_end")}
                                    value={
                                        typeInfo.preSaleEndTime ?
                                        new Date(typeInfo.preSaleEndTime)
                                        : null
                                    }
                                    sx={{ flex: 1 }}
                                    onChange={(date) => handleOnChange("preSaleEndTime", date)} 
                                />
                            </Box>
                        )
                    }
                    
                </Box>
                
                <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between'}}>
                        <Box>
                            {renderSectionTitle(t("free_ticket"))}
                        </Box>
                        <Switch
                            sx={{ ml: 1 }}
                            checked={typeInfo.isFree}
                            onChange={(event) => handleOnChange("isFree", event.target.checked)}
                        />
                    </Box>

                    {
                        typeInfo.isFree && (
                            <Box sx={{ display: 'flex', gap: '16px', marginTop: '16px'}}>
                                <TextField
                                    label={t("price")}
                                    value={typeInfo.price}
                                    sx={{ flex: 1 }}
                                    type="number"
                                    onChange={(event) => handleOnChange("price", event.target.value)} 
                                />

                                <TextField
                                    label={t("price_unit_optional")}
                                    value={typeInfo.priceUnit}
                                    sx={{ flex: 1 }}
                                    onChange={(event) => handleOnChange("priceUnit", event.target.value)} 
                                />
                            </Box>
                        )
                    }
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between'}}>
                    <Box>
                        {renderSectionTitle(t("multiple_entry"))}
                    </Box>
                    <Switch
                        sx={{ ml: 1 }}
                        checked={typeInfo.allowMultipleEntry}
                        onChange={(event) => handleOnChange("allowMultipleEntry", event.target.checked)}
                    />
                </Box>
                
                <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between'}}>
                        <Box>
                            {renderSectionTitle(t("gift_included"))}
                            {renderDescription(t("gift_included_desc"))}
                        </Box>
                        <Switch
                            sx={{ ml: 1 }}
                            checked={typeInfo.haveGift}
                            onChange={(event) => handleOnChange("haveGift", event.target.checked)}
                        />
                    </Box>

                    {
                        typeInfo.haveGift && (
                            <SelectList 
                                data={(products || []).map(({ product_id, product_translations, product_media, product_sku }) => {
                                    const name = product_translations
                                        .find(({ language_code }) => language_code === LANGUAGE_CODE.EN)?.name ?? ""
                                    return {
                                        value: product_id,
                                        label: name,
                                        extra: product_media?.[0]?.url,
                                        sub: product_sku.map((sku) => {
                                            return {
                                                name,
                                                value: sku.sku_id,
                                                subName: sku.sku_code,
                                                url: sku.product_media?.[0]?.url
                                            }
                                        })
                                    }
                                })}
                                value={[{ products: typeInfo.products }, { skus: typeInfo.skus }]}
                                multiple={true}
                                handleSetDefault={(setter, subSetter) => {
                                    setter && setter(typeInfo.products)
                                    subSetter && subSetter(typeInfo.skus)
                                }}
                                handleOnChange={(id) => handleOnChange('products', id)}
                                handleSubOnChange={(id) => handleOnChange('skus', id)}
                                customRender={(val, setter, subSetter) => {
                                    const allProduct = val[0]?.products?.map((id: number) => {
                                        const found = products.find(({product_id}) => product_id === id)
                                        if (!found) return;
                                        const {
                                            product_translations: translations
                                        } = found
                                        const name = translations
                                        .find(({ language_code }) => language_code === LANGUAGE_CODE.EN)?.name ?? ""
                                        return {
                                            id,
                                            name,
                                            url: found.product_media?.[0]?.url,
                                            isProduct: true
                                        }
                                    })
                                    const allSku = val[1]?.skus?.map((id: number) => {
                                        let target: {
                                            id: number,
                                            name: string,
                                            subName: string,
                                            url: string,
                                            isSku: boolean,
                                        }| undefined;
                                        products.forEach(({ product_translations: translations, product_sku }) => {
                                            const found = product_sku.find((sku) => sku.sku_id === id)
                                            if (!found) return;
                                            const name = translations
                                            .find(({ language_code }) => language_code === LANGUAGE_CODE.EN)?.name ?? ""
                                            target = {
                                                id,
                                                name,
                                                subName: found.sku_code,
                                                url: found.product_media?.[0]?.url,
                                                isSku: true
                                            }
                                        })
                                        return target
                                    })

                                    return (
                                        <Box sx={{ display: 'flex', rowGap: '8px', gap: '8px', overflow: 'scroll' }}>
                                            {
                                                _.sortBy([...allSku, ...allProduct], 'name').map(({
                                                    id,
                                                    name,
                                                    subName,
                                                    url,
                                                    isProduct,
                                                    isSku,
                                                }) => (
                                                    <Box
                                                        key={`${name}_${id}`}
                                                        sx={{
                                                            display: 'flex',
                                                            gap: '8px',
                                                            padding: '8px',
                                                            borderRadius: '12px',
                                                            backgroundColor: COLORS.GREY_3,
                                                            width: 'fit-content'
                                                        }}
                                                    >
                                                        <Image
                                                            src={url}
                                                            width={40}
                                                            height={40}
                                                            style={{ borderRadius: '8px' }}
                                                            alt={name}
                                                        />
                                                        <Box
                                                            sx={{
                                                                marginTop: 'auto',
                                                                marginBottom: 'auto'
                                                            }}
                                                        >
                                                            <Typography variant="body2" fontWeight="bold">
                                                                {name}
                                                            </Typography>
                                                            {
                                                                <Typography variant="body3" fontWeight="400">
                                                                    {subName}
                                                                </Typography>
                                                            }
                                                        </Box>
                                                        <Image
                                                            src={'/images/icon-cross.svg'}
                                                            width={16}
                                                            height={16}
                                                            style={{ marginTop: 'auto', marginBottom: 'auto' }}
                                                            alt={name}
                                                            onClick={() => {
                                                                if (isProduct) {
                                                                    const newProducts = typeInfo.products.filter(productId => productId !== id)
                                                                    setter && setter(newProducts)
                                                                    handleOnChange("products", newProducts)
                                                                }
                                                                if (isSku) {
                                                                    const newSkus = typeInfo.skus.filter(skuId => skuId !== id)
                                                                    subSetter && subSetter(newSkus)
                                                                    handleOnChange("skus", newSkus)
                                                                }
                                                            }}
                                                            onMouseDown={(event) => event.stopPropagation()}
                                                        />
                                                    </Box>
                                                ))
                                            }
                                        </Box>
                                    )
                                }}
                            />
                        )
                    }
                </Box>
                

                <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginTop: "30px" }}>
                    <CancelButton onAction={handleClose} />
                    <UpdateButton onAction={handleUpdate} label={tCommon("button_save")}/>
                </Box>
            </ModalContainer>
            </Modal>
        </Box>
    );
};

export default EditTicketType;
