import React from "react";
import { Button, ButtonProps } from "@mui/material";
import { useTranslations } from "next-intl";
import AddRoundedIcon from "@mui/icons-material/AddRounded";
import { ButtonHeadTable } from "./styled";

const ImportButton = (props: ButtonProps) => {
  const t = useTranslations("common");

  return (
    <ButtonHeadTable variant="contained" endIcon={<AddRoundedIcon />} {...props} >
      {t("button_import_from_csv")}
    </ButtonHeadTable>
  );
};

export default ImportButton;
