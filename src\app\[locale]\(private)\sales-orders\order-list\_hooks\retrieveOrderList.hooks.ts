import { useMemo } from "react";
import { OrderStatusType, orderStatus } from "../../_enums/order.enum";
import {
  useSaleOrderListPage,
  useSaleOrderListOrderStatus,
  useSaleOrderListSearch,
  useSaleOrderListSort,
  useSaleOrderListShowPerPage,
} from "../../_hooks";
import { OrderListType } from "../../_schema/saleOrder.schema";

export const useRetrieveOrderList = (data: OrderListType[]) => {
  const [page] = useSaleOrderListPage();
  const [searchOrderStatus] = useSaleOrderListOrderStatus();
  const [searchText] = useSaleOrderListSearch();
  const [{ sortOrder, sortOrderBy }] = useSaleOrderListSort();
  const [showPerPage] = useSaleOrderListShowPerPage();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const orderStatusMapper: Record<string, OrderStatusType[]> = {
    All: [orderStatus.inProgress, orderStatus.complete, orderStatus.cancel],
    inProgress: [orderStatus.inProgress],
    complete: [orderStatus.complete],
  };

  const filterData = useMemo(
    () =>
      data
        .filter((x) => {
          return orderStatusMapper[searchOrderStatus ?? "All"].includes(x.orderStatus);
        })
        .filter((x) => {
          if (searchText) {
            const searchName = (x.userName as string)
              .toUpperCase()
              .includes(searchText.toUpperCase());
            const searchEmail = (x.userEmail as string)
              .toUpperCase()
              .includes(searchText.toUpperCase());
            const searchOrderNo = (x.orderNo as string)
              .toUpperCase()
              .includes(searchText.toUpperCase());
            const searchPrice = /^\d+$/.test(searchText)
              ? x.orderAmount.toString().includes(searchText)
              : false;
            return searchName || searchEmail || searchOrderNo || searchPrice;
          }
          return true;
        })
        .sort((a: any, b: any) => {
          const strA: any = a?.[sortOrderBy] ?? "";
          const strB: any = b?.[sortOrderBy] ?? "";

          if (typeof strA === "number") {
            if (sortOrder === "asc") {
              return strA - strB;
            }
            return strB - strA;
          }
          if (sortOrder === "asc") {
            return strA.localeCompare(strB);
          }
          return strB.localeCompare(strA);
        }),
    [data, searchOrderStatus, searchText, sortOrder, sortOrderBy, orderStatusMapper]
  );

  return {
    allRows: data,
    rows: filterData.slice((page - 1) * showPerPage, (page - 1) * showPerPage + showPerPage),
    totalRowCount: filterData.length,
  };
};
