"use client";
import React from "react";
import { Box, CircularProgress } from "@mui/material";
import { useTranslations } from "next-intl";
import { useQuery } from "@tanstack/react-query";
import PageHeader from "@/components/PageHeader";
import EditButton from "@/components/buttons/EditButton";
import xior from "xior";
import LabelValue from "@/components/LabelValue";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import { ICollection } from "@/interface/ICollection";

interface Props {
  params: { id: string };
}

const CollectionDetail = ({ params: { id } }: Props) => {
  const router = useRouter();
  const t = useTranslations("collection");
  const { data, isLoading, isLoadingError } = useQuery({
    queryKey: ["collections"],
    queryFn: () =>
      xior.get<ICollection>(`/api/collections/${id}`).then((res) => res.data),
  });

  React.useEffect(() => {
    if (isLoadingError) router.push(ROUTES.COLLECTION);
  }, [isLoadingError, router]);

  if (isLoading) {
    return (
      <Box
        sx={{ height: "100%" }}
        display={"flex"}
        justifyContent="center"
        alignItems={"center"}
      >
        <CircularProgress color="primary" />
      </Box>
    );
  }

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={[t("label_title"), data?.collectionId || ""]}>
        <EditButton
          onClick={() => {
            router.push(`${ROUTES.COLLECTION}/${id}/edit`);
          }}
        />
      </PageHeader>
      <Box flex={1} padding="8px 34px">
        <LabelValue
          label={t("label_collection_id")}
          value={data?.collectionId}
        />
        <LabelValue label={t("label_collection_name")} value={data?.name} />
        <LabelValue label={t("label_description")} value={data?.description} />
        <LabelValue
          type={"image"}
          label={t("label_thumbnail")}
          src={data?.photoUrl}
          height={218}
          width={454}
        />
      </Box>
    </Box>
  );
};

export default CollectionDetail;
