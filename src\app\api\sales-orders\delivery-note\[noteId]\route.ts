import { NextRequest} from "next/server";
import {SalesOrderNoteId} from "@/interface/ISalesOrderNoteId"
import {
    authorizedGet,
    getAuthHeaders,
    handleApiError,
} from "@/utils/api";

async function GET(req: NextRequest, { params }: { params: { noteId: string } }){
    try{

        const data = await authorizedGet<SalesOrderNoteId>(
            `/sales-orders/delivery-note/${params.noteId}`,
            await getAuthHeaders(req)
        );
        return Response.json(
            data,
            { status: 200 }
          );
    }catch(error){
        console.error(error); 
        return handleApiError(error);
    }
}

export { GET};