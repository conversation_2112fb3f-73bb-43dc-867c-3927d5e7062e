import { <PERSON><PERSON>, IconButton, Link, TableCell, TableRow, TextField, Typography } from "@mui/material"
import SearchIcon from '@mui/icons-material/Search';
import { IInventory } from "@/interface/IInventory";
import { useRouter } from 'next/navigation';
import { ROUTES } from "@/utils/constants";
import { IProductWarehouseRecords } from "@/interface/IProductWarehouseRecord";
import { ChangeEvent } from "react";
import Image from "next/image";


type Props = {
  getInventoryDto: IInventory
  params: { id: string }
  addProductWarehouseRecords: IProductWarehouseRecords
  handleAddRecordsChange: (addProductWarehouseRecords: IProductWarehouseRecords) => void
}

const SingleInventoryRow = ({ getInventoryDto, params, addProductWarehouseRecords, handleAddRecordsChange }: Props) => {

  const router = useRouter();

  const handleAdjustClick = () => {
    const historyUrl = ROUTES.ADJUSTMENT_HISTORY(params.id);
    router.push(historyUrl);
  }

  const handleAddRecordsFormChange = (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = event.target;

    if (/^\d*$/.test(value)) {
      handleAddRecordsChange({
        ...addProductWarehouseRecords,
        [name]: value
      });
    }
  }

    const renderProductType = () =>{
        if(getInventoryDto.productType === 1){
          return(
            <>
            <Typography>Physical</Typography>
            </>
          )
        }else if(getInventoryDto.productType === 2){
          return(
            <>
            <Typography>Digital</Typography>
            </>
          )
        }
      }

    const handleQueryClick = () =>{
      router.push(`/products/product-list/${getInventoryDto.productId}/edit`)
    }  

    return(
        <>
        <TableRow>
        <TableCell>          
            <IconButton sx={{
                background: "#24378C",
                color:"white",
                '&:hover': {
                  background: "#24378C", 
              },
              }}
              >
            <SearchIcon onClick={handleQueryClick}/>
        </IconButton>
        </TableCell>
        <TableCell>
          {/* <img src={getInventoryDto.thumbnail}
            width="100px"
          /> */}
          <Image
            src={getInventoryDto.thumbnail}
            alt="Product Image"
            width={100}
            height={80}
          />
        </TableCell>
        <TableCell>{getInventoryDto.name}</TableCell>
        <TableCell>
          <TextField id="outlined-basic" variant="outlined"
            name="sku"
            value={addProductWarehouseRecords.sku}
            onChange={handleAddRecordsFormChange}
          />
        </TableCell>
        <TableCell>{renderProductType()}</TableCell>
        <TableCell>
          <TextField id="outlined-basic" variant="outlined"
            name="currentUnavailable"
            type="text"
            value={addProductWarehouseRecords.currentUnavailable || ''}
            onChange={handleAddRecordsFormChange}
            inputProps={{
              maxLength: 10, // 可選，設置最大字符數
              pattern: "[0-9]*",
            }}
          />
        </TableCell>
        <TableCell>
            {/* <TextField id="outlined-basic" variant="outlined" 
             name="currentCommitted"
             value={addProductWarehouseRecords.currentCommitted || ''}
             onChange={handleAddRecordsFormChange}
            /> */}{addProductWarehouseRecords.currentCommitted}
        </TableCell>
        <TableCell>
            {/* <TextField id="outlined-basic" variant="outlined" 
             name="currentQuantity"
             value={addProductWarehouseRecords.currentQuantity || ''}
             onChange={handleAddRecordsFormChange}
            /> */}{addProductWarehouseRecords.currentQuantity}
        </TableCell>
        {/* <TableCell>
            <TextField id="outlined-basic" variant="outlined" />
            </TableCell> */}
      </TableRow>
      <Button onClick={handleAdjustClick}>Adjustment History</Button>
    </>
  )
}

export default SingleInventoryRow