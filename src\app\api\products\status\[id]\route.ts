import {
    authorizedPut,
    getAuthHeaders,
    handleApiError,
  } from "@/utils/api";
import { NextRequest } from "next/server";

async function PUT(req: NextRequest,{ params }: { params: { id: number } }) {

    const productData = await req.json();

    try{

       const data = await authorizedPut(
            `/products/status/${params.id}`,
            await getAuthHeaders(req),
            productData
       ) 
       return Response.json(data, { status: 200 });
    }catch(error){
        return handleApiError(error);
    }
}

export { PUT}