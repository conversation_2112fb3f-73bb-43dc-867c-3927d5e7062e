"use client";
import ModalContainer from "@/components/ModalContainer";
import AddNewButton from "@/components/buttons/AddNewButton";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import TextField from "@/components/input/TextField";
import { TagSchema } from "@/schemas/TagSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Modal, Typography, Grid } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import * as React from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";
import CloseIcon from '@mui/icons-material/Close';
import InputContainer from "@/components/input/InputContainer";

type FormValue = z.infer<typeof TagSchema>;

const CreateTag = () => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const [tags, setTags] = React.useState<any[]>([])
  const [tag, setTag] = React.useState('')

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
    clearErrors,
    reset,
    register
  } = useForm<FormValue>();

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (isSubmitting) return;
    setTag('')
    setTags([])
    reset();
    clearErrors()
    setOpen(false);
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [isSubmitting]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      let uploadTag = tags
      if (uploadTag.length === 0) {
        if (!data || !data.name) {
          setError("name", { type: "custom", message: "at least enter one tag" })
          return;
        }
        uploadTag = [data?.name]
      }
      const {
        data: resData
      } = await xior.post("/api/tags", uploadTag.map((tag) => ({ name: tag })));
      queryClient.invalidateQueries({ queryKey: ["tags"] });
      console.log("uploadTag >> ", uploadTag)
      if (resData?.length !== uploadTag.length) {
        const created = resData.map((item: { id: number, name: string }) => (item.name))
        console.log("created >> ", created)

        const success = uploadTag.filter((tag) => created.includes(tag))
        const failed = uploadTag.filter((tag) => !created.includes(tag))

        if (failed.length > 0) {
          setError("name", { type: "custom", message: `Tag: ${failed.join(", ")} creation failed` })
        }
        return
      }
      setTag('')
      setTags([])
      reset();
      setOpen(false);
    } catch (e) {
      setError("name", { type: "custom", message: "duplicated_tags" });
    }
  };

  const handleOnChange = (event: any) => {
    const {
      value
    } = event.target
    setTag(value)

    if (value[value.length - 1] === ',') {
      setTags([... new Set([
        ...tags,
        value.slice(0, -1)
      ])])
      setTag('')
    }
  }

  return (
    <Box>
      <AddNewButton onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("tags.title_create_tag")}
            </Typography>
          </Box>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <>
                <TextField
                  value={tag}
                  onChange={(event) => {
                    handleOnChange(event)
                    field.onChange(event)
                  }}
                  disabled={isSubmitting}
                  label={t("tags.label_new_tag_name")}
                  placeholder={t("tags.label_split")}
                  error={errors?.name?.message}
                />
                <InputContainer>
                  <div style={{ display: 'flex', flexWrap: 'wrap', columnGap: 4 }}>
                    {
                      tags.map((tag) =>
                        <span
                          style={{
                            padding: 8,
                            border: '1px solid black',
                            borderRadius: 20,
                            marginTop: 4,
                            width: 'fit-content'
                          }}
                          key={`tag_${tag}`}
                        >
                          {tag}
                          <CloseIcon fontSize="small" onClick={() => {
                            setTags([
                              ...tags.filter((tagItem) => tagItem !== tag)
                            ])
                          }} />
                        </span>
                      )
                    }
                  </div>
                </InputContainer>

              </>
            )}
          />

          <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
            <CancelButton disabled={isSubmitting} onAction={handleClose} />
            <SaveButton disabled={isSubmitting} />
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default CreateTag;
