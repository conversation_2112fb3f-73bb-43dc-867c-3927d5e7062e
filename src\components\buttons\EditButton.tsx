import React from "react";
import { Button, ButtonProps, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import EditIcon from "../icons/EditIcon";
import { COLORS } from "@/styles/colors";

interface Props {
  isSmall?: boolean;
  label?: string;
  isPrimay?: boolean;
}

const EditButton = (props: Props & ButtonProps) => {
  const t = useTranslations("common");
  const { isSmall, label, isPrimay = true, sx, ...otherProps } = props;

  const padding = isSmall? "14px 20px": "16px 26px"
  const backgroundColor = isPrimay? COLORS.PRIMARY_1: COLORS.WHITE
  const border = isPrimay? undefined: `2px solid ${COLORS.GREY_3}`
  const fontColor = isPrimay? COLORS.WHITE: COLORS.GREY_8

  return (
    <Button
      variant="contained"
      sx={{
        padding,
        borderRadius: "999px",
        backgroundColor,
        border,
        ...sx
      }}
      {...otherProps}
    >
      <Typography variant={isSmall? "body2": "body1"} fontWeight="bold" color={fontColor}>
        {label || t("button_edit")}
      </Typography>
    </Button>
  );
};

export default EditButton;
