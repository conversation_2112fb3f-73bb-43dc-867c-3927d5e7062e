import {
    authorizedDelete,
    authorizedGet,
    authorizedPut,
    getAuthHeaders,
    handleApiError,
  } from "@/utils/api";
  import { NextRequest } from "next/server";
  import {ProductDetailDto} from '@/interface/IProductDetailDto'

  async function DELETE(req: NextRequest,{ params }: { params: { id: number } }) {
    try {
      await authorizedDelete(
        `/products/${params.id}`,
        await getAuthHeaders(req)
      );
      return Response.json({ status: "success" }, { status: 200 });
    } catch (error) {
      return handleApiError(error);
    }
  }

  
  async function GET(req: NextRequest,{ params }: { params: { id: number } }) {
    try {
      const data = await authorizedGet(
        `/products/${params.id}`,
        await getAuthHeaders(req)
      );
      return Response.json(data, { status: 200 });
    } catch (error) {
      return handleApiError(error);
    }
  }

  async function PUT(req: NextRequest,{ params }: { params: { id: number } }) {

    const productData: ProductDetailDto = await req.json();

    try{

     const data = await authorizedPut(
      `/products/${params.id}`,
      await getAuthHeaders(req),
      productData
     )
     return Response.json(data, { status: 200 });

    }catch(error){
      return handleApiError(error);
    }
  }
  
  export { DELETE , GET , PUT };