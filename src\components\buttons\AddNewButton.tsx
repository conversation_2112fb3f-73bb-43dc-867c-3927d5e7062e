import React from "react";
import { Button, ButtonProps } from "@mui/material";
import { useTranslations } from "next-intl";
import AddRoundedIcon from "@mui/icons-material/AddRounded";
import { ButtonHeadTable } from "./styled";

interface AddNewButtonProps extends ButtonProps {
  label?: string
}

const AddNewButton = (props: AddNewButtonProps) => {
  const t = useTranslations("common");

  return (
    <ButtonHeadTable variant="contained" sx={{marginRight:0, minWidth:'100px', ...(props.sx || {})}} {...props}> 
      {props.label || t("button_add_new")}
      {props.children}
    </ButtonHeadTable>
  );
};

export default AddNewButton;
