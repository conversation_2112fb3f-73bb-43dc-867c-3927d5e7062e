"use client";
import * as React from "react";
import { useTranslations } from "next-intl";
import { Typography, Button, TextFieldProps } from "@mui/material";
import { styled } from "@mui/material/styles";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import InputContainer, { InputContainerProps } from "./InputContainer";
import "react-international-phone/style.css";
import AddNewButton from "../buttons/AddNewButton";
import { ACCEPTED_IMAGE_TYPES } from "@/utils/constants";

interface Props extends InputContainerProps {
    fullWidth?: boolean;
    defaultCountry?: string;
    value: string;
    onChange: (file: File | undefined) => void;
}

const headers = [{
    label: "label_picture"
}, {
    label: "label_filename",
}, {
    label: "label_sorting"
}, {
    label: false
}]

type Column = {
    image: any,
    name: string,
    sorting: number,
    action: any
}

export default function ProductTable({
    label,
    description,
    required,
    defaultCountry = "hk",
    value,
    onChange,
    width,
    fullWidth,
    error,
    ...otherProps
}: Props & Omit<TextFieldProps, "error">) {
    const [rows, setRows] = React.useState<Array<Column>>([])
    const t = useTranslations()

    const handleOnChange = () => {

    }

    return (
        <InputContainer>
            <Typography fontSize={14}>
                {t("product.label_list_of_thumbnail")}
            </Typography>
            <Typography fontSize={12} color={'grey'}>
                <i>
                    {
                        t("file_upload.image_video_metadata").split("\n").map((item: string) => (
                            <>{item}<br /></>
                        ))
                    }
                </i>
            </Typography>
            <TableContainer component={Paper}>
                <Table sx={{ width: 450 }} aria-label="product table">
                    <TableHead>
                        <TableRow>
                            {
                                headers.map((header, index) => (
                                    // <TableCell key={header.id}>{ header?.label && t(`product.${header.label}`)}</TableCell>
                                    <TableCell key={index}>{header?.label && t(`product.${header.label}`)}</TableCell>
                                ))
                            }
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {rows.map((row) => (
                            <TableRow
                                key={row.name}
                                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                            >
                                <TableCell component="th" scope="row">
                                    {row.image}
                                </TableCell>
                                <TableCell align="right">{row.name}</TableCell>
                                <TableCell align="right">{row.sorting}</TableCell>
                                <TableCell align="right">{""}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
            <AddNewButton onClick={handleOnChange}>
            </AddNewButton>
        </InputContainer>
    );
}
