"use client";
import LabelValue from "@/components/LabelValue";
import PageHeader from "@/components/PageHeader";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import ContactMethodInput from "@/components/input/ContactMethodInput";
import FileInput from "@/components/input/FileInput";
import InputContainer from "@/components/input/InputContainer";
import PhoneInput from "@/components/input/PhoneInput";
import Select from "@/components/input/Select";
import SelectCountry from "@/components/input/SelectCountry";
import SelectGender from "@/components/input/SelectGender";
import SocialMediaInput from "@/components/input/SocialMediaInput";
import TextField from "@/components/input/TextField";
import useFileUpload from "@/hooks/useFileUpload";
import { IProfile } from "@/interface/IProfile";
import { ProfileSchema } from "@/schemas/ProfileSchema";
import { CONTACTS_METHODS, ROUTES, SOCIAL_MEDIAS } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, CircularProgress } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { NextPage } from "next";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useEffect, useMemo } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";

type FormValue = z.infer<typeof ProfileSchema>;
const defaultValues = {
  gender: "",
  identity: "",
  dateOfBirth: null,
  countryCode: "hk",
  phoneNumber: "",
  photoUrl: "",
  name: "",
  addressLine1: "",
  addressLine2: "",
  addressLine3: "",
  contacts: {
    whatsapp: "",
    telegram: "",
    other: "",
  },
  socialMedias: {
    instagram: "",
    facebook: "",
    youtube: "",
    x: "",
    linkedin: "",
    tiktok: "",
    pinterest: "",
    other: "",
  },
};

const EditProfile: NextPage = () => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const router = useRouter();
  const { uploadFile } = useFileUpload();

  const {
    handleSubmit,
    control,
    formState: { isDirty, isSubmitting, errors },
    setError,
    reset,
  } = useForm<FormValue>({
    resolver: zodResolver(ProfileSchema),
    defaultValues,
  });

  const { data, isLoading, isLoadingError } = useQuery({
    queryKey: ["profile"],
    queryFn: () => xior.get<IProfile>("/api/profile").then((res) => res.data),
  });

  useEffect(() => {
    if (data) {
      const { email, countryCode, dateOfBirth, ...otherValues } = data;
      reset({
        ...otherValues,
        countryCode: countryCode || "hk",
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
      });
    }
  }, [data, reset]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const { photoUrl, dateOfBirth, ...otherData } = data;
      await xior.put("/api/profile/", {
        ...otherData,
        dateOfBirth: dateOfBirth ? format(dateOfBirth, "yyyy-MM-dd") : null,
        photoUrl: await uploadFile(photoUrl, "users"),
      });
      queryClient.invalidateQueries({ queryKey: ["profile"] });
      router.push(ROUTES.PROFILE);
    } catch (e) {
      setError("name", { message: "unknown_error" });
    }
  };

  useEffect(() => {
    if (isLoadingError) router.push(ROUTES.ROOMS);
  }, [isLoadingError, router]);

  const otherContactInputs = useMemo(() => {
    return (
      <>
        {Object.keys(CONTACTS_METHODS).map((item, index) => (
          <Controller
            key={item}
            name={`contacts.${item}` as any}
            control={control}
            render={({ field }) => (
              <ContactMethodInput
                label={
                  index === 0 ? t("profile.label_contact_methods") : undefined
                }
                contactMethod={item as any}
                value={field.value as string}
                onChange={field.onChange}
                disabled={isSubmitting}
                error={(errors?.contacts as any)?.[item]}
              />
            )}
          />
        ))}
      </>
    );
  }, [control, errors?.contacts, isSubmitting, t]);

  const socialMediaInputs = useMemo(() => {
    return (
      <>
        {Object.keys(SOCIAL_MEDIAS).map((item, index) => (
          <Controller
            key={item}
            name={`socialMedias.${item}` as any}
            control={control}
            render={({ field }) => (
              <SocialMediaInput
                label={
                  index === 0 ? t("profile.label_social_media") : undefined
                }
                socialMedia={item as any}
                value={field.value as string}
                onChange={field.onChange}
                disabled={isSubmitting}
                error={(errors?.socialMedias as any)?.[item]}
              />
            )}
          />
        ))}
      </>
    );
  }, [control, errors?.socialMedias, isSubmitting, t]);

  if (isLoading) {
    return (
      <Box
        sx={{ height: "100%" }}
        display={"flex"}
        justifyContent="center"
        alignItems={"center"}
      >
        <CircularProgress color="primary" />
      </Box>
    );
  }

  return (
    <Box
      sx={{ height: "100%" }}
      display={"flex"}
      flexDirection={"column"}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
    >
      <PageHeader title={[t("profile.label_title"), t("common.title_edit")]}>
        <>
          <CancelButton
            disabled={isSubmitting}
            onAction={() => router.push(ROUTES.PROFILE)}
          />
          <SaveButton disabled={!isDirty || isSubmitting} />
        </>
      </PageHeader>
      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
        maxWidth={450}
      >
        <Controller
          name="photoUrl"
          control={control}
          render={({ field }) => (
            <FileInput
              value={field.value}
              onChange={field.onChange}
              metadata={t("file_upload.image_metadata")}
              disabled={isSubmitting}
              error={errors.photoUrl?.message}
              type="image"
              size="small"
            />
          )}
        />
        <Controller
          name="identity"
          control={control}
          render={({ field }) => (
            <Select
              required
              data={[
                {
                  label: t("profile.identity_value_personal"),
                  value: "personal",
                },
                {
                  label: t("profile.identity_value_company"),
                  value: "company",
                },
              ]}
              disabled={isSubmitting}
              label={t("profile.label_identity")}
              error={errors?.identity?.message}
              sx={{ width: 360 }}
              value={field.value}
              onChange={field.onChange}
            />
          )}
        />
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              fullWidth={false}
              disabled={isSubmitting}
              label={t("profile.label_name")}
              error={errors?.name?.message}
              sx={{ width: 360 }}
            />
          )}
        />
        <Controller
          name="gender"
          control={control}
          render={({ field }) => (
            <SelectGender
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("profile.label_gender")}
              error={errors?.gender?.message}
              sx={{ width: 360 }}
            />
          )}
        />
        <LabelValue label={t("profile.label_email")} value={data!.email} />
        <Controller
          name="phoneNumber"
          control={control}
          render={({ field }) => (
            <PhoneInput
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("profile.label_phone_number")}
              error={errors?.phoneNumber?.message}
              width={360}
              fullWidth
            />
          )}
        />
        {otherContactInputs}
        <Controller
          name="dateOfBirth"
          control={control}
          render={({ field }) => (
            <InputContainer label={t("profile.label_dob")}>
              <DatePicker
                disabled={isSubmitting}
                disableFuture
                format="dd/MM/yyyy"
                value={field.value}
                onChange={field.onChange}
                views={["year", "month", "day"]}
                sx={{ width: 360 }}
              />
            </InputContainer>
          )}
        />
        <Controller
          name="addressLine1"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              fullWidth={false}
              disabled={isSubmitting}
              label={t("profile.label_address")}
              sx={{ width: 360 }}
            />
          )}
        />
        <Controller
          name="addressLine2"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              fullWidth={false}
              disabled={isSubmitting}
              sx={{ width: 360 }}
            />
          )}
        />
        <Controller
          name="addressLine3"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              fullWidth={false}
              disabled={isSubmitting}
              sx={{ width: 360 }}
            />
          )}
        />
        <Controller
          name="countryCode"
          control={control}
          render={({ field }) => (
            <SelectCountry
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("profile.label_country")}
              error={errors?.countryCode?.message}
              width={360}
              fullWidth
            />
          )}
        />
        {socialMediaInputs}
      </Box>
    </Box>
  );
};

export default EditProfile;
