"use client";
import type { NextPage } from "next";
import { useCallback, useEffect, useState } from "react";
import { Box, Button, Typography } from "@mui/material";
import Link from "next/link";
import { useTranslations } from "next-intl";
import PublicPageContainer from "@/components/PublicPageContainer";
import SubmitButton from "@/components/buttons/SubmitButton";
import TextField from "@/components/input/TextField";
import Image from "next/image";
import { ROUTES } from "@/utils/constants";
import { VerifyCodeSchema } from "@/schemas/VerifyCodeSchema";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior, { XiorError } from "xior";
import { useRouter, useSearchParams } from "next/navigation";

type FormValue = z.infer<typeof VerifyCodeSchema>;

const Verify: NextPage = () => {
  const t = useTranslations();
  const router = useRouter();
  const [resendCounter, setResendCounter] = useState(0);
  const searchParams = useSearchParams();
  const email = searchParams.get("email");

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
  } = useForm<FormValue>({
    defaultValues: { email: email || "", otp: "" },
    resolver: zodResolver(VerifyCodeSchema),
  });

  const resendCode = useCallback(() => {
    xior
      .post("/api/auth/resend-otp", { email })
      .then(() => {
        setResendCounter(30);
      })
      .catch((e) => {
        setError("otp", { message: e.response.data });
      });
  }, [email, setError]);

  useEffect(() => {
    if (!email) {
      router.push(ROUTES.LOGIN);
    }
  }, [email, router]);

  useEffect(() => {
    if (resendCounter > 0) {
      setTimeout(() => {
        setResendCounter((state) => state - 1);
      }, 1000);
    }
  }, [resendCounter]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      await xior.post("/api/auth/verify-otp", data);
      router.push(
        `${ROUTES.SETUP_PROFILE}?email=${encodeURIComponent(data.email)}&otp=${
          data.otp
        }`
      );
    } catch (e) {
      setError("otp", { message: (e as XiorError)?.response?.data });
    }
  };

  return (
    <PublicPageContainer>
      <Typography variant="h1" sx={{ marginBottom: 1 }}>
        {t("verify.title")}
      </Typography>
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{ marginBottom: 2.5 }}
      >
        {t("verify.message")}
      </Typography>
      <Typography
        variant="body1"
        sx={{ marginBottom: 2, whiteSpace: "pre", textAlign: "center" }}
      >
        {t("verify.check_inbox")}
      </Typography>
      <Box
        component={"form"}
        style={{ width: "100%" }}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="otp"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting}
              placeholder={t("verify.placeholder_sign_up_code")}
              error={errors?.otp?.message}
            />
          )}
        />
        <Button
          onClick={resendCode}
          variant="text"
          disableFocusRipple
          disabled={resendCounter > 0 || isSubmitting}
          sx={{
            textDecorationLine: "underline",
            textTransform: "none",
            p: 0,
            color: "text.primary",
            fontWeight: 700,
          }}
        >
          {t("verify.button_resend_sign_up_code")}{" "}
          {resendCounter > 0 && `(${resendCounter})`}
        </Button>
        <SubmitButton
          fullWidth
          type="submit"
          variant="contained"
          color="primary"
          disabled={isSubmitting}
          sx={{ marginTop: 1.25, marginBottom: 2.75 }}
        >
          {t("verify.button_create_new_account")}
        </SubmitButton>
      </Box>

      <Typography
        component={"span"}
        variant="body1"
        sx={{ marginBottom: 4.75, textAlign: "center" }}
      >
        {t.rich("signup.agree_to_tos_and_pp", {
          tos: (chunks) => (
            <Link href={ROUTES.HOME} className="link-grey">
              {chunks}
            </Link>
          ),
        })}
      </Typography>
      <Typography variant="body1" component={"span"}>
        {t.rich("signup.already_have_an_account", {
          arrow: () => (
            <Box
              sx={{
                height: 20,
                width: 20,
                position: "relative",
                mx: 0.5,
                display: "inline-block",
                top: 4,
              }}
            >
              <Image src={"/images/arrow-right.svg"} layout="fill" alt="" />
            </Box>
          ),
          login: (chunks) => (
            <Link href={ROUTES.LOGIN} className="link-black">
              {chunks}
            </Link>
          ),
        })}
      </Typography>
    </PublicPageContainer>
  );
};

export default Verify;
