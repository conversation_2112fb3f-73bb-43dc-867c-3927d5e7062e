import { Box, FormControl, InputLabel, MenuItem, Select, SelectChangeEvent, Stack, styled, TextField, Typography } from "@mui/material";
import { IPaymentCreateDto } from '@/interface/IPaymentGateway';
import { ChangeEvent } from "react";

type Props = {
  paymentCreateDto: IPaymentCreateDto;
  handlePaymentDto:(paymentCreateDto:IPaymentCreateDto)=> void
}

const Shipping = ({paymentCreateDto,handlePaymentDto}:Props) =>{

  const handleTextField = (event: ChangeEvent <HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent) =>{
    const { name, value } = event.target;

    const updatedDeliveryAddress = [...paymentCreateDto.deliveryAddress || []];
  
    if (name === "deliveryAddress1") {
      updatedDeliveryAddress[0] = value || ''; 
    } else if (name === "deliveryAddress2") {
      updatedDeliveryAddress[1] = value || ''; 
    }

    handlePaymentDto({
      ...paymentCreateDto,
      [name]: value || '',
      deliveryAddress: updatedDeliveryAddress
    });
  }

    const Item = styled('div')(({ theme }) => ({
        backgroundColor: '#fff',
        ...theme.typography.body2,
        padding: theme.spacing(1),
        textAlign: 'center',
        color: theme.palette.text.secondary,
        ...theme.applyStyles('dark', {
          backgroundColor: '#1A2027',
        }),
      }));

      const textFieldStyle = {
        textAlign: 'left',
        marginBottom: '5px', 
      };

    return(
        <>
        <br/>
        <Typography variant="h3">Shipping Details</Typography><br/>
        <Typography sx={{mb:"10px",textAlign: 'left'}}>Country / Region</Typography>
        <FormControl fullWidth>
        <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            name="deliveryAddressCountry"
            value={paymentCreateDto.deliveryAddressCountry || ''}
            onChange={handleTextField}
        >
            <MenuItem value="hk">Hong Kong</MenuItem>
            {/* <MenuItem value="taiwan">Taiwan</MenuItem>
            <MenuItem value="singapore">Singapore</MenuItem> */}
        </Select>
        </FormControl><br/><br/>
        <Typography sx={textFieldStyle}>Address Line 1</Typography>
        <TextField 
        id="outlined-basic" 
        // label="Address Line 1" 
        variant="outlined" 
        fullWidth
        name="deliveryAddress1"
        value={paymentCreateDto.deliveryAddress?.[0]}
        onChange={handleTextField}
        />
        <br/><br/>
        <Typography sx={textFieldStyle}>Address Line 2</Typography>
        <TextField 
        id="outlined-basic" 
        // label="Address Line 2" 
        variant="outlined" 
        fullWidth
        name="deliveryAddress2"
        value={paymentCreateDto.deliveryAddress?.[1]}
        onChange={handleTextField}
        />
        <br/><br/>
        <Typography sx={textFieldStyle}>Postal Code</Typography>
        <TextField 
        id="outlined-basic" 
        // label="Postal Code" 
        variant="outlined" 
        fullWidth
        name="deliveryAddressPostalCode"
        value={paymentCreateDto.deliveryAddressPostalCode}
        onChange={handleTextField}
        />
        <br/><br/>
        </>
    )
}

export default Shipping;