"use client";
import AddNewButton from "@/components/buttons/AddNewButton";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import ContactInput from "@/components/input/ContactInput";
import ContactMethodInput from "@/components/input/ContactMethodInput";
import FileInput from "@/components/input/FileInput";
import SelectMemberGroup from "@/components/input/SelectMemberGroup";
import SelectTag from "@/components/input/SelectTag";
import SocialMediaInput from "@/components/input/SocialMediaInput";
import SocialNetworkInput from "@/components/input/SocialNetworkInput";
import TextField from "@/components/input/TextField";
import LabelValue from "@/components/LabelValue";
import PageHeader from "@/components/PageHeader";
import useFileUpload from "@/hooks/useFileUpload";
import { IRoom } from "@/interface/IRoom";
import { RoomSchema } from "@/schemas/RoomSchema";
import { CONTACTS_METHODS, ROUTES, SOCIAL_MEDIAS, UNITY_ENDPOINT } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Box,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  InputAdornment,
  Switch,
  Typography
} from "@mui/material";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";
import DeleteRoom from "../../components/DeleteRoom";

interface Props {
  params: { id: string };
}

type FormValue = z.infer<typeof RoomSchema>;

const RoomInformation = ({ params: { id } }: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const router = useRouter();
  const { uploadFile } = useFileUpload();
  const [contact, setContact] = useState<Array<any>>([])
  const [socialNetwork, setSocialNetwork] = useState<Array<any>>([])

  const {
    handleSubmit,
    control,
    formState: { isDirty, isSubmitting, errors },
    setError,
    getValues,
    watch,
    reset,
  } = useForm<FormValue>({
    // resolver: zodResolver(RoomSchema),
    defaultValues: {
      roomName: "",
      roomUrl: "",
      description: "",
      tags: [],
      memberGroups: [],
      visibleToPublic: true,
      openToAllMembers: true,
      roomSocialUrls: [],
      roomContacts: [],
      socialMedias: ""
    },
  });

  const { data, isLoading, isLoadingError } = useQuery({
    queryKey: ["rooms", id],
    queryFn: () => xior.get<IRoom>(`/api/rooms/${id}`).then((res) => res.data),
  });

  useEffect(() => {
    if (data) {
      const { id, roomId, roomSocialUrls, roomContacts, ...otherValues } = data;
      setContact(roomContacts)
      setSocialNetwork(roomSocialUrls)
      // reset({
      //   ...otherValues,
      //   roomSocialUrls,
      //   roomContacts
      // });
    }
  }, [data, reset]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const { logoUrl, thumbnailUrl, roomSocialUrls, roomContacts, bgMusicUrl, ...otherData } = data;
      let parsedContacts: any = {}
      let parsedSocialNetwork: any = {}

      roomSocialUrls?.map((item: { method: string, value: string }) => {
        parsedContacts[item.method] = item.value
      })
      roomContacts?.map((item: { method: string, value: string }) => {
        parsedSocialNetwork[item.method] = item.value
      })

      let parsedData = {
        ...otherData,
        roomSocialUrls: parsedSocialNetwork,
        roomContacts: parsedContacts
      }

      await RoomSchema.parseAsync(parsedData)

      await xior.put(`/api/rooms/${id}`, {
        ...otherData,
        ...parsedData,
        roomSocialUrls,
        roomContacts,
        logoUrl: await uploadFile(logoUrl, "rooms"),
        thumbnailUrl: await uploadFile(thumbnailUrl, "rooms"),
        bgMusicUrl: await uploadFile(bgMusicUrl, "rooms"),
      });
      queryClient.invalidateQueries({ queryKey: ["rooms"] });
      router.push(ROUTES.ROOMS);
    } catch (e) {
      console.log("error >> ", e)
      // if (e?.issues) {
      //   e?.issues?.map((err) => {
      //     if (err?.path) {
      //       setError(err.path[0], {
      //         type: "custom",
      //         message: "required",
      //       })
      //     }
      //   })
      // }
    }
  };

  useEffect(() => {
    if (isLoadingError) router.push(ROUTES.ROOMS);
  }, [isLoadingError, router]);

  // const handleContactOnChange = (value: any, field: any, idx: number, setter: Function) => {
  //   const cloneContact: Array<any> = [...contact]

  //   cloneContact[idx] = {
  //     ...cloneContact[idx],
  //     [field]: value
  //   }

  //   setter(cloneContact)
  //   setContact(cloneContact)
  // }
  const handleContactOnChange = () => { }

  const otherContactInputs = useMemo(() => {
    return (
      <>
        <Typography fontSize={14}>
          {t("profile.label_contact_methods")}
        </Typography>
        <Controller
          key={`roomContacts`}
          name={`roomContacts` as any}
          control={control}
          render={({ field }) => (
            <>
              {
                contact.map((item, index) => (
                  <ContactInput
                    key={item.id}
                    contactMethod={item as any}
                    value={field?.value?.[index] ?? {}}
                    // index={index}
                    setter={field.onChange}
                    onChange={handleContactOnChange}
                    disabled={isSubmitting}
                    error={(errors?.contacts as any)?.[item]}
                  />
                ))
              }
            </>
          )}
        />
        <AddNewButton sx={{ width: 100 }} onClick={() => setContact([...contact, {}])} />
      </>
    );
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [contact, getValues("roomContacts"), control, errors?.contacts, isSubmitting, t]);

  // const handleSocialNetworkOnChange = (value: any, field: any, idx: number, setter: Function) => {
  //   const cloneSocialNetwork: Array<any> = [...socialNetwork]

  //   cloneSocialNetwork[idx] = {
  //     ...cloneSocialNetwork[idx],
  //     [field]: value
  //   }

  //   setter(cloneSocialNetwork)
  //   setSocialNetwork(cloneSocialNetwork)
  // }
  const handleSocialNetworkOnChange = () => { }

  const socialNetworkInputs = useMemo(() => {
    return (
      <>
        <Typography fontSize={14}>
          {t("profile.label_social_media")}
        </Typography>
        <Controller
          key={`roomSocialUrls`}
          name={`roomSocialUrls` as any}
          control={control}
          render={({ field }) => (
            <>
              {
                socialNetwork.map((item, index) => (
                  <SocialNetworkInput
                    key={item.id}
                    socialNetwork={item as any}
                    value={field?.value?.[index] ?? {}}
                    index={index}
                    setter={field.onChange}
                    onChange={handleSocialNetworkOnChange}
                    disabled={isSubmitting}
                    error={(errors?.roomSocialUrls as any)?.[item]}
                  />
                ))
              }
            </>
          )}
        />
        <AddNewButton sx={{ width: 100 }} onClick={() => setSocialNetwork([...socialNetwork, {}])} />
      </>
    );
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [socialNetwork, getValues("roomSocialUrls"), control, errors?.socialMedias, isSubmitting, t]);

  if (isLoading) {
    return (
      <Box
        sx={{ height: "100%" }}
        display={"flex"}
        justifyContent="center"
        alignItems={"center"}
      >
        <CircularProgress color="primary" />
      </Box>
    );
  }

  return (
    <Box
      sx={{ height: "100%" }}
      display={"flex"}
      flexDirection={"column"}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
    >
      <PageHeader
        title={[
          t("room_information.label_title"),
          t("room_information.title_edit_room"),
        ]}
      >
        <>
          {data && <DeleteRoom room={data} />}
          <CancelButton
            disabled={isSubmitting}
            onAction={() => router.push(`${ROUTES.ROOMS}`)}
          />
          <SaveButton disabled={isSubmitting || !isDirty} />
        </>
      </PageHeader>
      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
        maxWidth={450}
      >

        <Controller
          name="roomName"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              disabled={isSubmitting}
              label={t("room_information.label_room_name")}
              error={errors?.roomName?.message}
            />
          )}
        />
        <Controller
          name="roomUrl"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              disabled={field.value || isSubmitting ? true : false}
              label={t("room_information.label_room_url")}
              error={errors?.roomUrl?.message}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" sx={{ mr: 0 }}>
                    {UNITY_ENDPOINT}
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("room_information.label_description")}
              placeholder={t("room_information.placeholder_description")}
              error={errors?.description?.message}
              multiline
              rows={8}
            />
          )}
        />
        <Controller
          name="tags"
          control={control}
          render={({ field }) => (
            <SelectTag
              value={field?.value || []}
              onChange={field.onChange}
              label={t("room_information.label_tags")}
              placeholder={t("room_information.placeholder_tags")}
              disabled={isSubmitting}
              error={errors?.description?.message}
            />
          )}
        />
        <Controller
          name="logoUrl"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("room_information.label_logo")}
              </Typography>
              <Typography fontSize={12} color={'grey'}>
                <i>
                  {
                    t("file_upload.image_metadata").split("\n").map((item: string) => (
                      <>{item}<br /></>
                    ))
                  }
                </i>
              </Typography>
              <FileInput
                value={field.value}
                onChange={field.onChange}
                metadata={t("file_upload.logo_upload")}
                disabled={isSubmitting}
                error={errors.logoUrl?.message}
                type="image"
              />
            </>
          )}
        />
        <Controller
          name="thumbnailUrl"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("room_information.label_thumbnail_image_video")}
              </Typography>
              <Typography fontSize={12} color={'grey'}>
                <i>
                  {
                    t("file_upload.image_video_metadata").split("\n").map((item: string) => (
                      <>{item}<br /></>
                    ))
                  }
                </i>
              </Typography>
              <FileInput
                value={field.value}
                onChange={field.onChange}
                metadata={t("file_upload.thumbnail_multimedia_upload")}
                disabled={isSubmitting}
                error={errors.thumbnailUrl?.message}
                type="image_video"
              />
            </>
          )}
        />
        <Controller
          name="bgMusicUrl"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("room_information.label_room_bg_music")}
              </Typography>
              <Typography fontSize={12} color={'grey'}>
                <i>
                  {
                    t("file_upload.audio_metadata").split("\n").map((item: string) => (
                      <>{item}<br /></>
                    ))
                  }
                </i>
              </Typography>
              <FileInput
                value={field.value}
                onChange={field.onChange}
                metadata={t("file_upload.background_music_upload")}
                disabled={isSubmitting}
                error={errors.bgMusicUrl?.message}
                type="audio"
              />
            </>
          )}
        />
        {otherContactInputs}
        {socialNetworkInputs}
        <Controller
          name="visibleToPublic"
          control={control}
          render={({ field: { onChange, value } }) => (
            <FormControlLabel
              sx={{ alignSelf: "start", mx: 0, mb: 1.5 }}
              labelPlacement="start"
              label={t("room_information.label_visible_to_public")}
              control={
                <Switch
                  sx={{ ml: 1 }}
                  disabled={isSubmitting}
                  checked={value}
                  onChange={onChange}
                />
              }
            />
          )}
        />
        <Controller
          name="memberGroups"
          control={control}
          render={({ field: { onChange, value } }) => (
            <SelectMemberGroup
              disabled={watch("openToAllMembers") || isSubmitting}
              label={t("room_information.label_room_exclusive_to")}
              placeholder="Member Group"
              error={errors?.memberGroups?.message}
              value={value}
              onChange={onChange}
            />
          )}
        />
        <Controller
          name="openToAllMembers"
          control={control}
          render={({ field: { onChange, value } }) => (
            <FormControlLabel
              sx={{ alignSelf: "start", mx: 0, mb: 1.5 }}
              labelPlacement="end"
              label={t("room_information.label_open_to_all_member")}
              control={
                <Checkbox
                  disabled={isSubmitting}
                  sx={{ ml: "-9px" }}
                  checked={value}
                  onChange={onChange}
                />
              }
            />
          )}
        />
      </Box>
    </Box>
  );
};

export default RoomInformation;
