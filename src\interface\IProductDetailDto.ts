// export interface ProductDetailDto {
//     id:           number;
//     name:         string;
//     owner:        number;
//     ownerName:    string;
//     description:  string;
//     status:       number;
//     thumbnail:    string;
//     createdAt:    number;
//     productType:  number;
//     audio:        string;
//     tags:         string;
//     compareAt:    number;
//     type:         number;
//     collections:  string;
//     price:        number;
//     category:     number;
//     quantity:     number;
//     currency:     "USD" | "HKD" | "EUR" | "GBP" | "JPY" | "KRW";
//     isDigital:    number;
//     categoryName: string;
//     productImage: ProductImage[];
// }

// export interface ProductImage {
//     url:          string;
//     name:         string;
//     resourceType: string;
// }

export interface ProductDetailDto {
    id:          number;
    name:         string;
    owner:        string;
    ownerName?:    string;
    description:  string;
    thumbnail:    string;
    createdAt:    number;
    compareAt:    string;
    status:       number;
    weight:      number;
    weightUnit:  string;
    productType:  number;
    audio:        string;
    price:        number;
    category:     number;
    openToAllMembers:boolean;
    taxIncluded:boolean;
    tax:         number;
    quantity:     number;
    type:         number;
    currency:     "USD" | "HKD" | "EUR" | "GBP" | "JPY" | "KRW";
    isDigital:    boolean;
    categoryName?: string;
    productImage: ProductImage[];
    ownerInfo: IOwner;
    tags:Tags[];
    collections: Collection[];
    memberGroup: MemberGroup[];
}

export interface ProductImage {
    url:          string;
    name:         string;
    resourceType: string;
}

export interface MemberGroup {
    id: number;
    name: string;
    userId:number;
}

export interface IOwner {
    id: number;
    ownerId: string;
    name: string;
    gender: string;
    countryCode: string;
    photoUrl: string;
    email: string;
    introduction: string;
    createdAt: string;
    userId:number;
    socialMedias?: Record<string, string>;
    socialUrls?: Array<any>;
    thumbnailUrl: string;
}

export interface Tags {
    id:number;
    name:string;
    userId:string;
}

export interface Collection{
    id:number;
    name:string;
    description:string;
    photoUrl:string;
    createdAt: number;
    updatedAt:number;
    userId:number;
}