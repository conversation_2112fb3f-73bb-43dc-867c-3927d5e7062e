import type { NextPage } from "next";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import ImportButton from "@/components/buttons/ImportButton";
import ExportButton from "@/components/buttons/ExportButton";
import TagsTable from "./components/TagsTable";
import CreateTag from "./components/CreateTag";

const Tags: NextPage = () => {
  const t = useTranslations("tags");

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={t("label_title")}>
        <>
          <ExportButton  />
          <ImportButton  />
          <CreateTag />
        </>
      </PageHeader>
      <Box flex={1} padding="26px 34px">
        <TagsTable />
      </Box>
    </Box>
  );
};

export default Tags;
