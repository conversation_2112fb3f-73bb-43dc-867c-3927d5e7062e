"use client";
import * as React from "react";
import TextField from "./TextField";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { InputContainerProps } from "./InputContainer";
import { IOwner } from "@/interface/IOwner";

interface OwnerOption {
  inputValue?: string;
  name: string;
  id?: number;
}

const filter = createFilterOptions<OwnerOption>();

interface Props extends InputContainerProps {
  placeholder?: string;
  disabled?: boolean;
  // onChange: (value: IOwner[]) => void;
  // value: IOwner[];
  // defaultValue?: IOwner[];
  onChange: (value: IOwner | null) => void;
  value?: IOwner | null | any;
  defaultValue?: IOwner | null;
}

export default function SelectOwner({ onChange, value, ...otherProps }: Props) {
  const dataQuery = useQuery({
    queryKey: ["owners"],
    queryFn: async () =>
      xior
        .get<IListResponse<IOwner>>("/api/owners")
        .then((res) => ({
          items: res.data.items as OwnerOption[],
          total: res.data.count,
        }))
  });

  return (
    <React.Fragment>
      <Autocomplete
        multiple
        freeSolo
        disableCloseOnSelect
        selectOnFocus
        clearOnBlur
        handleHomeEndKeys
        options={dataQuery.data?.items || []}
        // value={value}
        // onChange={(event, newValue) => {
        //     onChange(event)
        // }}
        value={value ? [value] : []}
        onChange={(event, newValue) => {
          const filteredValue = newValue.filter((item): item is IOwner => typeof item !== "string");
          onChange(filteredValue.length > 0 ? filteredValue[0] : null);
        }}
        filterOptions={(options, params) => {
          const filtered = filter(options, params);
          if (params.inputValue !== "") {
            filtered.push({
              inputValue: params.inputValue,
              name: `Add "${params.inputValue}"`,
            });
          }

          return filtered;
        }}
        getOptionLabel={(option) =>
          typeof option === "string" ? option : option.inputValue || option.name
        }
        isOptionEqualToValue={(option, value) => option.name === value.name}
        renderOption={(props, option) => <li {...props}>{option.name}</li>}
        renderInput={(params) => <TextField {...params} {...otherProps} />}
      />
    </React.Fragment>
  );
}
