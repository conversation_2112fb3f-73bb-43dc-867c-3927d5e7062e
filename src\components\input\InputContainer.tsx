"use client";
import { Typography } from "@mui/material";
import Box from "@mui/material/Box";
import { useTranslations } from "next-intl";
import { CSSProperties, ReactNode } from "react";

export interface InputContainerProps {
  label?: string;
  description?: string;
  required?: boolean;
  width?: number;
  error?: string;
  labelStyle?: CSSProperties;
  blockStyle?: CSSProperties;
}

export default function InputContainer({
  label,
  description,
  required,
  width,
  children,
  error,
  labelStyle,
  blockStyle,
}: InputContainerProps & { children: ReactNode }) {
  const t = useTranslations("error");

  return (
    <Box sx={{ position: 'relative', width, ...blockStyle }}>
      {label && (
        <Typography
          component={"div"}
          variant="body1"
          sx={{ ...labelStyle }}
        >
          {label}
          {required && (
            <Typography display={"inline"} sx={{ color: "#FF0000" }}>
              *
            </Typography>
          )}
          {description && (
            <Typography color="text.secondary">{description}</Typography>
          )}
        </Typography>
      )}
      {children}
      {error && (
        <Typography
          color="error"
          fontSize={12}
          fontWeight={400}
          sx={{ mx: "14px", mt: "4px" }}
        >
          {t(error)}
        </Typography>
      )}
    </Box>
  );
}
