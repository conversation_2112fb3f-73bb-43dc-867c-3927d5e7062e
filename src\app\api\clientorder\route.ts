import {
    authorizedGet,
    getAuthHeaders,
    authorizedPut,
    handleApiError,
    authorizedPost,
  } from "@/utils/api";
import { NextRequest , NextResponse} from "next/server";
import {ClientOrderDetails} from "@/interface/IClientOrderDetails";

async function GET(req: NextRequest) {
    
    try{

        const data = await authorizedGet<ClientOrderDetails>(
            '/clientorder',
            await getAuthHeaders(req)
        )

        return Response.json(data, { status: 200 });

    }catch(error){
        return handleApiError(error);
    }

}

export { GET };