"use client";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  styled,
  TextField,
} from "@mui/material";
import { useTranslations } from "next-intl";
import { countWords } from "@/utils/wordCount";
import { SaleOrderDetailType } from "../../../_schema/saleOrder.schema";
import { updateOrderRemark } from "../../../_actions/updateOrderRemark";
import { revalidateRetrieveOrderById } from "../../../_actions/retrieveOrderById.action";

interface SalesOrderRemarkEditProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  saleOrderDetail?: SaleOrderDetailType;
}

const StyledDialog = styled(Dialog)(({ theme }) => ({
  "& div.MuiDialog-paper": {
    maxWidth: "1024px",
    width: "1024px",
    // height: "349px",
    padding: "40px",
    borderRadius: "24px",
  },
}));
const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  fontSize: "24px",
  fontWeight: 700,
  marginBottom: "32px",
  padding: "0",
}));
const RemarkTextField = styled(TextField)(({ theme }) => ({
  width: "100%",
  "& .MuiInputBase-root": {
    height: "120px",
    borderRadius: 24,
    border: `1px solid ${theme.palette.incutix.grey[400]} !important`,
    paddingLeft: 20,
    alignItems: "start",
  },
  "& input.MuiInputBase-input": {
    padding: "16px 4px",
    fontSize: "14px",
    fontWeight: 700,
  },
  "& fieldset": {
    border: 0,
  },
}));
const FloatingText = styled(Box)(({ theme }) => ({
  position: "absolute",
  bottom: "10px",
  left: "20px",
}));
const CancelButton = styled(Button)(({ theme }) => ({
  borderRadius: 999,
  border: `2px solid ${theme.palette.incutix.grey[300]}`,
  color: theme.palette.incutix.grey[800],
  padding: "10px 18px",
  fontSize: 14,
  fontWeight: 700,
  "&:hover": {
    border: `2px solid ${theme.palette.incutix.grey[400]}`,
  },
}));
const ConfirmButton = styled(Button)(({ theme }) => ({
  borderRadius: 999,
  backgroundColor: theme.palette.incutix.primary[200],
  color: theme.palette.incutix.white,
  padding: "10px 18px",
  fontSize: 14,
  fontWeight: 700,
  "&:hover": {
    backgroundColor: theme.palette.incutix.primary[100],
  },
  "& span.MuiCircularProgress-root": {
    color: theme.palette.incutix.white,
  },
}));

const SalesOrderRemarkEdit = ({ open, setOpen, saleOrderDetail }: SalesOrderRemarkEditProps) => {
  const t = useTranslations("sales_orders.detail.remark.dialog");

  const [wordCount, setWordCount] = useState<number>(0);
  const [remark, setRemark] = useState<string>(saleOrderDetail?.orderAdminNote ?? "");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    setWordCount(countWords(remark));
  }, [remark]);

  const handleClose = () => {
    setOpen(false);
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (typeof e.target.value === "string") {
      setRemark(e.target.value);
    }
  };

  const handleConfirmClick = () => {
    setIsLoading(true);
    updateOrderRemark({ orderId: saleOrderDetail?.orderId, remark }).finally(() => {
      revalidateRetrieveOrderById();
      setIsLoading(false);
      setOpen(false);
    });
  };

  return (
    <StyledDialog open={open} onClose={handleClose}>
      <StyledDialogTitle>{t("title")}</StyledDialogTitle>
      <Box position={"relative"} padding={0}>
        <RemarkTextField multiline rows={3} onChange={handleTextChange} value={remark} />
        <FloatingText>{t("word_count", { count: wordCount })}</FloatingText>
      </Box>
      <DialogActions sx={{ paddingTop: "24px" }}>
        <CancelButton onClick={handleClose}>{t("cancel")}</CancelButton>
        <ConfirmButton onClick={handleConfirmClick}>
          {isLoading ? <CircularProgress size={24} /> : t("confirm")}
        </ConfirmButton>
      </DialogActions>
    </StyledDialog>
  );
};

export default SalesOrderRemarkEdit;
