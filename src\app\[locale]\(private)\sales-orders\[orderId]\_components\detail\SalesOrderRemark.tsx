"use client";
import { Box, Button, Paper, styled } from "@mui/material";
import { useTranslations } from "next-intl";
import { useState } from "react";
import SalesOrderRemarkEdit from "./SalesOrderRemarkEdit";
import { SaleOrderDetailType } from "../../../_schema/saleOrder.schema";

type SalesOrderRemarkProps = {
  saleOrderDetail?: SaleOrderDetailType;
};

const StyledBox = styled(Paper)(({ theme }) => ({
  width: 320,
  borderRadius: 16,
  border: `1px solid ${theme.palette.incutix.grey[400]}`,
  padding: "20px",
  backgroundColor: theme.palette.incutix.white,
}));
const DetailTitle = styled(Box)(({ theme }) => ({
  fontSize: 18,
  fontWeight: 700,
  color: "#000000",
  justifyContent: "left",
  alignItems: "center",
  display: "flex",
}));
const DetailItemValue = styled(Box)(({ theme }) => ({
  minHeight: 20,
  display: "flex",
  width: "100%",
  justifyContent: "right",
  alignItems: "center",
  fontSize: 14,
  fontWeight: 400,
  color: "#000000",
  textAlign: "right",
}));
const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: 999,
  border: `2px solid ${theme.palette.incutix.grey[300]}`,
  color: theme.palette.incutix.grey[800],
  padding: "8px 18px",
  fontSize: 14,
  fontWeight: 700,
  "&:hover": {
    border: `2px solid ${theme.palette.incutix.grey[400]}`,
  },
}));
const RemarkBox = styled(Box)(({ theme }) => ({
  marginTop: "12px",
  fontSize: "14px",
  fontWeight: 400,
  color: "#000000",
}));

const SalesOrderRemark = ({ saleOrderDetail }: SalesOrderRemarkProps) => {
  const t = useTranslations("sales_orders.detail.remark");
  const [open, setOpen] = useState<boolean>(false);

  const data = {
    remark: saleOrderDetail?.orderAdminNote,
  };

  const handleOpenDialog = () => {
    setOpen(true);
  };

  return (
    <StyledBox elevation={0}>
      <Box display={"flex"} flexDirection={"row"}>
        <DetailTitle>{t("title")}</DetailTitle>
        <DetailItemValue>
          <StyledButton variant="outlined" onClick={handleOpenDialog}>
            {t("edit")}
          </StyledButton>
        </DetailItemValue>
      </Box>
      {data.remark && <RemarkBox>{data.remark}</RemarkBox>}
      <SalesOrderRemarkEdit open={open} setOpen={setOpen} saleOrderDetail={saleOrderDetail} />
    </StyledBox>
  );
};

export default SalesOrderRemark;
