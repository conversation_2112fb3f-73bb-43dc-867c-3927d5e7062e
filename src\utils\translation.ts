import { LANGUAGE_CODE } from "./constants";

export enum EventTranslation {
    CONTENT='content',
    QUESTION='question',
    ANSWER='answer',
    NAME='name',
    VENUE='venue',
    VALUE='value'
}

export enum TicketTranslation {
    NAME='name',
    VALUE='value',
    DESCRIPTIONG='description'
}

export const extractTranslationBody = (lang: LANGUAGE_CODE, target: EventTranslation|TicketTranslation, content: {
    translation: {
        id?: number,
        content: string,
        fields: EventTranslation|TicketTranslation,
        language_code: LANGUAGE_CODE
    }
}[]) => {
    const found = (content || []).map(({ translation }) => translation)
        .find(({ language_code, fields }) => language_code === lang && fields === target)
    if (!found) return ""
    return found.content
}

export const extractMultiTranslationBody = (lang: LANGUAGE_CODE, target: EventTranslation|TicketTranslation, content: {
    translation: {
        id?: number,
        content: string,
        fields: EventTranslation|TicketTranslation,
        language_code: LANGUAGE_CODE
    }
}[]) => {
    const found = (content || []).map(({ translation }) => translation)
        .filter(({ language_code, fields }) => language_code === lang)
    if (!found) return []
    return found
}