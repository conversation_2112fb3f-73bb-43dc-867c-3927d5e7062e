"use client";
import CancelButton from "@/components/buttons/CancelButton";
import EditButton from "@/components/buttons/EditButton";
import SaveButton from "@/components/buttons/SaveButton";
import TextField from "@/components/input/TextField";
import ModalContainer from "@/components/ModalContainer";
import { ITag } from "@/interface/ITag";
import { TagSchema } from "@/schemas/TagSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Modal, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import * as React from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";

type FormValue = z.infer<typeof TagSchema>;

interface Props {
  tag: ITag;
}
const EditTag = (props: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors, isDirty },
    setError,
    clearErrors,
    reset,
  } = useForm<FormValue>({
    defaultValues: { name: props.tag.name },
    resolver: zodResolver(TagSchema),
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (isSubmitting) return;
    setOpen(false);
    clearErrors();
    if (isDirty) {
      reset();
    }
  }, [clearErrors, isDirty, isSubmitting, reset]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      await xior.put(`/api/tags/${props.tag.id}`, data);
      queryClient.invalidateQueries({ queryKey: ["tags"] });
      setOpen(false);
      reset({ name: data.name });
    } catch (e) {
      setError("name", { type: "custom", message: "duplicated_tags" });
    }
  };

  return (
    <Box>
      <EditButton sx={{ marginRight: 1.5 }} onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("tags.title_edit_tag")}
            </Typography>
          </Box>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                value={field.value}
                onChange={field.onChange}
                required
                disabled={isSubmitting}
                label={t("tags.label_tag_name")}
                error={errors?.name?.message}
              />
            )}
          />

          <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
            <CancelButton disabled={isSubmitting} onAction={handleClose} />
            <SaveButton disabled={isSubmitting} />
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default EditTag;
