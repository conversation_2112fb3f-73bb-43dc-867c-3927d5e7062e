import { z } from "zod";
import { SCHEMA_ERRORS } from "@/utils/constants";
import { ImageFileSchema } from "./ImageFileSchema";
import { AudioFileSchema } from "./AudioFileSchema";
import { ImageVideoFileSchema } from "./ImageVideoFileSchema";

export const RoomSchema = z.object({
  duration:z.string().nullable(),
  timezone:z.string().nullable(),
  weight:z.string().nullable(),
  compareAt:z.string().nullable(),
  tax:z.string().nullable(),
  audio: z.string().nullable(),
  isDigital: z.number().nullable(),
  status: z.number().nullable(),
  productType: z.number().nullable(),
  currency: z.string().nullable(),
  weightUnit: z.string().nullable(),
  price: z.string().nullable(),
  bidUnit: z.string().nullable(),
  type: z.number().nullable(),
  startDate: z.number().nullable(),
  endDate: z.number().nullable(),
  taxIncluded: z.number().nullable(),
  inventory: z.number().nullable(),
  roomExcluded: z.object({}).array().nullable(),
  category: z.string().nullable(),
  collections: z.object({}).array().nullable(),
  name: z.string().nullable(),
  owner: z.number().nullable(),
  productList: z.object({}).array().nullable(),
  thumbnail: z.string().nullable(),
  roomName: z
    .string({ required_error: SCHEMA_ERRORS.required })
    .min(1, SCHEMA_ERRORS.required),
  roomUrl: z
    .string({ required_error: SCHEMA_ERRORS.required })
    .min(1, SCHEMA_ERRORS.required),
  description: z.string().nullish().optional(),
  tags: z
    .object({
      id: z.number(),
      name: z.string(),
    })
    .array()
    .nullish(),
  visibleToPublic: z.boolean(),
  memberGroups: z
    .object({
      id: z.number(),
      name: z.string(),
    })
    .array(),
  openToAllMembers: z.boolean(),
  // logoUrl: z.union([ImageFileSchema, z.string().nullish().optional()]),
  logoUrl: z.instanceof(File),
  // thumbnailUrl: z.union([
  //   ImageVideoFileSchema,
  //   z.string().nullish().optional(),
  // ]),
  thumbnailUrl: z.instanceof(File),
  // bgMusicUrl: z.union([AudioFileSchema, z.string().nullish().optional()]),
  bgMusicUrl: z.instanceof(File),
  roomSocialUrls: z
    .object({
      instagram: z.string().nullish().optional(),
      facebook: z.string().nullish().optional(),
      youtube: z.string().nullish().optional(),
      x: z.string().nullish().optional(),
      linkedin: z.string().nullish().optional(),
      tiktok: z.string().nullish().optional(),
      pinterest: z.string().nullish().optional(),
      other: z.string().nullish().optional(),
      method: z.string(),
      value: z.string(),
    })
    .array()
    .nullish()
    .optional(),
  roomContacts: z
    .object({
      whatsapp: z.string().nullish().optional(),
      telegram: z.string().nullish().optional(),
      other: z.string().nullish().optional(),
      method: z.string(),
      value: z.string(),
    })
    .array()
    .nullish()
    .optional(),
  contacts: z.string(),
  socialMedias: z.string(),
});
