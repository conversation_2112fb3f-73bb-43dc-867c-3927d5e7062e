"use client"
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import { useState } from 'react';
import { Typography } from '@mui/material';
import {EventList} from '@/interface/IEventList';
import {Event} from '@/interface/IEventList';
import Image from "next/image";
import AllTab from './AllTab';
import RegionTab from './RegionTab';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const tabStyled = {
    '&.Mui-selected': {
        color: 'rgba(79, 183, 71, 1)', 
        borderColor: 'rgba(79, 183, 71, 1)',
    },
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

type Props = {
  eventList: EventList
}

const ContainerTab = ( {eventList}:Props) =>{

    const [value, setValue] = useState(0);


    const eventsByRegion = eventList.events.reduce((acc, event) => {
      if (!acc[event.region]) {
        acc[event.region] = [];
      }
      acc[event.region].push(event);
      return acc;
    }, {} as Record<string, Event[]>);

    console.log("all events", eventList.events)
    console.log("eventsByRegion",eventsByRegion)

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
      setValue(newValue);
    };
  
    const regionMap:any = {
      hk: '香港',
      bk: '曼谷',
      mo: '澳門',
      my: '馬來西亞',
      sel: '首爾',
      sg: '新加坡'
    };

    const filteredRegions = eventList.uniqueRegions.filter((region) => eventsByRegion[region]?.length > 0);
    return(
        <>
      <Box sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={value}
            onChange={handleChange}
            aria-label="basic tabs example"
            TabIndicatorProps={{
              style: { backgroundColor: 'rgba(79, 183, 71, 1)' },
            }}
          >
            {/* 默认的 "所有" Tab */}
            <Tab label="所有" {...a11yProps(0)} sx={tabStyled} />
            {/* 动态生成其他 Tab */}
            {/* {eventList.uniqueRegions.map((region, index) => (
              <Tab
                key={region}
                label={regionMap[region] || region}
                {...a11yProps(index + 1)} 
                sx={tabStyled}
              />
            ))} */}
              {/* {eventList.uniqueRegions
                .filter((region) => eventsByRegion[region]?.length > 0) // 过滤掉没有事件的 region
                .map((region, index) => (
                  <Tab
                    key={region}
                    label={regionMap[region] || region}
                    {...a11yProps(index + 1)}
                    sx={tabStyled}
                  />
                ))} */}
              {filteredRegions.map((region, index) => (
              <Tab
                key={region}
                label={regionMap[region] || region}
                {...a11yProps(index + 1)}
                sx={tabStyled}
              />
            ))}
          </Tabs>
        </Box>
        {/* 默认的 "所有" TabPanel */}
        <CustomTabPanel value={value} index={0}>
          {/* 显示所有事件 */}
          {/* {eventList.events.map((event) => (
            <Box key={event.id} sx={{ mb: 2 }}>
            <Image
                    src={event.thumbnail}
                    alt="Facebook Icon"
                    width={40}
                    height={40}
                    layout="fixed"
                />
              <Typography variant="h6">{event.eventName}</Typography>
              <Typography>{event.description}</Typography>
              <Typography>{event.region}</Typography>
            </Box>
          ))} */}
          <AllTab eventList={eventList}/>
        </CustomTabPanel>
        {/* 动态生成其他 TabPanel */}
        {filteredRegions.map((region, index) => (
          <CustomTabPanel key={region} value={value} index={index + 1}>
            {/* 显示对应 region 的事件 */}
            {eventsByRegion[region]?.length > 0 ? (
          //   eventsByRegion[region].map((event) => (
          //     <Box key={event.id} sx={{ mb: 2 }}>
          //       <Image
          //         src={event.thumbnail}
          //         alt="Facebook Icon"
          //         width={40}
          //         height={40}
          //         layout="fixed"
          //       />
          //       <Typography variant="h6">{event.eventName}</Typography>
          //       <Typography>{event.description}</Typography>
          //       <Typography>{event.region}</Typography>
          //     </Box>
          //     <RegionTab event={event}/>
          //   )
          // )
          <RegionTab events={eventsByRegion[region]} />
          ) : (
            <Typography>没有找到相关活动。</Typography>
          )}
          </CustomTabPanel>
        ))}
      </Box>
        </>
    )
}

export default ContainerTab;