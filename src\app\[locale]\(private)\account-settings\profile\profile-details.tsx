
import {
    Avatar,
    Button,
    Checkbox,
    IconButton,
    Modal,
    TextField,
    Typography,
    Box
  } from "@mui/material";
import { useTranslations } from "next-intl";
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import CloseIcon from '@mui/icons-material/Close';
import CustomButton from "@/components/buttons/CustomButton"
import LockIcon from '@mui/icons-material/Lock';
import EditIcon from '@mui/icons-material/Edit';
import CustomTextField from "@/components/input/CustomTextField"
import { IProfile } from "@/interface/IProfile";
import { useState } from "react";
import FileUploadIcon from '@mui/icons-material/FileUpload';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import useFileUpload from "@/hooks/useFileUpload";
import xior from "xior";
import PhoneInput from "@/components/input/PhoneInput";
import {showErrorPopUp,showSuccessPopUp} from "@/utils/toast";
import { DatePicker } from "@mui/x-date-pickers";
import dayjs, { Dayjs } from "dayjs";
import { format } from "date-fns";


  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    // border: '2px solid #000',
    boxShadow: 24,
    p: 4,
  };
  
  
  const textFieldStyle = {
      '& .MuiOutlinedInput-root': {
        '&:hover fieldset': {
            borderColor: 'rgba(79, 183, 71, 1)', 
        },
        '&.Mui-focused fieldset': {
            borderColor: 'rgba(79, 183, 71, 1)', 
        },
    },
    '& .MuiInputLabel-root.Mui-focused': {
        color: 'rgba(79, 183, 71, 1)', 
    },
  }
  
  const selectStyle = {
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: 'rgba(79, 183, 71, 1)',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: 'rgba(79, 183, 71, 1)',
    },
  }
  
  const menuItemStyle = {
    '&:hover': {
      backgroundColor: 'rgba(79, 183, 71, 1)', // MenuItem hover 時背景顏色
    },
    '&.Mui-selected': {
      backgroundColor: 'rgba(79, 183, 71, 1)', // 選中時背景顏色
      '&:hover': {
        backgroundColor: 'rgba(79, 183, 71, 0.8)', // 選中且 hover 時的背景顏色
      },
    },
  }

type Props = {
    profileDto:IProfile
    handleOpen:() => void
    fetchProfileDataApi:() => void
}  

const defaultValues = {
  gender: "",
  identity: "",
  dateOfBirth: null,
  countryCode: "hk",
  phoneNumber: "",
  photoUrl: "",
  name: "",
  addressLine1: "",
  addressLine2: "",
  addressLine3: "",
  contacts: {
    whatsapp: "",
    telegram: "",
    other: "",
  },
  socialMedias: {
    instagram: "",
    facebook: "",
    youtube: "",
    x: "",
    linkedin: "",
    tiktok: "",
    pinterest: "",
    other: "",
  },
};

const ProfileDetails = ({
                        profileDto,
                        handleOpen,
                        fetchProfileDataApi
                        }:Props) =>{

    const t = useTranslations("profile");
    const spt = useTranslations("setup_profile");
    const { uploadFile } = useFileUpload();
    const { showToast: showSuccess } = showSuccessPopUp();
    const { showToast: showError } = showErrorPopUp();
    const [updateProfileDto,setUpdateProfileDto] = useState<IProfile>({
        gender: "",
        identity: profileDto.identity,
        dateOfBirth: profileDto.dateOfBirth,
        name:profileDto.name,
        email:profileDto.email,
        phoneNumber:profileDto.phoneNumber,
        countryCode:profileDto.countryCode,
        addressLine1:profileDto.addressLine1,
        addressLine2: "",
        addressLine3: "",
        receivePromotions:profileDto.receivePromotions,
        photoUrl:profileDto.photoUrl,
        contacts: {
          whatsapp: "",
          telegram: "",
          other: "",
        },
        socialMedias: {
          instagram: "",
          facebook: "",
          youtube: "",
          x: "",
          linkedin: "",
          tiktok: "",
          pinterest: "",
          other: "",
        },
    })    
    const [photoUrl, setPhotoUrl] = useState<File | string | undefined>(undefined);   
    const [selectedDate, setSelectedDate] = useState<string>("");

    const handleDateChange = (date: Date | null) => {
      if (date) {
        // setSelectedDate(date.toLocaleDateString()); // 將 Date 轉換為 string
        handleUpdateProfileDto({
          ...updateProfileDto,
          dateOfBirth: date.toLocaleDateString()
        });
      } else {
        setSelectedDate(""); // 清空選擇
      }
    };
    
    console.log("updateProfileDto",updateProfileDto)
    console.log("selectedDate",selectedDate)
    
    const handleUpdateProfileDto = (updateProfileDto:IProfile) =>{
        setUpdateProfileDto(updateProfileDto);
    }

    const handleUpdateProfileDetails = async () =>{
      try{

        const response = await xior.put("/api/profile/",updateProfileDto)
        console.log("update profile successfully",response.data)
        showSuccess("Update profile successfully");
        // await fetchProfileDataApi
        window.location.reload();
      }catch(err){
        console.log("update profile error",err)
      }
    }
    

    const handleProfileDtoUpdateForm = (event:any) =>{

        const { name, value, checked, type } = event.target;

        const updatedValue = type === 'checkbox' ? checked : value;

        handleUpdateProfileDto({
            ...updateProfileDto,
            [name]:updatedValue
        });
    }

    const handlePhoneChange = (event: React.ChangeEvent<HTMLInputElement> ) => {
      const value = event.target.value;
      handleUpdateProfileDto({
        ...updateProfileDto,
        phoneNumber: value
      });
    };
  

    const handlePhotoUrlChange = async (event: React.ChangeEvent<HTMLInputElement>) =>{
        const selectedFile = event.target.files?.[0];
        if(selectedFile){
            const reader = new FileReader();
            reader.onload = () =>{
                setPhotoUrl(reader.result as string);
            }
            reader.readAsDataURL(selectedFile);
            try{

                const selectedPhotoUrl = await uploadFile(selectedFile, "profilehumbnail");
                console.log("Uploaded thumbnail URL:", selectedPhotoUrl);

                handleUpdateProfileDto({
                    ...updateProfileDto,
                    photoUrl:selectedPhotoUrl
                });

            }catch(error){
                console.error("Upload failed:", error);
            }
        }
    }

    const removeProfile = () =>{
        setPhotoUrl("");
        handleUpdateProfileDto({
            ...updateProfileDto,
            photoUrl:""
        });
    }

    const renderAvatar = () =>{
        if(updateProfileDto.photoUrl){
            return(
            <>
            <Avatar
            alt={updateProfileDto.name}
            src={updateProfileDto.photoUrl}
            sx={{ width: 100, height: 100,  border: 1,borderColor:"rgba(177, 181, 195, 1)" }}
            />
            </>
            )
        }else {
            return(
            <>
            <Avatar
            alt={updateProfileDto.name}
            src={"/images/DefaultProfilePic.png"}
            sx={{ width: 100, height: 100,  border: 1,borderColor:"rgba(177, 181, 195, 1)" }}
            />
            </>
            )
        }
    }

    const renderDeleteAvatarBtn = () =>{
      if(updateProfileDto.photoUrl){
        return(
          <>
            <IconButton sx={{
                    color:"red"
                }}
                onClick={removeProfile}
                >
                <DeleteOutlineIcon/>
            </IconButton>
          </>
        )
      }else{
        return(
          <>
          </>
        )
      }
    }

    return(
        <>
        <Typography variant="h2">
        {t("account_information")}
        </Typography>
        <br></br>
          <Box sx={{
            display:'flex'
          }}>
            <Box sx={{
              display:'flex',
              flexDirection:"column",
              minWidth: 120,
              gap: 2

            }}>
              <TextField
              id="outlined-multiline-static"
              label={t('label_name')}
              name="name"
              value={updateProfileDto.name}
              InputLabelProps={{
                shrink: true, 
              }}
              sx={textFieldStyle}
              onChange={handleProfileDtoUpdateForm}
              />
              <TextField
              id="outlined-multiline-static"
              label={t('label_email')}
              name="email"
              disabled={true}
              value={updateProfileDto.email}
              InputLabelProps={{
                shrink: true, 
              }}
              sx={textFieldStyle}
              onChange={handleProfileDtoUpdateForm}
              />
              {/* <TextField
              id="outlined-multiline-static"
              label={t('label_phone_number')}
              name="phoneNumber"
              value={updateProfileDto.phoneNumber}
              InputLabelProps={{
                shrink: true, 
              }}
              sx={textFieldStyle}
              onChange={handleProfileDtoUpdateForm}
              /> */}
            <PhoneInput
            // label={t('label_phone_number')}
            value={updateProfileDto.phoneNumber}
            onChange={handlePhoneChange}
            fullWidth
            error={!updateProfileDto.phoneNumber ? "Phone number is required" : undefined}
            
          />
            <Box sx={{ minWidth: 120 }}>
            <FormControl fullWidth>
              <InputLabel id="demo-simple-select-label"
                sx={{
                  '&.Mui-focused': {
                    color: 'rgba(79, 183, 71, 1)', // 當選中時顏色
                  },
                }}
              >{t("label_country")}</InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                name="countryCode"
                value={updateProfileDto.countryCode}
                label={t("label_country")}
                sx={selectStyle}
                onChange={handleProfileDtoUpdateForm}
              >
                <MenuItem value={"hk"} sx={menuItemStyle}>Hong Kong</MenuItem>
                <MenuItem value={"tw"} sx={menuItemStyle}>Taiwan</MenuItem>
                <MenuItem value={"sg"} sx={menuItemStyle}>Singapore</MenuItem>
              </Select>
            </FormControl>
            </Box>
            <TextField
              id="outlined-multiline-static"
              label={t('label_address')}
              name="addressLine1"
              value={updateProfileDto.addressLine1}
              InputLabelProps={{
                shrink: true, 
              }}
              sx={textFieldStyle}
              onChange={handleProfileDtoUpdateForm}
              />
              <DatePicker
                  label={t("label_dob")}
                  disableFuture
                  value={updateProfileDto.dateOfBirth ? new Date(updateProfileDto.dateOfBirth) : null}
                  onChange={handleDateChange} 
              />
              {/* <p>選擇的日期: {selectedDate ? selectedDate.toLocaleDateString() : '未選擇'}</p> */}
              <Box sx={{
                display:'flex',
                alignItems:"center"
              }}>
              <Checkbox
                name="receivePromotions"
                checked={updateProfileDto.receivePromotions}
                onChange={handleProfileDtoUpdateForm}
                sx={{
                  '&.Mui-checked': {
                    color: 'rgba(79, 183, 71, 1)',
                  },
                }}
              />
              <Typography >{spt('subscribe_for_updates')}</Typography>
              </Box>
              <Box>
              <CustomButton namespace="change_password" label="label_change_password" icon={<LockIcon />} onClick={handleOpen}/>&nbsp;&nbsp;
              <CustomButton namespace="profile" label="label_update_profile" icon={<EditIcon/>} onClick={handleUpdateProfileDetails}/>
              </Box>
            </Box>
            <Box sx={{
              ml:4,
            }}>
            {/* <Avatar
            alt={updateProfileDto.name}
            src={updateProfileDto.photoUrl}
            sx={{ width: 100, height: 100,  border: 1,borderColor:"rgba(177, 181, 195, 1)" }}
            /> */}
            {
                renderAvatar()
            }
            <input
                accept="image/*"
                style={{ display: 'none' }}
                id="upload-avatar"
                type="file"
                onChange={handlePhotoUrlChange}
            />
            <label htmlFor="upload-avatar">
                {/* <Button component="span">
                    Upload Avatar
                </Button> */}
                <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    cursor: "pointer",
                    borderColor: "black",
                    '&:hover': {
                    opacity: 0.8,
                    },
                }}
                >
                <IconButton sx={{ padding: 0, marginRight: 1, }}> 
                    <FileUploadIcon />
                </IconButton>
                <Typography variant="body1" sx={{mr:1}}>重新上傳</Typography>
                {renderDeleteAvatarBtn()}
                </Box>
            </label>
            </Box>
          </Box>
        </>
    )
}

export default ProfileDetails