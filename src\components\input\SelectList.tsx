"use client";
import * as React from "react";
import { Box, Checkbox, Collapse, InputLabel, MenuItem, Select as MuiSelect, SelectProps, TextField, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import InputContainer, { InputContainerProps } from "./InputContainer";
import Image from "next/image";
import { TypeInfo } from "@/app/[locale]/(private)/event/component/AddTicketType";

interface Props extends InputContainerProps {
    data: { label: string; value: string|number, extra?: any, sub?: any[] }[];
    handleOnChange?: (val: any) => void
    handleSubOnChange?: (val: any) => void
    handleSetDefault?: (
        setter?: React.Dispatch<React.SetStateAction<any>>,
        subSetter?: React.Dispatch<React.SetStateAction<any>>
    ) => void,
    endIcon?: () => React.ReactNode
    customRender?: (
        val: any,
        setter?: React.Dispatch<React.SetStateAction<any>>,
        subSetter?: React.Dispatch<React.SetStateAction<any>>
    ) => React.ReactNode
}

export default function SelectList({
    label,
    description,
    variant = "outlined",
    fullWidth = true,
    error,
    required,
    multiple = false,
    data,
    value,
    sx,
    handleOnChange,
    handleSubOnChange,
    handleSetDefault,
    customRender,
    endIcon,
    defaultValue,
    ...otherProps
}: Props & Omit<SelectProps, "error">) {
    const t = useTranslations("error");
    const [checked, setChecked] = React.useState<number[]>([]);
    const [subChecked, setSubChecked] = React.useState<number[]>([]);
    const [collapsed, setCollapsed] = React.useState<number[]>([]);

    React.useEffect(() => {
        handleSetDefault && handleSetDefault(setChecked, setSubChecked)
    }, [handleSetDefault])

    React.useEffect(() => {
        handleOnChange && handleOnChange(checked)
    }, [checked, handleOnChange])

    React.useEffect(() => {
        handleSubOnChange && handleSubOnChange(subChecked)
    }, [subChecked, handleSubOnChange])

    return (
        <TextField
            value={value}
            select
            defaultValue={defaultValue}
            label={label}
            fullWidth={fullWidth}
            SelectProps={{
                ...(customRender? {
                    renderValue: (val) => customRender(val, setChecked, setSubChecked)
                }: {}),
                ...(multiple ? {
                    multiple,
                }: {}),
                MenuProps: {
                    PaperProps: {
                        style: {
                            maxHeight: 150,
                        },
                    },
                },
            }}
            InputProps={{
                ...(endIcon? { endAdornment: endIcon() }: {})
            }}
            sx={sx || {}}
            variant={variant}
        >
            {data.map(({ label, value, sub, extra }) => (
                <>
                    <MenuItem value={value} key={value} sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', gap: '12px' }}>
                            <Checkbox
                                value={value}
                                checked={checked.includes(Number(value))}
                                onChange={(event) => {
                                    const {
                                        value,
                                        checked: selected
                                    } = event.target

                                    if (selected) {
                                        setChecked([...checked, Number(value)])
                                    } else {
                                        setChecked(checked.filter(id => id !== Number(value)))
                                    }
                                }}
                            />
                            <Image 
                                src={extra}
                                height={40}
                                width={40}
                                style={{
                                    borderRadius: '8px'
                                }}
                                alt={label}
                            />
                            <Typography
                                variant="body2"
                                fontWeight="bold"
                                sx={{ marginTop: 'auto', marginBottom: 'auto' }}
                            >
                                {label}
                            </Typography>
                        </Box>
                        {
                            sub && sub.length > 0 && (
                                <Image
                                    src={
                                        collapsed.includes(Number(value))? 
                                        `/images/icon-arrowUp.svg`
                                        :`/images/icon-arrowDown.svg`
                                    }
                                    width={16}
                                    height={16}
                                    alt='expands'
                                    onClick={() => {
                                        if (collapsed.includes(Number(value))) {
                                            setCollapsed(collapsed.filter(id => id !== Number(value)))
                                        } else {
                                            setCollapsed([...collapsed, Number(value)])
                                        }
                                    }}
                                />
                            )
                        }
                    </MenuItem>
                    {
                        sub && sub.length > 0 && (
                            <Collapse in={collapsed.includes(Number(value))} timeout="auto" unmountOnExit>
                                {
                                    sub.map(({
                                        name,
                                        value,
                                        subName,
                                        url
                                    }) => {
                                        return (
                                            <MenuItem value={value} key={value}>
                                                <Box sx={{ display: 'flex', gap: '12px', marginLeft: '20px' }}>
                                                    <Checkbox
                                                        value={value}
                                                        checked={subChecked.includes(Number(value))}
                                                        onChange={(event) => {
                                                            const {
                                                                value,
                                                                checked: selected
                                                            } = event.target
                                                            console.log("value >> ", value)
                                                            if (selected) {
                                                                setSubChecked([...subChecked, Number(value)])
                                                            } else {
                                                                setSubChecked(subChecked.filter(id => id !== Number(value)))
                                                            }
                                                        }}
                                                    />
                                                    <Image 
                                                        src={url}
                                                        height={40}
                                                        width={40}
                                                        style={{
                                                            borderRadius: '8px'
                                                        }}
                                                        alt={name}
                                                    />
                                                    <Box>
                                                        <Typography
                                                            variant="body2"
                                                            fontWeight="bold"
                                                            sx={{ marginTop: 'auto', marginBottom: 'auto' }}
                                                        >
                                                            {name}
                                                        </Typography>
                                                        <Typography
                                                            variant="body3"
                                                            fontWeight="400"
                                                            sx={{ marginTop: 'auto', marginBottom: 'auto' }}
                                                        >
                                                            {subName}
                                                        </Typography>
                                                    </Box>
                                                </Box>
                                            </MenuItem>
                                        )
                                    })
                                }
                            </Collapse>
                        )
                    }
                    
                </>
            ))}
        </TextField>
    );
}
