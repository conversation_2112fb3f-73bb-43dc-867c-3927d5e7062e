"use client";
import type { NextPage } from "next";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import ExportButton from "@/components/buttons/ExportButton";
import ImportButton from "@/components/buttons/ImportButton";
import AddNewButton from "@/components/buttons/AddNewButton";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import OwnerTable from "./components/OwnerTable";

const Owners: NextPage = () => {
  const t = useTranslations("owner");
  const router = useRouter();

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={t("label_title")}>
        <>
          <ExportButton  />
          <ImportButton  />
          <AddNewButton onClick={() => router.push(ROUTES.CREATE_OWNER)} />
        </>
      </PageHeader>
      <Box flex={1} padding="26px 34px">
        <OwnerTable />
      </Box>
    </Box>
  );
};

export default Owners;
