import { NextRequest, NextResponse } from "next/server";
import { SalesOrderDto } from "@/interface/ISalesOrderDto";
import {
    authorizedGet,
    getAuthHeaders,
    handleApiError,
} from "@/utils/api";

 async function GET(req: NextRequest) {
    try {
        
        const response = await authorizedGet<SalesOrderDto>(
            "/sales-orders",
            await getAuthHeaders(req) 
        );
        
        return Response.json(
            response,
            { status: 200 }
          );
    } catch (error) {
        console.error(error); 
        return handleApiError(error);
    }
}

export { GET};