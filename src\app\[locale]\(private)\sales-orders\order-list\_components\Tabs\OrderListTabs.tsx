"use client";

import { useTranslations } from "next-intl";
import { Box, Tab, Tabs, styled } from "@mui/material";
import { useState } from "react";
import OrderListTable from "./OrderListTable";
import { useSaleOrderListOrderStatus } from "../../../_hooks";
import { orderStatus } from "../../../_enums/order.enum";
import { OrderListType } from "../../../_schema/saleOrder.schema";

type OrderListTabsProps = {
  data: OrderListType[];
  totalRowCount: number;
};
type StyledTabsProps = {
  children?: React.ReactNode;
  value: number;
  onChange: (event: React.SyntheticEvent, newValue: number) => void;
};
type StyledTabProps = {
  label: string;
};

const StyledTabs = styled((props: StyledTabsProps) => (
  <Tabs {...props} TabIndicatorProps={{ children: <span className="MuiTabs-indicatorSpan" /> }} />
))(({ theme }) => ({
  "& span.MuiTabs-indicator": {
    display: "flex",
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  "& span.MuiTabs-indicatorSpan": {
    width: "60%",
    backgroundColor: theme.palette.incutix.primary[100],
  },
}));
const StyledTab = styled((props: StyledTabProps) => <Tab {...props} />)(({ theme }) => ({
  minWidth: 0,
  padding: "0px 20px",
  textTransform: "none",
  fontSize: "16px",
  fontWeight: 700,
  color: theme.palette.incutix.grey[700],
  "&:hover": {
    color: theme.palette.incutix.primary[300],
  },
  "&.Mui-selected": {
    color: theme.palette.incutix.primary[200],
    fontSize: "16px",
    fontWeight: 700,
  },
}));

const orderStatusMapper: Record<number, string> = {
  0: "All",
  1: orderStatus.inProgress,
  2: orderStatus.complete,
};

const OrderListTabs = (props: OrderListTabsProps) => {
  const t = useTranslations("sales_orders");
  const [orderStatus, setOrderStatus] = useSaleOrderListOrderStatus();

  const [tabIndex, setTabIndex] = useState<number>(0);

  const handleTabChange = (event: React.SyntheticEvent, value: number) => {
    setTabIndex(value);
    setOrderStatus(orderStatusMapper[value] ?? "");
  };

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <StyledTabs value={tabIndex} onChange={handleTabChange}>
          <StyledTab label={t("tab.all")} />
          <StyledTab label={t("tab.in_progress")} />
          <StyledTab label={t("tab.completed")} />
        </StyledTabs>
      </Box>
      <OrderListTable {...props} />
    </Box>
  );
};

export default OrderListTabs;
