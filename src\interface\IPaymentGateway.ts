 export interface IPaymentCreateDto {
    product: IProduct[];
    paymentType?: 'QRCODE' | 'MOBILEWEB' | 'WEB' | 'REDIRECT';
    paymentPlatform: 'WeChatPay' | 'AliPay' | 'Airwallex';
    feeType: 'USD' | 'HKD' | 'EUR' | 'GBP' | 'JPY' | 'KRW';
    totalFee: number;
    description?: string | null; 
    clientName: string;
    clientEmail: string;
    clientContactNumber: string;
    billingAddressCountry: string;
    billingAddress: string[]; 
    billingAddressPostalCode: string;
    deliveryAddressCountry: string | null;
    deliveryAddress: string[] | null; 
    deliveryAddressPostalCode: string | null;
    promoCode?: string | null; 
}

export interface IProduct {
    id: number;
    quantity: number;
  }