"use client"
import { useTranslations } from 'next-intl';
import { Box, Card, Checkbox, Icon, IconButton, Menu, MenuItem, Skeleton, styled, Tab, Tabs, Typography } from '@mui/material';
import React, { useState, useEffect, useMemo } from 'react';
import { keepPreviousData, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import xior from 'xior';
import EventTab, { EVENT_STATUS } from '../component/eventTab';
import TicketTab from '../component/ticketTab';
import { COLORS } from '@/styles/colors';
import { generateDisplayableDate } from '@/utils/date';
import Image from 'next/image';
import InventoryTab from '../component/InventoryTab';
import Select from '@/components/input/Select';
import { LANGUAGE_CODE, LANGUAGE_OPTIONS } from '@/utils/constants';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const Container = styled(Box)(({}) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '32px'
}))

const a11yProps = (index: number) => {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

const CustomTabPanel = (props: TabPanelProps) => {
    const { children, value, index, ...other } = props;

    return (
        <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
        >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );
}

interface Props {
  params: { id: string };
}

const Event = ({ params: { id } }: Props) =>{
    const t = useTranslations('event');
    const tTicket = useTranslations('ticket');
    const tCommon = useTranslations('common');
    const [value, setValue] = useState(0);
    const [isEdit, setIsEdit] = useState(false);
    const queryClient = useQueryClient();

    const [anchorEl, setAnchorEl] = useState(null);
    const [language, setLanguage] = useState(LANGUAGE_CODE.EN);

    const TabContent = [{
        label: t('event_detail'),
        value: 'EVENT'
    }, {
        label: tTicket('tickets'),
        value: 'TICKET'
    }, {
        label: tTicket('inventory'),
        value: 'INVENTORY'
    }]

    const {
        data: eventInfo,
        refetch,
        isLoading
    } = useQuery({
        queryKey: ["event"],
        queryFn: async () => {
            return xior
            .get(`/api/event/${id}`)
        },
        placeholderData: keepPreviousData,
        retry: 3,
    });

    const eventStatusMutation = useMutation({
        mutationFn: () => {
            const body = status === EVENT_STATUS.ACTIVE? { status: EVENT_STATUS.INACTIVE }:
                                { status: EVENT_STATUS.ACTIVE }
            return xior.put(`/api/event/status/${id}`, body)
        },
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["event"]});
        }
    });

    const {
        name,
        status,
        updated_at: updatedAt
    } = (eventInfo?.data?.data?.event ?? {})

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const renderTab = () => (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '32px'
            }}
        >
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={value} onChange={handleChange}>
                    {
                        TabContent.map(({ label, value }, index) => (
                            <Tab key={label} label={label} {...a11yProps(index)} />
                        ))
                    }
                </Tabs>
                <CustomTabPanel value={value} index={0}>
                    <EventTab language={language} eventInfo={eventInfo} isLoading={isLoading} isEdit={isEdit} />
                </CustomTabPanel>
                <CustomTabPanel value={value} index={1}>
                    <TicketTab language={language} id={Number(id)} eventInfo={eventInfo} isLoading={isLoading} isEdit={isEdit} />
                </CustomTabPanel>
                <CustomTabPanel value={value} index={2}>
                    <InventoryTab language={language} id={Number(id)} eventInfo={eventInfo} isLoading={isLoading} isEdit={isEdit} />
                </CustomTabPanel>
            </Box>
        </Box>
    )

    const handleClick = (event: any) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleEdit = () => {
        setIsEdit(true)
        setAnchorEl(null)
    }

    const changeEventStatus = () => {
        eventStatusMutation.mutate()
    }

    return(
        <Container>
            <Box display="flex" flexDirection={"row"} justifyContent={"space-between"}>
                <Box>
                    <Box display="flex" flexDirection={"row"} gap={"12px"}>
                        <Typography variant='h3' fontWeight={'bold'}>
                            {name}
                        </Typography>
                        <Typography
                            variant='body2'
                            fontWeight={'bold'}
                            color={status === EVENT_STATUS.ACTIVE? COLORS.PRIMARY_1 : COLORS.GREY_6}
                            bgcolor={status === EVENT_STATUS.ACTIVE? COLORS.PRIMARY_3 : COLORS.GREY_3}
                            borderRadius={'999px'}
                            padding="3px 10px 5px 10px"
                        >
                            {t(`${status}`)}
                        </Typography>
                    </Box>
                    <Typography variant='body2' color={COLORS.GREY_6} fontWeight={'400'}> 
                        {tCommon("updated")}: {generateDisplayableDate(updatedAt, 'YYYY/MM/DD, HH:mm')}
                    </Typography>
                </Box>

                <Box display="flex" flexDirection={"row"} gap={"12px"} alignItems={"center"}>
                    <Select 
                        data={LANGUAGE_OPTIONS.map((item) => ({ ...item, label: `${tCommon("language")}: ${item.label}`}))}
                        handleOnChange={setLanguage}
                        value={language}
                    />

                    <Box
                        sx={{
                            display: 'flex',
                            width: '36px',
                            height: '36px',
                            borderRadius: "999px",
                            alignItems: 'center',
                            justifyContent: 'center',
                            border: `1px solid ${COLORS.GREY_4}`
                        }}
                        onClick={handleClick}
                    >
                        <Image
                            src="/images/icon-option.svg"
                            width={24}
                            height={24}
                            alt={"option"}
                        />
                    </Box>

                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            padding: '16px 26px 16px 26px',
                            borderRadius: '999px',
                            gap: '8px',
                            border: `1px solid ${COLORS.GREY_3}`
                        }}
                    >
                        <Image
                            src="/images/icon-hidden.svg"
                            width={24}
                            height={24}
                            alt="show"
                        />
                        <Typography variant='body1' fontWeight={"bold"} color={COLORS.GREY_8}>
                            {tCommon("preview")}
                        </Typography>
                    </Box>

                    <Box
                        sx={{
                            padding: '16px 26px 16px 26px',
                            borderRadius: '999px',
                            backgroundColor: COLORS.PRIMARY_1
                        }}
                        onClick={changeEventStatus}
                    >
                        <Typography variant='body1' fontWeight={"bold"} color={COLORS.WHITE}>
                            {status === EVENT_STATUS.ACTIVE? t("inactive") : t("publish_event")}
                        </Typography>
                    </Box>
                </Box>
            </Box>

            <Menu
                id="event_option_menu"
                anchorEl={anchorEl}
                keepMounted
                open={Boolean(anchorEl)}
                onClose={handleClose}
            >
                <MenuItem key={'edit_mode'} onClick={handleEdit}>
                    {tCommon("title_edit")}
                </MenuItem>
            </Menu>

            {renderTab()}
        </Container>
    )
}

export default Event;