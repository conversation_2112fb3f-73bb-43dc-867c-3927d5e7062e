"use client";
import type { NextPage } from "next";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import ExportButton from "@/components/buttons/ExportButton";
import ImportButton from "@/components/buttons/ImportButton";
import AddNewButton from "@/components/buttons/AddNewButton";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import MemberListTable from "./components/MemberListTable";

const MemberList: NextPage = () => {
  const t = useTranslations("member_management_list");
  const router = useRouter();

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={t("label_title")}>
        <>
          <ExportButton  />
        </>
      </PageHeader>
      <Box flex={1} padding="26px 34px">
        <MemberListTable />
      </Box>
    </Box>
  );
};

export default MemberList;
