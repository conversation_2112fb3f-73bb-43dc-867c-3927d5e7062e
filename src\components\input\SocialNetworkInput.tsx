"use client";
import * as React from "react";
import {
  TextField as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TextFieldProps,
  InputAdornment,
  Select,
  MenuItem,
  InputLabel
} from "@mui/material";
import Box from "@mui/material/Box";
import InputContainer, { InputContainerProps } from "./InputContainer";
import { CONTACTS_METHODS, SOCIAL_MEDIAS } from "@/utils/constants";
import DeleteButton from "../buttons/DeleteButton";
import { useTranslations } from "next-intl";

interface Props extends InputContainerProps {
  value?: any;
  onChange?: Function;
  setter?: Function;
  socialNetwork?: any;
  index?: number;
  remove?: Function;
}

export default function SocialNetworkInput({
  socialNetwork,
  label,
  description,
  required,
  value,
  onChange,
  error,
  setter,
  index,
  remove,
  disabled,
  ...otherProps
}: Props & Omit<TextFieldProps, "error">) {
  const t = useTranslations()
  return (
    <InputContainer label={label} error={error}>
      <Box display={"flex"} flexDirection={"row"} gap={1}>
        <Select
          // {...otherProps}
          sx={{ width: 250 }}
          size="small"
          error={!!error}
          displayEmpty
          // onChange={(event) => {
          //   onChange(event.target.value, "method", index, setter)
          // }}
          // onChange={onChange}
          value={value['method']}
        >
          {Object.values(SOCIAL_MEDIAS).map((method) => (
            <MenuItem value={method.method} key={method.label}>
              <Box
                component="img"
                src={method.src}
                sx={{ height: 16, width: 16, marginRight: 3, objectFit: "contain" }}
              />
              {method.label}
            </MenuItem>
          ))}
        </Select>
        <MuiTextField
          sx={{ width: 480 }}
          disabled={disabled}
          value={value['value']}
          // onChange={(event) => {
          // onChange(event.target.value, "value", index, setter)
          // }}
          onChange={onChange}
          placeholder={SOCIAL_MEDIAS[value['method']?.toLowerCase()]?.placeholder ?? t("common.select_placeholder")}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Box
                  component="img"
                  src={"/images/link.png"}
                  sx={{ height: 16, width: 16, objectFit: "contain" }}
                />
              </InputAdornment>
            ),
          }}
        />
        {/* <DeleteButton onClick={() => remove(index, setter)} /> */}
        <DeleteButton onClick={() => {
          if (typeof remove === 'function') {
            remove(index, setter);
          } else {
            console.error("Remove function is not defined");
          }
        }} />
      </Box>
    </InputContainer>
  );
}
