'use client'
import {
    Box,
    Typography,
  } from "@mui/material";
import { useTranslations } from "next-intl";
import ImagesSlider from '@/components/ImagesSlider';
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import Image from "next/image";
import FollowUs from "./follow-us";

const Item = styled(Paper)(({ theme }) => ({
    //backgroundColor: '#fff',
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: 'center',
    color: theme.palette.text.secondary,
    ...theme.applyStyles('dark', {
    //   backgroundColor: '#1A2027',
    }),
  }));

const boxStyled = {
    display:"flex",
    flexDirection:"column",
    alignItems:"start"
}

const AboutIncutix = () =>{
    const t = useTranslations("about_incutix");

    // const images = [
    //     'https://s3.ap-southeast-1.amazonaws.com/portal-dev.fun-verse.io/productThumbnail/tmp/afd7689e05bad8fdc1df34e15520a26239d505ca.jpeg',
    //   ];
    const bannerImage = 'https://s3.ap-southeast-1.amazonaws.com/portal-dev.fun-verse.io/productThumbnail/tmp/afd7689e05bad8fdc1df34e15520a26239d505ca.jpeg';
    return(
        <>
        {/* <Typography>
            {t("about_incutix")}
        </Typography>
        <ImagesSlider
            images={images}
        /> */}
        <Box sx={{
            paddingRight:2,
            paddingLeft:2
        }}>
       <Box
            sx={{
                position: 'relative',
                width: '100%',
                height: '721px', // 根据需要调整高度
                overflow: 'hidden',
                backgroundImage: `url(${bannerImage})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                borderRadius:"32px",
            }}
        >
            <Box
                sx={{
                    position: 'absolute',
                    top: '0',
                    left: '0',
                    right: '0',
                    bottom: '0',
                    ///backgroundColor: 'rgba(0, 0, 0, 0.2)', // 半透明背景
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    textAlign: 'center',
                    color: 'white',
                    padding: '20px',
                }}
            >
            <Grid container spacing={2}>
            <Grid item xs={6}>
            {/* <Item>xs=8</Item> */}
            <Box sx={boxStyled}>
            <Typography variant="body1" gutterBottom sx={{
                color:"rgba(244, 128, 31, 1)"
            }}>
                關於 /
            </Typography>
            <Typography variant="h1" gutterBottom sx={{
                color:"white"
            }}>
                INCUTIX
            </Typography>
            <Typography sx={{textAlign:"start",fontSize:"16px"}}>
                INCUTIX 一個嶄新在線票務平台，專門提供由 Incubase Studio (www.incubasestudio.com) 及其附屬公司組織的最令人興奮的 [地區性娛樂體驗]。 從您最喜歡的角色展覽到身臨其境的漫遊體驗，我們的限時活動將世界各地的刺激尋求者和動漫迷聯繫在一起。 憑藉多種支付選項和一流的客戶支持，INCUTIX 為我們的客戶提供安全、便捷的門票購買和獨家活動商品。
            </Typography>
            <Typography sx={{textAlign:"start",fontSize:"16px",mt:2,mb:1}}>
                由Incubase Studio創辦
            </Typography>
                <Image
                    src={"/images/Incubase_logo.png"}
                    alt="Product Image"
                    width={232}
                    height={47}
                    layout="fixed"
                />
            </Box>    
            </Grid>
                <Grid item xs={6}>
                    {/* Right Box */}
                    <Box
                        sx={{
                       // backgroundColor: 'rgba(0, 0, 0, 0.2)', 
                        mb: 10,
                        display: 'flex',
                        justifyContent: 'flex-end', 
                        }}
                    >
                        <Box
                        sx={{
                            padding: 5,
                            width: 100, 
                            // height: 100, 
                            backgroundColor: 'rgba(0, 0, 0, 0.2)', 
                            display: 'block',
                            alignItems: 'center', 
                            justifyContent: 'center', 
                        }}
                        >
                        <Typography variant="h2">160萬+</Typography>
                        <Typography variant="body1">已出售門票</Typography>
                        </Box>
                    </Box>

                    {/* Left Box */}
                    <Box
                        sx={{
                       // backgroundColor: 'rgba(0, 0, 0, 0.2)', 
                        display: 'flex',
                        justifyContent: 'flex-start', 
                        }}
                    >
                        <Box
                        sx={{
                            padding: 5,
                            width: 100, 
                            // height: 100,
                            backgroundColor: 'rgba(0, 0, 0, 0.3)', 
                            display: 'block',
                            alignItems: 'center', 
                            justifyContent: 'center', 
                        }}
                        >
                        <Typography variant="h2">100+</Typography>
                        <Typography variant="body1">授權活動</Typography>
                        </Box>
                    </Box>
                </Grid>
            </Grid>
            </Box>
        </Box>
        </Box>
        <FollowUs/>
        </>
    )
}

export default AboutIncutix;