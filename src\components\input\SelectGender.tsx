"use client";
import * as React from "react";
import { MenuItem } from "@mui/material";
import Select, { SelectProps } from "@mui/material/Select";
import { useTranslations } from "next-intl";
import InputContainer, { InputContainerProps } from "./InputContainer";

interface Props extends InputContainerProps {}

const GENDERS = [
  "female",
  "male",
  "transgender_female",
  "transgender_male",
  "non_binary",
  "agender",
  "not_listed",
  "not_to_state",
];

export default function SelectGender({
  label,
  description,
  fullWidth = true,
  error,
  required,
  ...otherProps
}: Props & Omit<SelectProps, "error">) {
  const t = useTranslations("profile");
  return (
    <InputContainer
      label={label}
      description={description}
      required={required}
      error={error}
    >
      <Select {...otherProps} size="small" error={!!error}>
        <MenuItem value="">Select gender</MenuItem>
        {GENDERS.map((genderValue) => (
          <MenuItem value={genderValue} key={genderValue}>
            {t(`gender_value_${genderValue}`)}
          </MenuItem>
        ))}
      </Select>
    </InputContainer>
  );
}
