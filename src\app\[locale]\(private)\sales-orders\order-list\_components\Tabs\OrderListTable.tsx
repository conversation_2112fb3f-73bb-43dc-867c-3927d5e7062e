"use client";

import { useEffect, useMemo } from "react";
import { styled } from "@mui/material/styles";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import EnhancedTableHead from "./EnhancedTableHead";
import { Box, Button, Checkbox, Link, Pagination, Stack } from "@mui/material";
import Grid from "@mui/material/Unstable_Grid2";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import { orderShippingStatus, orderStatus, OrderStatusType } from "../../../_enums/order.enum";
import { getCurrencySymbol } from "@/utils/currency";
import CloseIcon from "@/components/icons/CloseIcon";
import {
  useSaleOrderListOrderStatus,
  useSaleOrderListPage,
  useSaleOrderListSearch,
  useSaleOrderListShowPerPage,
  useSaleOrderListSort,
} from "../../../_hooks";
import { OrderListType } from "../../../_schema/saleOrder.schema";

import { useRouter } from "next/navigation";
import OrderStatusTag from "../../../[orderId]/_components/OrderStatusTag";
import DeliveryStatusTag from "../../../[orderId]/_components/DeliveryStatusTag";
import { batchUpdateShippingStatus } from "../../../_actions/batchUpdateShippingStatus";
import { revalidateRetrieveOrderList } from "../../../_actions/retrieveOrderList.action";
import { useSelectedOrder } from "../../_hooks";
import { useRetrieveOrderList } from "../../_hooks/retrieveOrderList.hooks";

interface OrderListTableProps {
  data: OrderListType[];
  totalRowCount: number;
}

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.body}`]: {
    color: "#000000",
    fontSize: 16,
    fontWeight: 400,
    border: 0,
    borderBottom: `1px solid ${theme.palette.incutix.grey[300]}`,
    whiteSpace: "nowrap",
    padding: "0px 16px",
    textAlign: "left",
    height: "24px",
    [`&:first-child`]: {
      padding: 0,
      textAlign: "center",
    },
  },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  height: "53px",
  backgroundColor: theme.palette.incutix.primary[500],
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));
const StyledPagination = styled(Pagination)(({ theme }) => ({
  "& button.MuiPaginationItem-root": {
    backgroundColor: theme.palette.incutix.white,
    border: 0,
    fontSize: 14,
    fontWeight: 700,
    "&.Mui-selected": {
      color: theme.palette.incutix.primary[200],
      backgroundColor: theme.palette.incutix.primary[500],
    },
    borderRadius: "100px",
  },
}));
const StyledCheckbox = styled(Checkbox)(({ theme }) => ({
  "&.Mui-checked": {
    color: theme.palette.incutix.primary[200],
  },
}));

// NOTE: Button
const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: 999,
  padding: "6px 18px",
  fontSize: 14,
  fontWeight: 700,
  border: `2px solid ${theme.palette.incutix.grey[300]}`,
  color: theme.palette.incutix.grey[800],
}));

// NOTE: Link
const StyledLink = styled(Link)(({ theme }) => ({
  color: theme.palette.incutix.grey[800],
  textDecoration: "none",
  cursor: "pointer",
}));

const useTableActionTranslations = (selected: number) => {
  const t = useTranslations("sales_orders.table.action");

  return {
    selected: t("selected", { selected: selected }),
    pickUp: t("pick_up"),
    deliveredToCourier: t("delivered_to_courier"),
  };
};

const GoToOrderDetailLink = ({ orderId, text }: { orderId: number; text: string }) => {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/sales-orders/${orderId}`);
  };

  return (
    <StyledLink underline="hover" onClick={handleClick}>
      {text}
    </StyledLink>
  );
};

const OrderListTable = ({ data }: OrderListTableProps) => {
  const { rows, totalRowCount } = useRetrieveOrderList(data);
  const [searchOrderStatus] = useSaleOrderListOrderStatus();
  const [searchText] = useSaleOrderListSearch();

  const [
    selectedOrders,
    { addTo: addToSelectedOrder, removeAt: removeAtSelectedOrder, clear: clearSelectedOrder },
  ] = useSelectedOrder();
  const tableActionTranslations = useTableActionTranslations(selectedOrders.length ?? 0);
  const [rowsPerPage] = useSaleOrderListShowPerPage();
  const [page, setPage] = useSaleOrderListPage();

  const isSelected = (orderId: number) => selectedOrders.some((x) => x === orderId);
  const isAnySelected = selectedOrders.length > 0;

  const dataCount = useMemo(
    () => Math.ceil(totalRowCount / rowsPerPage),
    [totalRowCount, rowsPerPage]
  );

  // Reset the selected after the filter data
  useEffect(() => {
    clearSelectedOrder();
  }, [searchOrderStatus, searchText]);

  // Table Checkbox
  const handleClick = (event: React.ChangeEvent<HTMLInputElement>, orderId: number) => {
    const selectedIndex = selectedOrders.some((x) => x === orderId);
    if (selectedIndex) {
      removeAtSelectedOrder(orderId);
    } else {
      addToSelectedOrder(orderId);
    }
  };

  // Table Pagination
  const handlePaginationOnChange = (event: React.ChangeEvent<unknown>, page: number) => {
    setPage(page);
  };

  // Table Action - cancel all selected
  const handleCancelSelectedClick = () => {
    clearSelectedOrder();
  };

  const handlePickUpClick = () => {
    const selectedOrderId = [...selectedOrders];

    batchUpdateShippingStatus({
      orderIds: selectedOrderId,
      shippingStatus: orderShippingStatus.delivered,
    }).finally(() => {
      revalidateRetrieveOrderList();
      clearSelectedOrder();
    });
  };

  const handleDeliveredToCourierClick = () => {};

  return (
    <>
      <TableContainer component={Paper} sx={{ margin: "32px 0px 0px 0px", maxWidth: "98%" }}>
        <Table sx={{ marginTop: "0" }}>
          <EnhancedTableHead />
          <TableBody>
            {rows.map((row: OrderListType) => {
              const isItemSelected = isSelected(row.orderId);

              return (
                <StyledTableRow
                  key={row.orderNo}
                  sx={{
                    backgroundColor: (theme) =>
                      isItemSelected
                        ? theme.palette.incutix.primary[500]
                        : theme.palette.incutix.white,
                  }}
                >
                  <StyledTableCell>
                    <StyledCheckbox
                      size="small"
                      checked={isItemSelected}
                      onChange={(event) => handleClick(event, row.orderId)}
                    />
                  </StyledTableCell>
                  <StyledTableCell align="left">
                    <OrderStatusTag status={row.orderStatus} />
                  </StyledTableCell>
                  <StyledTableCell align="left">
                    <GoToOrderDetailLink text={row.orderNo} orderId={row.orderId} />
                  </StyledTableCell>
                  <StyledTableCell align="left">{row.userName}</StyledTableCell>
                  <StyledTableCell align="left">{row.userEmail}</StyledTableCell>
                  <StyledTableCell align="right">{`${getCurrencySymbol(row.currency)} ${
                    row.orderAmount
                  }`}</StyledTableCell>
                  <StyledTableCell align="left">
                    <DeliveryStatusTag
                      status={row.shippingStatus ?? ""}
                      shippingType={row.shippingType ?? ""}
                    />
                  </StyledTableCell>
                  <StyledTableCell align="left">
                    {dayjs.unix(row.create_at).format("YYYY/MM/DD HH:mm:ss")}
                  </StyledTableCell>
                </StyledTableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <Grid container spacing={3} sx={{ padding: "16px 16px 16px 0px" }}>
        <Grid xs>
          <Stack direction="row" spacing={2}>
            {isAnySelected && (
              <StyledButton endIcon={<CloseIcon />} onClick={handleCancelSelectedClick}>
                {tableActionTranslations.selected}
              </StyledButton>
            )}
            <StyledButton onClick={handlePickUpClick}>
              {tableActionTranslations.pickUp}
            </StyledButton>
            <StyledButton onClick={handlePickUpClick}>
              {tableActionTranslations.deliveredToCourier}
            </StyledButton>
          </Stack>
        </Grid>
        <Grid xs={4} alignContent={"end"}>
          <Box display={"flex"} justifyContent={"flex-end"}>
            <StyledPagination
              count={dataCount}
              page={page}
              hideNextButton={true}
              hidePrevButton={true}
              onChange={handlePaginationOnChange}
            />
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default OrderListTable;
