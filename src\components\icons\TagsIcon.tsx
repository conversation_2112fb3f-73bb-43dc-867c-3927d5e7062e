"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const TagsIcon = createSvgIcon(
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.656 9.4135L14.1864 9.94377L14.188 9.94218L13.656 9.4135ZM9.41747 13.6532L8.88707 13.1229L8.88677 13.1232L9.41747 13.6532ZM7.74451 13.6532L8.2752 13.1232L8.2746 13.1226L7.74451 13.6532ZM2.6665 8.57976H1.9165C1.9165 8.77879 1.99561 8.96966 2.13641 9.11033L2.6665 8.57976ZM2.6665 2.66669V1.91669C2.25229 1.91669 1.9165 2.25247 1.9165 2.66669H2.6665ZM8.57803 2.66669L9.10843 2.13643C8.96777 1.99573 8.77698 1.91669 8.57803 1.91669V2.66669ZM13.656 7.74602L14.188 7.21733L14.1864 7.21576L13.656 7.74602ZM13.1256 8.88324L8.88707 13.1229L9.94787 14.1834L14.1864 9.94376L13.1256 8.88324ZM8.88677 13.1232C8.84661 13.1634 8.79893 13.1953 8.74647 13.2171L9.32101 14.6027C9.5556 14.5054 9.76871 14.3628 9.94816 14.1831L8.88677 13.1232ZM8.74647 13.2171C8.694 13.2388 8.63777 13.25 8.58099 13.25V14.75C8.83495 14.75 9.08641 14.7 9.32101 14.6027L8.74647 13.2171ZM8.58099 13.25C8.5242 13.25 8.46797 13.2388 8.4155 13.2171L7.84096 14.6027C8.07556 14.7 8.32703 14.75 8.58099 14.75V13.25ZM8.4155 13.2171C8.36304 13.1953 8.31536 13.1634 8.2752 13.1232L7.21381 14.1831C7.39326 14.3628 7.60637 14.5054 7.84096 14.6027L8.4155 13.2171ZM8.2746 13.1226L3.19659 8.04919L2.13641 9.11033L7.21442 14.1837L8.2746 13.1226ZM3.4165 8.57976V2.66669H1.9165V8.57976H3.4165ZM2.6665 3.41669H8.57803V1.91669H2.6665V3.41669ZM8.04763 3.19695L13.1256 8.27628L14.1864 7.21576L9.10843 2.13643L8.04763 3.19695ZM13.1241 8.2747C13.2046 8.35574 13.2498 8.4654 13.2498 8.57976H14.7498C14.7498 8.06925 14.5479 7.57945 14.188 7.21734L13.1241 8.2747ZM13.2498 8.57976C13.2498 8.69411 13.2046 8.80378 13.1241 8.88482L14.188 9.94218C14.5479 9.58007 14.7498 9.09026 14.7498 8.57976H13.2498ZM5.3165 5.50002C5.3165 5.39877 5.39859 5.31669 5.49984 5.31669V6.81669C6.22701 6.81669 6.8165 6.2272 6.8165 5.50002H5.3165ZM5.49984 5.31669C5.60109 5.31669 5.68317 5.39877 5.68317 5.50002H4.18317C4.18317 6.2272 4.77266 6.81669 5.49984 6.81669V5.31669ZM5.68317 5.50002C5.68317 5.60127 5.60109 5.68335 5.49984 5.68335V4.18335C4.77266 4.18335 4.18317 4.77285 4.18317 5.50002H5.68317ZM5.49984 5.68335C5.39859 5.68335 5.3165 5.60127 5.3165 5.50002H6.8165C6.8165 4.77285 6.22701 4.18335 5.49984 4.18335V5.68335Z" fill="currentColor"/>
</svg>
  ,
  "Tags"
);

export default TagsIcon;