"use client";
import * as React from "react";
import TextField from "./TextField";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { ITag } from "@/interface/ITag";
import { InputContainerProps } from "./InputContainer";

interface TagOption {
  inputValue?: string;
  name: string;
  id?: number;
}

const filter = createFilterOptions<TagOption>();

interface Props extends InputContainerProps {
  placeholder?: string;
  disabled?: boolean;
  onChange: (value: ITag[]) => void;
  value: ITag[] | any;
  defaultValue?: ITag[];
}

export default function SelectTag({ onChange, value, ...otherProps }: Props) {
  const queryClient = useQueryClient();
  const dataQuery = useQuery({
    queryKey: ["tags"],
    queryFn: async () =>
      xior.get<IListResponse<ITag>>("/api/tags?size=9999").then((res) => ({
        items: res.data.items as TagOption[],
        total: res.data.count,
      })),
  });

  const mutation = useMutation({
    mutationFn: (name: string) => {
      return xior.post<Array<ITag>>("/api/tags", [{ name: name.replace("Add ", "").replace(/"/g, "") }])
    },
    onSuccess: (res) => {
      queryClient.invalidateQueries({ queryKey: ["tags"] });
      const tags = res.data;
      console.log("tags >> ", tags)
      console.log("value >> ", value)
      const found = value.map((item: string | number | any) => {
        const added = tags.find((tag) => tag.name === item?.inputValue)

        if (added) return added
        return item
      })
      onChange(found as ITag[]);
    },
  });

  return (
    <React.Fragment>
      <Autocomplete
        multiple
        freeSolo
        disableCloseOnSelect
        selectOnFocus
        clearOnBlur
        handleHomeEndKeys
        options={dataQuery.data?.items || []}
        value={value}
        onChange={(event, newValue) => {
          console.log("newValue >> ", newValue)
          const newTags = newValue.map((item) =>
            typeof item === "string"
              ? dataQuery.data?.items.find((tag) => tag.name === item) || {
                name: item,
              }
              : item
          );

          for (const newTag of newTags) {
            if (!newTag.id) {
              mutation.mutate(newTag.name);
            }
          }

          onChange(newTags as ITag[]);
        }}
        filterOptions={(options, params) => {
          const filtered = filter(options, params);
          if (params.inputValue !== "") {
            filtered.push({
              inputValue: params.inputValue,
              name: `Add "${params.inputValue}"`,
            });
          }

          return filtered;
        }}
        getOptionLabel={(option) =>
          typeof option === "string" ? option : option?.inputValue ? option.inputValue : option?.name
        }
        isOptionEqualToValue={(option, value) => option?.name === value?.name}
        renderOption={(props, option) => <li {...props}>{option.name}</li>}
        renderInput={(params) => <TextField {...params} {...otherProps} />}
      />
    </React.Fragment>
  );
}
