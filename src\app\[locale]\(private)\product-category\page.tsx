import type { NextPage } from "next";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import ExportButton from "@/components/buttons/ExportButton";
import ImportButton from "@/components/buttons/ImportButton";
import ProductCategoryTable from "./components/ProductCategoryTable";
import CreateProductCategory from "./components/CreateProductCategory";
import { useState } from "react";

const ProductCategory: NextPage = () => {
  const t = useTranslations("product_category");

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={t("label_title")}>
        <>
          <ExportButton  />
          <ImportButton  />
          <CreateProductCategory />
        </>
      </PageHeader>
      <Box flex={1} padding="26px 34px">
        <ProductCategoryTable />
      </Box>
    </Box>
  );
};

export default ProductCategory;
