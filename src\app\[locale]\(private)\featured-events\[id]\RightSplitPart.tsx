"use client"
import {Box, Typography } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';
import { BorderLeft } from '@mui/icons-material';

const RightSplitPart = () => {

    const titleStyle = {
        color:"rgba(91, 170, 100, 1)",
        mb:2
    }

    const splitBackground = {
        background:"rgba(244, 245, 246, 1)",
        mb:2,
        padding:2,
        borderRadius:2
    }

    const textDecoration = {
        borderLeft: "5px solid rgba(91, 170, 100, 1)",
        borderRadius: "4px",
        paddingLeft: "10px",
    }

    return(
        <>
        <Box sx={splitBackground}>
           <Box sx={textDecoration}>
          <Typography variant="h2" sx={titleStyle}>購買門票</Typography>
          </Box>
          <Box sx={{
            display:"flex"
          }}>
          <Typography sx={{
            mr:1,
            color:"rgba(91, 170, 100, 1)",
            mb:2
          }}>1/3</Typography>
          <Typography >選擇活動日期</Typography>
          </Box>
          {/* <Typography>
            這是右邊的部分，你可以在這裡放置任何內容。
          </Typography> */}
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DateCalendar         
            sx={{
            backgroundColor: 'white', // 设置背景为白色
            borderRadius: '8px', // 可选：添加圆角
            boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)', // 可选：添加阴影
            }}
        />
          </LocalizationProvider>
          <Box sx={{
            display:"flex",
            mt:2
          }}>
          <Typography sx={{
            mr:1,
            color:"rgba(91, 170, 100, 1)",
            mb:2
          }}>2/3</Typography>
          <Typography >選擇場次時間</Typography>
          </Box>
          <Box sx={{
            display:"flex",
            mt:2
          }}>
          <Typography sx={{
            mr:1,
            color:"rgba(91, 170, 100, 1)",
            mb:2
          }}>3/3</Typography>
          <Typography >選擇門票類型</Typography>
          </Box>
          </Box>  
        </>
    )
}

export default RightSplitPart;