"use client"
import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import ProductsRow from './ProductsRow';
import { Checkbox } from '@mui/material';
import xior from "xior";
import { IProduct } from "@/interface/IProduct";
import { useEffect, useState } from 'react';
import { IProfile } from '@/interface/IProfile';

type Props = {
  allProductsDtoList: IProduct[]
  getAllProductsApi:()=> void;
}

const ProductsTable = ({allProductsDtoList,getAllProductsApi}:Props) =>{

    return(
        <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>      
              <Checkbox
              />
      </TableCell>
              <TableCell></TableCell>
              <TableCell>Thumbnail</TableCell>
              <TableCell>Product Name</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Product Exclustive To</TableCell>
              <TableCell>Last Updated</TableCell>
              <TableCell>Created At</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
          {/* {Array.from({ length: 5 }, (_, index) => (
            <ProductsRow key={index} />
          ))} */}
          {
           allProductsDtoList && 
           allProductsDtoList.map((value)=>(
              <ProductsRow  
              key={value.id} 
              allProductsDto={value} 
              getAllProductsApi={getAllProductsApi}
              />
            ))
          }
          </TableBody>
        </Table>
      </TableContainer>
    )

}

export default ProductsTable