"use client";
import React, { useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  PaginationState,
  ColumnDef,
  getExpandedRowModel,
  ExpandedState,
} from "@tanstack/react-table";
import { Box, IconButton, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import PaginationTable from "@/components/PaginationTable";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { IProductCategory } from "@/interface/IProductCategory";
import PageSelector from "@/components/PageSelector";
import SearchInput from "@/components/input/SearchInput";
import ExpandMoreIcon from "@mui/icons-material/ExpandMoreRounded";
import EditProductCategory from "./EditProductCategory";
import DeleteProductCategory from "./DeleteProductCategory";

const ProductCategoryTable = () => {
  const t = useTranslations("");
  const [keyword, setKeyword] = React.useState("");
  const [expanded, setExpanded] = React.useState<ExpandedState>({});
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const {
    data,
    isLoading
  } = useQuery({
    queryKey: ["productCategory", { pagination, keyword }],
    queryFn: async () =>
      xior
        .get<IListResponse<IProductCategory>>("/api/product-category", {
          params: {
            page: pagination.pageIndex + 1,
            size: pagination.pageSize,
            keyword,
          },
        })
        .then((res) => res.data),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  const columns = useMemo<ColumnDef<IProductCategory>[]>(
    () => [
      {
        accessorKey: "id",
        header: "",
        cell: (data) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"space-between"}
            gap={1}
            mx={1}
          >
            <EditProductCategory category={data.row.original} />
            <DeleteProductCategory category={data.row.original} />
          </Box>
        ),
      },
      {
        accessorKey: "name",
        header: t("product_category.label_name"),
        minSize: 360,
        cell: ({ row, getValue }) => {
          const canExpand = row.getCanExpand();
          const isParent = !row.parentId;

          return (
            <Box display={"flex"} flexDirection={"row"} position="relative">
              {isParent && canExpand && (
                <IconButton
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    position: "absolute",
                    alignSelf: "center",
                  }}
                >
                  <ExpandMoreIcon
                    sx={{
                      transform: row.getIsExpanded() ? "rotate(180deg)" : "",
                    }}
                  />
                </IconButton>
              )}
              <Typography component="span" sx={{ pl: isParent ? 6 : 8 }}>
                {getValue<string>()}
              </Typography>
            </Box>
          );
        },
      },
    ],
    [t]
  );

  const defaultData = React.useMemo<IProductCategory[]>(() => [], []);

  const table = useReactTable({
    getRowId: (originalRow, index, parent) => {
      const parentId = parent?.getValue("id");
      return parentId
        ? `${parentId}.${originalRow.id}.${index}`
        : `${originalRow.id}.${index}}`;
    },
    data: data?.items ?? defaultData,
    columns,
    rowCount: data?.count,
    state: { pagination, expanded },
    getSubRows: (row) => row.children,
    onPaginationChange: setPagination,
    onExpandedChange: setExpanded,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    manualPagination: true,
    defaultColumn: {
      size: 100,
    },
  });

  return (
    <Box display={"flex"} flexDirection={"column"} height={"100%"}>
      <Box display={"flex"} flexDirection={"row"} mb={3}>
        <Box flex={1}>
          <SearchInput
            value={keyword}
            onChange={(value) => {
              setKeyword(value);
            }}
          />
        </Box>
        <PageSelector
          value={pagination.pageSize}
          onChange={(newPageSize) => {
            table.setPageSize(newPageSize);
          }}
        />
      </Box>
      <Box flex={1}>
        <PaginationTable table={table} isLoading={isLoading} msg={t("product_category.title_empty")}/>
      </Box>
    </Box>
  );
};

export default ProductCategoryTable;
