"use client";
import * as React from "react";
import { Input<PERSON>abel, MenuItem, Select as MuiSelect, SelectProps, TextField } from "@mui/material";
import { useTranslations } from "next-intl";
import InputContainer, { InputContainerProps } from "./InputContainer";

interface Props extends InputContainerProps {
  data: { label: string; value: string|number, component?: () => React.ReactNode, extra?: any }[];
  handleOnChange?: (val: any) => void
  endIcon?: () => React.ReactNode
  customRender?: (val: any) => React.ReactNode
}

export default function Select({
  label,
  description,
  variant = "outlined",
  fullWidth = true,
  error,
  required,
  multiple = false,
  data,
  value,
  sx,
  handleOnChange,
  customRender,
  endIcon,
  defaultValue,
  ...otherProps
}: Props & Omit<SelectProps, "error">) {
  const t = useTranslations("error");

  return (
    <TextField
      value={value}
      select
      defaultValue={defaultValue}
      label={label}
      fullWidth={fullWidth}
      SelectProps={{
        ...(customRender? {
          renderValue: customRender
        }: {}),
        ...(multiple ? {
          multiple,
        }: {})
      }}
      InputProps={{
        ...(endIcon? { endAdornment: endIcon() }: {})
      }}
      onChange={(event) => handleOnChange && handleOnChange(event.target.value)}
      sx={sx || {}}
      variant={variant}
    >
      {data.map(({ label, value, component }) => (
        <MenuItem value={value} key={value}>
          {component? component() :　label}
        </MenuItem>
      ))}
    </TextField>
  );
}
