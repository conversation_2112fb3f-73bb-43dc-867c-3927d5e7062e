import type { NextPage } from "next";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import ExportButton from "@/components/buttons/ExportButton";
import ImportButton from "@/components/buttons/ImportButton";
import CreateMemberGroup from "./components/CreateMemberGroup";
import MemberGroupTable from "./components/MemberGroupTable";

const MemberGroups: NextPage = () => {
  const t = useTranslations("member_group");

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={t("label_title")}>
        <>
          <ExportButton  />
          <ImportButton  />
          <CreateMemberGroup />
        </>
      </PageHeader>
      <Box flex={1} padding="26px 34px">
        <MemberGroupTable />
      </Box>
    </Box>
  );
};

export default MemberGroups;
