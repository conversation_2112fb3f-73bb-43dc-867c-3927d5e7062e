"use client";
import * as React from "react";
import {
  Box,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  MenuItem,
  MenuList,
  Typography,
} from "@mui/material";
import SvgIcon from '@mui/material/SvgIcon';
import { COLORS } from "@/styles/colors";
import DashboardIcon from "./icons/DashboardIcon";
import { useTranslations } from "next-intl";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import  RoomIcon  from "./icons/RoomIcon";
import SalesOrdersIcon from "./icons/SalesOrdersIcon";
import ProductsIcon from "./icons/ProductsIcon";
import MembersIcon from "./icons/MembersIcon";
import MessageIcon from "./icons/MessageIcon";
import ProductCategoryIcon from "./icons/ProductCategoryIcon"
import CollectionIcon from "./icons/CollectionIcon";
import OwnerIcon from "./icons/OwnerIcon";
import TagsIcon from "./icons/TagsIcon";
// import SalesOrdersIcon from './icons/SalesOrdersIcon';

type DrawerRouteBase = { label: string; route: string };
type DrawerRoute = (DrawerRouteBase & { Icon: typeof SvgIcon; children?: DrawerRouteBase[] })[];
export const DRAWER:DrawerRoute = [
  { label: "salesOrder", Icon: SalesOrdersIcon, route: ROUTES.ORDER_LIST },
  // {
  //   label: "room_information",
  //   Icon: RoomIcon,
  //   route: ROUTES.ROOMS,
  // },
  // { label: "sales_orders", Icon: SalesOrdersIcon, route: ROUTES.SALES_ORDER,
  //   children: [
  //     {
  //       label: "orders_list",
  //       route: ROUTES.ORDER_LIST,
  //     },
  //     {
  //       label: "delivery_note",
  //       route: ROUTES.DELIVERY_NOTE,
  //     },
  //   ],
  // },

  // // {
  // //   label: "products",
  // //   Icon: DashboardIcon,
  // //   route: ROUTES.PRODUCT
  // // },
  // {
  //   label: "product",
  //   Icon: ProductsIcon,
  //   route: ROUTES.PRODUCTS,
  //   children: [
  //     {
  //       label: "product_list",
  //       route: ROUTES.PRODUCT_LIST,
  //     },
  //     {
  //       label: "inventory",
  //       route: ROUTES.INVENTORY,
  //     },
  //   ],
  // },
  // {
  //   label: "member_management",
  //   Icon: MembersIcon,
  //   route: ROUTES.MEMBER_MANAGEMENT,
  //   children: [
  //     {
  //       label: "member_list",
  //       route: ROUTES.MEMBER_LIST,
  //     },
  //     {
  //       label: "member_group",
  //       route: ROUTES.MEMBER_GROUP,
  //     },
  //     {
  //       label: "invitation",
  //       route: ROUTES.INVITATION,
  //     },
  //   ],
  // },
  // { label: "message", Icon: MessageIcon, route: ROUTES.MESSAGE },
  // {
  //   label: "product_category",
  //   Icon: ProductCategoryIcon,
  //   route: ROUTES.PRODUCT_CATEGORY,
  // },
  // { label: "collection", Icon: CollectionIcon, route: ROUTES.COLLECTION },
  // { label: "owner", Icon: OwnerIcon, route: ROUTES.OWNER },
  // { label: "tags", Icon: TagsIcon, route: ROUTES.TAGS },
  // {
  //   label: "settings",
  //   Icon: DashboardIcon,
  //   route: ROUTES.SETTINGS,
  //   children: [
  //     {
  //       label: "profile",
  //       route: ROUTES.PROFILE,
  //     },
  //     {
  //       label: "change_password",
  //       route: ROUTES.CHANGE_PASSWORD,
  //     },
  //   ],
  // },
];

export default function SideBar() {
  const pathname = usePathname();
  const t = useTranslations("sidebar");
  const router = useRouter();

  const menuItems = React.useMemo(() => {
    const items = [];
    for (const { label, Icon, route, children } of DRAWER) {
      items.push(
        <MenuItem
          key={label}
          selected={pathname.split("/")[1] === route.replace("/", "")}
          href={children ? children[0].route : route}
          onClick={() => {
            router.push(children ? children[0].route : route);
          }}
          sx={{ minHeight: 36, height: 36, px: 2.75, mb: 1.5 }}
        >
          <ListItemIcon>
            <Icon sx={{ height: 14, width: 14 }} />
          </ListItemIcon>
          <ListItemText>
            <Typography fontSize={13} fontWeight="400">
              {t(label)}
            </Typography>
          </ListItemText>
        </MenuItem>
      );

      if (children && pathname.startsWith(route)) {
        for (const child of children) {
          const selected = pathname.startsWith(child.route);

          items.push(
            <ListItemButton
              key={child.label}
              href={child.route}
              onClick={() => router.push(child.route)}
              sx={{ pl: 6 }}
            >
              <ListItemText>
                <Typography
                  fontSize={13}
                  fontWeight="400"
                  sx={{
                    fontWeight: selected ? 700 : 400,
                    color: selected ? COLORS.BLUE : undefined,
                  }}
                >
                  {t(child.label)}
                </Typography>
              </ListItemText>
            </ListItemButton>
          );
        }
      }
    }
    return items;
  }, [pathname, router, t]);

  return (
    <Box sx={{ background: COLORS.WHITE, minWidth: 200 }}>
      <MenuList>{menuItems}</MenuList>
    </Box>
  );
}
