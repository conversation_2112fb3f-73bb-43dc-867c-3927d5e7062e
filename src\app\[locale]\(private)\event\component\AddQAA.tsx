"use client";
import * as React from "react";
import { <PERSON>, Modal, TextField, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { useRouter } from "next/navigation";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";
import AddNewButton from "@/components/buttons/AddNewButton";
import MyButton from '@/components/buttons/CustomButton';
import EditButton from "@/components/buttons/EditButton";
import { LANGUAGE_CODE } from "@/utils/constants";

interface Props {
    eventId: number,
    content: {id: number, answser: string, question: string}[];
    language: LANGUAGE_CODE
}

const AddQAA = ({ content, eventId, language }: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const t = useTranslations("event");
  const tCommon = useTranslations("common");
  const [open, setOpen] = React.useState(false);
  const [introduction, setIntroduction] = React.useState('');
  const [qaa, setQAA] = React.useState<any>({});

  const mutation = useMutation({
    mutationFn: () => {
        return xior.put(`/api/event/qaa/${eventId}`, {qaa: [...content, qaa], language})
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["event"]});
      setOpen(false);
    },
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (mutation.isPending) return;
    setOpen(false);
  }, [mutation.isPending]);

  const handleUpdate = () => {
    mutation.mutate()
  }

  const handleChange = (target: 'question'|'answer', value: string) => {
    setQAA({
        ...qaa,
        [target]: value
    })
  }

  return (
    <Box>
         <EditButton
            label={tCommon("button_add")}
            isPrimay={false}
            isSmall={true}
            onClick={handleOpen}
        />
        <Modal open={open} onClose={handleClose}>
            <ModalContainer sx={{ padding: '40px', gap: '32px' }}>
                <Typography variant="h3">{t("qaa")}</Typography>
                <TextField
                    size="medium"
                    disabled={mutation.isPending}
                    label={t("question")}
                    value={qaa.question}
                    onChange={(event) => handleChange('question', event.target.value)}
                    placeholder={t("question")}
                />

                <TextField
                    size="medium"
                    disabled={mutation.isPending}
                    label={t("answer")}
                    value={qaa.answer}
                    onChange={(event) => handleChange('answer', event.target.value)}
                    placeholder={t("answer")}
                />

                <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginTop: "30px" }}>
                    <CancelButton onAction={handleClose} />
                    <UpdateButton onAction={handleUpdate} />
                </Box>
            </ModalContainer>
        </Modal>
    </Box>
  );
};

export default AddQAA;
