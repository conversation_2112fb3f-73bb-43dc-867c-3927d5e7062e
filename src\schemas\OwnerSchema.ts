import { z } from "zod";
import { SCHEMA_ERRORS } from "@/utils/constants";
import { ImageFileSchema } from "./ImageFileSchema";

export const OwnerSchema = z.object({
  photoUrl: z.union([ImageFileSchema, z.string()]),
  name: z
    .string({ required_error: SCHEMA_ERRORS.required })
    .min(1, SCHEMA_ERRORS.required),
  email: z.union([z.literal(""), z.string().email(SCHEMA_ERRORS.invalidEmail)]),
  introduction: z.string().optional(),
  gender: z.string().optional(),
  countryCode: z.string(),
  socialMedias: z.object({
    method: z.string(),
    value: z.string()
  }).array()
});
