import {Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material"
import SingleInventoryRow from "./SingleInventoryRow"
import { IInventory } from "@/interface/IInventory";
import { IProductWarehouseRecords } from "@/interface/IProductWarehouseRecord";

type Props = {
    getInventoryDto:IInventory
    params: { id: string }
    addProductWarehouseRecords:IProductWarehouseRecords
    handleAddRecordsChange:(addProductWarehouseRecords:IProductWarehouseRecords)=>void
}

const SingleInventoryTable = ({getInventoryDto,params,addProductWarehouseRecords,handleAddRecordsChange}:Props) =>{

    console.log(getInventoryDto)

    return(
        <>
        <Typography variant="h6" >Current Inventory Level</Typography>
            <Table sx={{ minWidth: 650, mt:"10px",mb:"10px" }} aria-label="simple table">
                    <TableHead>
                    <TableRow>
                        <TableCell>SKU</TableCell>
                        <TableCell>Unavailable</TableCell>
                        <TableCell>Committed</TableCell>
                        <TableCell>Available</TableCell>
                    </TableRow>
                    </TableHead>
                    <TableBody>
            
                        <TableRow>
                        <TableCell>{getInventoryDto?.sku ? getInventoryDto?.sku : "-"}</TableCell>
                        <TableCell>{getInventoryDto?.unavailable ? getInventoryDto?.unavailable : "-" }</TableCell>
                        <TableCell>{getInventoryDto?.currentCommitted? getInventoryDto?.currentCommitted: "-"}</TableCell>
                        <TableCell>{getInventoryDto?.currentQuantity? getInventoryDto?.currentQuantity:"-"}</TableCell>
                        </TableRow>
                    </TableBody>
            </Table>
            <TableContainer >
            <Typography variant="h6" >Edit Inventory Level</Typography>
            <Table sx={{ minWidth: 650, mt:"10px",mb:"10px"  }} aria-label="simple table">
                <TableHead>
                <TableRow>
                    <TableCell></TableCell>
                    <TableCell>Thumbnail</TableCell>
                    <TableCell>Product Name</TableCell>
                    <TableCell>SKU</TableCell>
                    <TableCell>Product Type</TableCell>
                    <TableCell>Unavailable</TableCell>
                    <TableCell>Committed</TableCell>
                    {/* <TableCell>Returned</TableCell> */}
                    <TableCell>Available</TableCell>
                </TableRow>
                </TableHead>
                <TableBody>
                {
                    getInventoryDto &&
                    <SingleInventoryRow 
                    getInventoryDto={getInventoryDto} params={params}
                    addProductWarehouseRecords={addProductWarehouseRecords}
                    handleAddRecordsChange={handleAddRecordsChange}
                    />
                }
                </TableBody>
            </Table>
        </TableContainer>
        </>
    )
}

export default SingleInventoryTable