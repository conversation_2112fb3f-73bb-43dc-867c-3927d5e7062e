import {
  authorizedPut,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";

async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const data = await authorizedPut<any>(
      "/api/admin/v1/ticket/type/toggle",
    //   await getAuthHeaders(req),,
      {},
      body
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { PUT };
