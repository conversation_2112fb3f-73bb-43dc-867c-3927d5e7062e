"use client";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import TextField from "@/components/input/TextField";
import PageHeader from "@/components/PageHeader";
import { ChangePasswordSchema } from "@/schemas/ChangePasswordSchema";
import { ROUTES } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box } from "@mui/material";
import type { NextPage } from "next";
import { signOut } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior, { XiorError } from "xior";
import { z } from "zod";

type FormValue = z.infer<typeof ChangePasswordSchema>;

const Profile: NextPage = () => {
  const router = useRouter();
  const t = useTranslations();

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors, isDirty },
    setError,
    reset,
  } = useForm<FormValue>({
    resolver: zodResolver(ChangePasswordSchema),
    defaultValues: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      await xior.post("/api/auth/change-password", data);
      signOut()
    } catch (e) {
      if (e instanceof XiorError) {
        switch (e?.response?.data) {
          case "invalid_credentials":
            setError("oldPassword", { message: "invalid_credential" });
            break;
          default:
            setError("oldPassword", { message: "unknown_error" });
        }
      } else {
        setError("oldPassword", { message: "unknown_error" });
      }
    }
  };

  return (
    <Box
      sx={{ height: "100%" }}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
    >
      <PageHeader title={[t("change_password.label_title")]}>
        <>
          <CancelButton disabled={isSubmitting} onAction={reset} />
          <SaveButton disabled={!isDirty || isSubmitting} />
        </>
      </PageHeader>
      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
        maxWidth={450}
      >
        <Controller
          name="oldPassword"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              size="medium"
              disabled={isSubmitting}
              type="password"
              label={t("change_password.label_current_password")}
              error={errors?.oldPassword?.message}
            />
          )}
        />
        <Controller
          name="newPassword"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              size="medium"
              disabled={isSubmitting}
              type="password"
              label={t("change_password.label_new_password")}
              description={t("change_password.description_new_password")}
              error={errors?.newPassword?.message}
            />
          )}
        />
        <Controller
          name="confirmPassword"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              size="medium"
              disabled={isSubmitting}
              type="password"
              label={t("change_password.label_confirm_password")}
              error={errors?.confirmPassword?.message}
            />
          )}
        />
      </Box>
    </Box>
  );
};

export default Profile;
