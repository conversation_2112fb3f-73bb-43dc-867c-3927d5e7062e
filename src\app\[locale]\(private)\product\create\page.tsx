"use client";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import FileInput from "@/components/input/FileInput";
import ProductTable from "@/components/input/ProductTable";
import Select from "@/components/input/Select";
import SelectCategory from "@/components/input/SelectCategory";
import SelectCollection from "@/components/input/SelectCollection";
import SelectMemberGroup from "@/components/input/SelectMemberGroup";
import SelectOwner from "@/components/input/SelectOwner";
import SelectTag from "@/components/input/SelectTag";
import TextField from "@/components/input/TextField";
import PageHeader from "@/components/PageHeader";
import useFileUpload from "@/hooks/useFileUpload";
import { ProductSchema } from "@/schemas/ProductSchema";
import { ROUTES, SUPPORT_CURRENCY, SUPPORT_WEIGTH, UNITY_ENDPOINT } from "@/utils/constants";
import { SUPPORT_TIMEZONE } from "@/utils/timezone";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Box,
  FormControlLabel,
  Typography,
  RadioGroup,
  Radio,
  Tabs,
  Tab,
  Checkbox
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import type { NextPage } from "next";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";
import ProductImageTable from "../components/ProductImageTable";
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import InputContainer from "@/components/input/InputContainer";
import { RoomSchema } from "@/schemas/RoomSchema";
import { ICollection } from "@/interface/ICollection";

type FormValue = z.infer<typeof RoomSchema>;

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const initialValues = {
  collection: [] as ICollection[],  // 确保类型
  // 其他字段
};

const CustomTabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ProductDetail: NextPage = () => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const router = useRouter();
  const { uploadFile } = useFileUpload();

  const [value, setValue] = useState(0);
  const [includeTax, setIncludeTax] = useState(0);
  const [isPublic, setIsPublic] = useState(0);
  const [type, setType] = useState(1)


  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors, isDirty },
    reset,
    setError,
    setValue: setFormValue,
  } = useForm<FormValue>({
    // resolver: zodResolver(ProductSchema),
    defaultValues: {
      audio: undefined,
      isDigital: 1,
      status: 1,
      productType: 1,
      currency: 'hkd',
      weightUnit: 'lb',
      price: '0.0',
      bidUnit: '0.0',
      type: 1,
      startDate: 0,
      endDate: 0,
      taxIncluded: 0,
      inventory: 0,
      roomExcluded: [],
      openToAllMembers: true,
      category: undefined,
      collections: [],
      description: undefined,
      name: undefined,
      owner: 0,
      productList: [],
      tags: [],
      thumbnail: undefined,
    },
  });

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      // await ProductSchema.parseAsync(data)
      const {
        thumbnail,
        productList
      } = data
      // const thumbnails = await Promise.all(Object.keys(thumbnail).map((key) => uploadFile(data?.thumbnail?.[key], "productThumbnail")))
      // const productImages = await Promise.all(productList.map((asset: any) => uploadFile(asset.file, "productAsset")))
      // const product = productList?.map((item: any, idx: number) => ({ name: item.name, sorting: item.sorting, url: productImages[idx] }))
      await xior.post("/api/product", {
        name: data.name,
        description: data.description,
        // thumbnail: thumbnails,
        audio: data.audio,
        status: data.status,
        price: data.price,
        taxIncluded: data.taxIncluded,
        tax: data.tax || '0.0',
        type: data.type,
        currency: data.currency,
        weight: data.weight,
        weightUnit: data.weightUnit,
        isDigital: data.isDigital,
        openToAllMembers: data.openToAllMembers,
        category: data.category,
        compareAt: data.compareAt,
        owner: data.owner,
        productType: data.productType,
        roomExcluded: data.roomExcluded,
        collections: data.collections,
        tags: data.tags,
        inventory: data.inventory,
        // product: product
      });
      queryClient.invalidateQueries({ queryKey: ["product"] });
      router.push(ROUTES.PRODUCT);
    } catch (e) {
      console.log("e >> ", e)
      // if (e?.issues) {
      //   e?.issues?.map((err) => {
      //     if (err?.path) {
      //       setError(err.path[0], {
      //         type: "custom",
      //         message: "required",
      //       })
      //     }
      //   })
      // }
    }
  };

  const renderProductInformation = () => {
    return (
      <>
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              disabled={isSubmitting}
              label={t("product.label_name")}
              placeholder={t("product.placeholder_name")}
              error={errors?.name?.message}
            />
          )}
        />
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              required
              label={t("product.label_description")}
              placeholder={t("product.label_placeholder_description")}
              error={errors?.description?.message}
              multiline
              rows={8}
            />
          )}
        />
        <Controller
          name="productType"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("product.label_product_type")}
              </Typography>
              <RadioGroup
                // label={"product.label_product_type"}
                name="productType"
                value={field.value}
                onChange={field.onChange}
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
              >
                <FormControlLabel value={1} control={<Radio />} label={t('product.label_physical_product')} />
                <FormControlLabel value={2} control={<Radio />} label={t('product.label_digital_product')} />
              </RadioGroup>
            </>
          )}
        />
        <Controller
          name="audio"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("product.label_product_audio")}
              </Typography>
              <Typography fontSize={12} color={'grey'}>
                <i>
                  {
                    t("file_upload.audio_metadata").split("\n").map((item: string) => (
                      <>{item}<br /></>
                    ))
                  }
                </i>
              </Typography>
              <FileInput
                value={field.value}
                onChange={field.onChange}
                metadata={t("file_upload.background_music_upload")}
                disabled={isSubmitting}
                error={errors.audio?.message}
                type="audio"
              />
            </>
          )}
        />
        <Controller
          name="collections"
          control={control}
          render={({ field }) => (
            <SelectCollection
              value={field?.value as ICollection[]?? []}
              onChange={field.onChange}
              label={t("product.label_collection")}
              placeholder={t("product.collection_description")}
              disabled={isSubmitting}
              error={errors?.description?.message}
            />
          )}
        />
        <Controller
          name="owner"
          control={control}
          render={({ field }) => (
            <SelectOwner
              value={field.value}
              onChange={field.onChange}
              label={t("product.label_owner")}
              placeholder={t("product.owner_description")}
              disabled={isSubmitting}
              error={errors?.description?.message}
            />
          )}
        />
        <Controller
          name="category"
          control={control}
          render={({ field }) => (
            <InputContainer label={t("product.label_category")} required>
              <SelectCategory
                value={field.value}
                onChange={field.onChange}
                disabled={isSubmitting}
                error={errors?.category?.message}
              />
            </InputContainer>
          )}
        />
        <Controller
          name="tags"
          control={control}
          render={({ field }) => (
            <SelectTag
              value={field.value}
              onChange={field.onChange}
              label={t("product.label_tag")}
              placeholder={t("product.placeholder_tags")}
              disabled={isSubmitting}
              error={errors?.description?.message}
            />
          )}
        />
        <Controller
          name="thumbnail"
          control={control}
          render={({ field }) => (
            <InputContainer label={t("product.label_list_of_thumbnail")} required>
              <Typography fontSize={12} color={'grey'}>
                <i>
                  {
                    t("file_upload.image_video_metadata").split("\n").map((item: string) => (
                      <>{item}<br /></>
                    ))
                  }
                </i>
              </Typography>
              <FileInput
                multiple={true}
                value={field.value}
                onChange={field.onChange}
                metadata={t("file_upload.thumbnail_multimedia_upload")}
                disabled={isSubmitting}
                error={errors.thumbnail?.message}
                type="image_video"
              />
            </InputContainer>
          )}
        />
        <Controller
          name="productList"
          control={control}
          render={({ field: { onChange, value } }) => (
            <FormControlLabel
              label={"form"}
              sx={{ alignSelf: "start", mx: 0, mb: 1.5 }}
              labelPlacement="start"
              control={
                <ProductImageTable value={value} onChange={onChange} />
              }
            // fullWidth={true}
            />
          )}
        />
      </>
    )
  }

  const renderSellingPrice = () => {
    return (
      <>
        <Controller
          name="price"
          control={control}
          render={({ field }) => (
            <InputContainer label={t("product.label_price")} required>
              <Box display={'flex'} flexDirection={"row"} gap={1}>
                <Select
                  sx={{ width: 100 }}
                  data={SUPPORT_CURRENCY}
                  onChange={(event) => {
                    return (
                      setFormValue("currency", JSON.stringify(event.target.value))
                    )
                  }}
                />
                <TextField
                  value={field.value}
                  onChange={field.onChange}
                  required
                  disabled={isSubmitting}

                  placeholder={"0.00"}
                  error={errors?.price?.message}
                />
              </Box>
            </InputContainer>
          )}
        />
        <Controller
          name="compareAt"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("product.label_compare_at_price")}
              </Typography>
              <Typography fontSize={12} color={"grey"}>
                {t("product.compare_at_price_description")}
              </Typography>
              <TextField
                value={field.value}
                onChange={field.onChange}
                required
                disabled={isSubmitting}
                placeholder={"0.00"}
              // error={errors?.compare_at_price?.message}
              />
            </>
          )}
        />
      </>
    )
  }

  const renderAuctionPrice = () => {
    return (
      <>
        <Controller
          name="price"
          control={control}
          render={({ field }) => (
            <InputContainer label={t("product.label_start_bidding")} required>
              <Box display={'flex'} flexDirection={"row"} gap={1}>
                <Select
                  sx={{ width: 100 }}
                  data={SUPPORT_CURRENCY}
                  onChange={(event) => {
                    setFormValue("currency", JSON.stringify(event.target.value))
                  }}
                />
                <TextField
                  value={field.value}
                  onChange={field.onChange}
                  required
                  disabled={isSubmitting}

                  placeholder={"0.00"}
                  error={errors?.price?.message}
                />
              </Box>
            </InputContainer>
          )}
        />
        <Controller
          name="bidUnit"
          control={control}
          render={({ field }) => (
            <InputContainer label={t("product.label_compare_at_price")} required>
              <Typography fontSize={12} color={"grey"}>
                {t("product.compare_at_price_description")}
              </Typography>
              <TextField
                value={field.value}
                onChange={field.onChange}
                required
                disabled={isSubmitting}
                placeholder={"0.00"}
              // error={errors?.compare_at_price?.message}
              />
            </InputContainer>
          )}
        />
        <Controller
          name="duration"
          control={control}
          render={({ field }) => (
            <InputContainer label={t("product.label_duration")} required>
              <Select
                sx={{ width: 402 }}
                data={SUPPORT_TIMEZONE}
                onChange={(event) => {
                  setFormValue("timezone", JSON.stringify(event.target.value))
                }}
              />
              <Box display={'flex'} flexDirection={"row"} gap={1}>
                <Typography fontSize={14}>
                  {t("product.label_from")}
                </Typography>
                <DateTimePicker
                  value={new Date(value)}
                  format="d MMM yyyy hh:mma"
                  onChange={(newValue) => setFormValue("startDate", new Date(Number(newValue)).valueOf())}
                />
                <Typography fontSize={14}>
                  {t("product.label_to")}
                </Typography>
                <DateTimePicker
                  value={new Date(value)}
                  format="d MMM yyyy hh:mma"
                  onChange={(newValue) => setFormValue("endDate", new Date(Number(newValue)).valueOf())}
                />
              </Box>

            </InputContainer>
          )}
        />
      </>
    )
  }

  const renderSelling = () => {
    return (
      <>
        <Controller
          name="status"
          control={control}
          render={({ field }) => (
            <InputContainer label={t("product.label_status")} required>
              <RadioGroup
                name="status"
                value={field.value || 1}
                onChange={field.onChange}
                row
              >
                <FormControlLabel value={1} control={<Radio />} label={t('product.label_draft')} />
                <FormControlLabel value={2} control={<Radio />} label={t('product.label_listed')} />
                <FormControlLabel value={3} control={<Radio />} label={t('product.label_unlisted')} />
              </RadioGroup>
            </InputContainer>
          )}
        />
        <Controller
          name="type"
          control={control}
          render={({ field }) => (
            <InputContainer label={t("product.label_selling_type")} required>
              <Typography fontSize={12} color={"grey"}>
                {t("product.selling_type_description")}
              </Typography>
              <RadioGroup
                name="type"
                value={field.value || 1}
                onChange={(event) => {
                  field.onChange(event)
                  setType(Number(event.target.value))
                }}
                row
              >
                <FormControlLabel value={1} control={<Radio />} label={t('product.label_retail')} />
                <FormControlLabel value={2} control={<Radio />} label={t('product.label_auction')} />
              </RadioGroup>
            </InputContainer>
          )}
        />
        {
          type === 1 && renderSellingPrice()
        }
        {
          type === 2 && renderAuctionPrice()
        }
        <Controller
          name="inventory"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("product.label_inventory")}
              </Typography>
              <TextField
                value={field.value}
                onChange={field.onChange}
                required
                disabled={isSubmitting}
                placeholder={"0"}
                error={errors?.inventory?.message}
              />
            </>
          )}
        />
        <Controller
          name="taxIncluded"
          control={control}
          render={({ field }) => (
            <>
              <FormControlLabel
                control={<Checkbox defaultChecked={false} onChange={(event) => {
                  field.onChange({
                    target: {
                      value: event.target.checked
                    }
                  })
                  setIncludeTax(event.target.checked ? 0 : 1)
                }} />}
                label={t("product.label_charge_tax")}
                defaultChecked={false}
              />
            </>
          )}
        />
        {
          includeTax ? (
            <Controller
              name="tax"
              control={control}
              render={({ field }) => (
                <InputContainer label={t("product.label_tax_rate")} required>
                  <TextField
                    value={field.value}
                    onChange={field.onChange}
                    required
                    disabled={isSubmitting}
                    placeholder={"0.00"}
                    error={errors?.name?.message}
                  />
                </InputContainer>
              )}
            />
          ) : null
        }
        <Controller
          name="weight"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("product.label_weight")}
              </Typography>
              <Typography fontSize={12} color={"grey"}>
                {t("product.weight_description")}
              </Typography>
              <Box display={'flex'} flexDirection={"row"} gap={1}>
                <TextField
                  value={field.value}
                  onChange={field.onChange}
                  disabled={isSubmitting}
                  placeholder={"0.0"}
                  error={errors?.weight?.message}
                />
                <Select
                  sx={{ width: 80 }}
                  data={SUPPORT_WEIGTH}
                  onChange={(event) => {
                    setFormValue("weightUnit", JSON.stringify(event.target.value))
                  }}
                />
              </Box>
            </>
          )}
        />
        <Controller
          name="openToAllMembers"
          control={control}
          render={({ field }) => (
            <>
              <FormControlLabel
                control={<Checkbox defaultChecked={false} onChange={(event) => {
                  field.onChange({
                    target: {
                      value: event.target.checked
                    }
                  })
                  setIsPublic(event.target.checked ? 0 : 1)
                }} />}
                label={t("product.label_open_to_public")}
                defaultChecked={false}
              />
            </>
          )}
        />
        {
          !isPublic ? (
            <Controller
              name="roomExcluded"
              control={control}
              render={({ field }) => (
                <>
                  <Typography fontSize={14}>
                    {t("product.label_room_exclusive_to")}
                  </Typography>
                  <SelectMemberGroup
                    value={field.value}
                    onChange={field.onChange}
                    placeholder={t('product.label_member_group')}
                  />
                </>
              )}
            />
          ) : null
        }
      </>
    )
  }

  const a11yProps = (index: number) => {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    // setValue(newValue); // seValue is number but newValue is string
  };

  return (
    <Box
      sx={{ height: "100%" }}
      display={"flex"}
      flexDirection={"column"}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
    >
      <PageHeader
        title={[
          t("product.label_product_detail"),
          t("product.label_product_create"),
        ]}
      >
        <>
          <CancelButton
            disabled={isSubmitting}
            onAction={() => router.push(ROUTES.PRODUCT)}
          />
          <SaveButton disabled={!isDirty} />
        </>
      </PageHeader>
      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
        maxWidth={450}
      >
        <Box>
          <Tabs
            value={value}
            onChange={handleChange}
            TabIndicatorProps={{ style: { backgroundColor: 'blue' } }}
          >
            <Tab label={t("product.label_information")} {...a11yProps(0)} />
            <Tab label={t("product.label_selling")} {...a11yProps(1)} />
          </Tabs>
        </Box>
        <CustomTabPanel value={value} index={0}>
          {
            renderProductInformation()
          }
        </CustomTabPanel>
        <CustomTabPanel value={value} index={1}>
          {
            renderSelling()
          }
        </CustomTabPanel>
      </Box>
    </Box>
  );
};

export default ProductDetail;
