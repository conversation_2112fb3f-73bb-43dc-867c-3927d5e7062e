# ✅ 使用官方 Docker 映像作為執行環境
image: docker:24.0.5

# ✅ 開啟 Docker-in-Docker（DinD）服務，用來在 runner 裡建置 Docker image
services:
  - name: docker:24.0.5-dind
    command: ["--tls=false"]

# ✅ 定義整體 pipeline 的階段
stages:
  - test
  - push
  - deploy

# ✅ 環境共用變數
variables:
  AWS_REGION: ap-southeast-1
  ECR_REGISTRY: 433299495796.dkr.ecr.ap-southeast-1.amazonaws.com
  IMAGE_NAME: incutix/admin
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""

default:
  before_script:
    - apk add --no-cache curl python3 py3-pip
    - pip install awscli
    - echo "🔐 設定 AWS 憑證與區域"
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $AWS_REGION
    - echo "🔐 登入 AWS ECR"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY

# ✅ test job for feature/cicd branch
unit_test:
  stage: test
  image: node:20
  before_script:
    - npm ci
  script:
    - npm run lint
    - npm test
  only:
    - feature/cicd

build_and_push:
  stage: push
  image: docker:24.0.5
  services:
    - name: docker:24.0.5-dind
      command: ["--tls=false"]
  script:
    - echo "=============================="
    - echo "🔧 [STAGE 1] Building image for $CI_COMMIT_REF_NAME"
    - echo "=============================="
    - |
      if [ "$CI_COMMIT_REF_NAME" = "main" ]; then
        export IMAGE_TAG=main
      elif [ "$CI_COMMIT_REF_NAME" = "uat" ]; then
        export IMAGE_TAG=uat
      elif [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
        export IMAGE_TAG=dev
      else
        export IMAGE_TAG=test
      fi
      echo "Using IMAGE_TAG=$IMAGE_TAG"
      cat env_$IMAGE_TAG > .env
      docker build -t $IMAGE_NAME:$IMAGE_TAG .
      echo "✅ Build complete"
      echo "=============================="
      echo "📦 [STAGE 2] Tagging image"
      echo "=============================="
      docker tag $IMAGE_NAME:$IMAGE_TAG $ECR_REGISTRY/$IMAGE_NAME:$IMAGE_TAG
      echo "✅ Tagging complete"
      echo "=============================="
      echo "🚀 [STAGE 3] Pushing image to ECR"
      echo "=============================="
      docker push $ECR_REGISTRY/$IMAGE_NAME:$IMAGE_TAG
      echo "✅ Push complete"
  only:
    - main
    - uat
    - dev
    - feature/cicd

deploy_to_ecs:
  stage: deploy
  image: amazonlinux:2
  before_script:
    - yum install -y unzip curl
    - yum install -y jq
    - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
    - unzip awscliv2.zip
    - ./aws/install
    - export PATH=/usr/local/bin:$PATH
  script:
    - set -x
    - echo "AWS CLI version:" && aws --version
    - echo "AWS_REGION is $AWS_REGION"
    - echo "AWS_ACCESS_KEY_ID is $AWS_ACCESS_KEY_ID"
    - echo "AWS_SECRET_ACCESS_KEY is $AWS_SECRET_ACCESS_KEY"
    - echo "AWS_SESSION_TOKEN is $AWS_SESSION_TOKEN"
    - "echo 'Current directory: $(pwd)'"
    - "echo 'Files in current directory:' && ls -al"
    - "aws sts get-caller-identity || { echo 'Failed to verify AWS credentials'; exit 1; }"
    - |
      if [ "$CI_COMMIT_REF_NAME" = "main" ]; then
        export SERVICE_NAME=incutix-prod-admin-service
        export ENV_URL=https://market.incutix.com
        export TASK_FAMILY=incutix-prod-admin-task
        export IMAGE_TAG=latest
      elif [ "$CI_COMMIT_REF_NAME" = "uat" ]; then
        export SERVICE_NAME=incutix-uat-admin-service
        export ENV_URL=https://uat-market.incutix.com
        export TASK_FAMILY=incutix-uat-admin-task
        export IMAGE_TAG=uat
      elif [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
        export SERVICE_NAME=incutix-dev-admin-service
        export ENV_URL=https://dev-market.incutix.com
        export TASK_FAMILY=incutix-dev-admin-task
        export IMAGE_TAG=dev
      else
        export SERVICE_NAME=incutix-dev-admin-service
        export ENV_URL=https://dev-market.incutix.com
        export TASK_FAMILY=incutix-dev-admin-task
        export IMAGE_TAG=test
      fi
      echo "SERVICE_NAME is $SERVICE_NAME"
      echo "ENV_URL is $ENV_URL"
      echo "TASK_FAMILY is $TASK_FAMILY"
      echo "IMAGE_TAG is $IMAGE_TAG"
    - |
      if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
        echo "Fetching current task definition for $TASK_FAMILY"
        aws ecs describe-task-definition --task-definition $TASK_FAMILY > task-def.json
        cat task-def.json
        echo "Updating image in container definitions to $ECR_REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
        jq --arg IMAGE "$ECR_REGISTRY/$IMAGE_NAME:$IMAGE_TAG" '.taskDefinition.containerDefinitions[0].image = $IMAGE' task-def.json > new-task-def.json
        jq '{
          family: .taskDefinition.family,
          networkMode: .taskDefinition.networkMode,
          containerDefinitions: .taskDefinition.containerDefinitions,
          requiresCompatibilities: .taskDefinition.requiresCompatibilities,
          cpu: .taskDefinition.cpu,
          memory: .taskDefinition.memory,
          executionRoleArn: .taskDefinition.executionRoleArn,
          taskRoleArn: .taskDefinition.taskRoleArn
        }' new-task-def.json > register-task-def.json
        echo "Registering new task definition revision"
        aws ecs register-task-definition --cli-input-json file://register-task-def.json > reg-task-def-out.json
        export TASK_REVISION=$(jq -r '.taskDefinition.revision' reg-task-def-out.json)
        echo "New task definition revision: $TASK_REVISION"
        echo "Updating ECS service to use new task definition revision"
        aws ecs update-service --cluster incutix-cluster --service $SERVICE_NAME --task-definition $TASK_FAMILY:$TASK_REVISION --force-new-deployment --region $AWS_REGION || { echo 'ECS update-service failed'; exit 1; }
      else
        echo "About to run: aws ecs update-service --cluster incutix-cluster --service $SERVICE_NAME --force-new-deployment --region $AWS_REGION"
        aws ecs update-service --cluster incutix-cluster --service $SERVICE_NAME --force-new-deployment --region $AWS_REGION || { echo 'ECS update-service failed'; exit 1; }
      fi
  only:
    - feature/cicd
    - dev
    - main
    - uat
  environment:
    name: $CI_COMMIT_REF_NAME
    url: $ENV_URL