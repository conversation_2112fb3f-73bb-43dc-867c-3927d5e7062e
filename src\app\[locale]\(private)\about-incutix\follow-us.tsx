import {
    Box,
    Typography,
  } from "@mui/material";
import * as React from 'react';
import FacebookIcon from '@mui/icons-material/Facebook';
import InstagramIcon from '@mui/icons-material/Instagram';
import YouTubeIcon from '@mui/icons-material/YouTube';
import { IconButton } from '@mui/material';
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';
import Image from "next/image";

const FollowUs = () =>{

    const handleFaceBookLink = () =>{
        window.open("https://www.google.com/", "_blank");
    }

    return(
        <>
        <Box sx={{
            display:"flex",
            alignItems:"center",
            justifyContent:"center",
            flexDirection:"column",
            mt:2
            // mb:2
        }}>
            <Typography variant="body1" sx={{
                fontSize:"13px",
                color:"rgba(91, 170, 100, 1)"
            }}>立即 /</Typography>
            <Typography variant="h2">追蹤我們</Typography>
            <Box id="buttonContainer"sx={{
                backgroundColor:"rgba(244, 245, 246, 1)",
                mt:2,
                paddingRight:2,
                padding:2,
                display:"flex",
                alignItems:"center",
                borderRadius:"999px"
            }}>
                <IconButton onClick={handleFaceBookLink}>
                    {/* <FacebookIcon/> */}
                        <Image
                            src={"/images/FaceBookIcon.png"}
                            alt="Facebook Icon"
                            width={40}
                            height={40}
                            layout="fixed"
                        />
                </IconButton>
                <IconButton onClick={handleFaceBookLink}>
                    {/* <InstagramIcon/> */}
                    <Image
                            src={"/images/InstagramIcon.png"}
                            alt="Facebook Icon"
                            width={40}
                            height={40}
                            layout="fixed"
                        />
                </IconButton>
                <IconButton onClick={handleFaceBookLink}>
                    {/* <YouTubeIcon/> */}
                    <Image
                            src={"/images/YouTubeIcon.png"}
                            alt="Facebook Icon"
                            width={40}
                            height={40}
                            layout="fixed"
                        />
                </IconButton>
                <IconButton>
                    <ArrowRightAltIcon/>
                </IconButton>
                <Typography variant="body1">追蹤我們</Typography>
            </Box>
            {/* <Image
                src={"/images/iPhone14Pro.png"}
                alt="Facebook Icon"
                width={294.54}
                height={521}
                // layout="fixed"
            /> */}
           <Box sx={{
                position: "relative",
                mt: -6 // Adjust this value to control the overlap
            }}>
            <Image
                src="/images/iPhone14Pro.png"
                alt="iphone14pro"
            />
            </Box>
        </Box>
        </>
    )
}

export default FollowUs;