"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const OwnerIcon = createSvgIcon(
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clipPath="url(#clip0_3663_3614)">
<path d="M8.66679 9.71426H5.71443C5.10822 9.71426 4.52684 9.95508 4.09818 10.3837C3.66953 10.8124 3.42871 11.3938 3.42871 12V13.1428M10.2859 5.14283C10.2859 6.4052 9.2625 7.42855 8.00014 7.42855C6.73777 7.42855 5.71443 6.4052 5.71443 5.14283C5.71443 3.88047 6.73777 2.85712 8.00014 2.85712C9.2625 2.85712 10.2859 3.88047 10.2859 5.14283Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M11.9998 12.6667V11.6667M11.9998 10.3333V10.0886M15.3332 11.3333C15.3332 13.1743 13.8408 14.6667 11.9998 14.6667C10.1589 14.6667 8.6665 13.1743 8.6665 11.3333C8.6665 9.49238 10.1589 8 11.9998 8C13.8408 8 15.3332 9.49238 15.3332 11.3333Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3663_3614">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
  ,
  "Owner"
);

export default OwnerIcon;