import { z } from "zod";
import {
  ACCEPTED_IMAGE_TYPES,
  ACCEPTED_VIDEO_TYPES,
  MAX_IMAGE_FILE_SIZE,
  MAX_VIDEO_FILE_SIZE,
  SCHEMA_ERRORS,
} from "@/utils/constants";

export const ImageVideoFileSchema = z
  .instanceof(File)
  .optional()
  .refine(
    (file) =>
      file && file.size > 0
        ? [...ACCEPTED_IMAGE_TYPES, ...ACCEPTED_VIDEO_TYPES].includes(file.type)
        : true,
    SCHEMA_ERRORS.invalidImageVideoType
  )
  .refine((file) => {
    if (!file) return true;

    if (ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      return file.size <= MAX_IMAGE_FILE_SIZE;
    }

    return file.size <= MAX_VIDEO_FILE_SIZE;
  }, SCHEMA_ERRORS.imageFileSizeExceed);
