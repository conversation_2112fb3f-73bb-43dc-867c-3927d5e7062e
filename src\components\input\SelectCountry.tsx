"use client";
import * as React from "react";
import { useTranslations } from "next-intl";
import {
  CountrySelector,
  defaultCountries,
  usePhoneInput,
  FlagImage,
} from "react-international-phone";
import { Typography, Button } from "@mui/material";
import Box from "@mui/material/Box";
import "react-international-phone/style.css";
import InputContainer, { InputContainerProps } from "./InputContainer";

interface Props extends InputContainerProps {
  fullWidth?: boolean;
  defaultCountry?: string;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
}

export default function SelectCountry({
  label,
  description,
  required,
  defaultCountry = "hk",
  value,
  onChange,
  width,
  fullWidth,
  error,
}: Props) {
  const t = useTranslations("error");

  const { country, setCountry } = usePhoneInput({
    defaultCountry,
    value: "",
    countries: defaultCountries,
  });

  React.useEffect(() => {
    setCountry(value);
  }, [setCountry, value]);

  return (
    <InputContainer
      label={label}
      description={description}
      required={required}
      width={width}
      error={error}
    >
      <CountrySelector
        selectedCountry={country.iso2}
        onSelect={(country) => onChange(country.iso2)}
        renderButtonWrapper={({ rootProps }) => (
          <Button
            {...rootProps}
            color="primary"
            variant="outlined"
            sx={{
              borderWidth: 0.5,
              borderColor: "#000",
              width: "100%",
              justifyContent: "flex-start",
            }}
          >
            <Box
              display="flex"
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"flex-start"}
            >
              <FlagImage iso2={country.iso2} />
              <Typography fontSize={"13px"} sx={{ ml: 1, color: "#000" }}>
                {country.name}
              </Typography>
            </Box>
          </Button>
        )}
      />
    </InputContainer>
  );
}
