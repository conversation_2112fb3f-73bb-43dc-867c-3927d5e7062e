"use server";
import { revalidateTag } from "next/cache";
import { GetSaleOrderByIdResponseType, SaleOrderDetailType } from "../_schema/saleOrder.schema";
import { fetchData } from "./api";

const URL = `${process.env.NEXT_PUBLIC_API_BASE}/api/admin/v1/order`; // {}?language={}

export const retrieveOrderById = async (
  orderId: number,
  language: string = "EN"
): Promise<SaleOrderDetailType | undefined> => {
  const requestUrl = `${URL}/${orderId}?language=${language}`;

  const resultResponse = await fetchData<GetSaleOrderByIdResponseType>(requestUrl, {
    next: { revalidate: 0, tags: ["retrieveOrderById"] },
  });

  return resultResponse?.data as SaleOrderDetailType;
};

export const revalidateRetrieveOrderById = () => {
  revalidateTag("retrieveOrderById");
};
