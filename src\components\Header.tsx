"use client";
// import * as React from "react";
// import {
//   Box,
//   Link,
//   IconButton,
//   Drawer,
//   List,
//   ListItem,
//   useMediaQuery,
//   Theme,
//   CircularProgress
// } from "@mui/material";
// import MenuIcon from '@mui/icons-material/Menu';
// import CloseIcon from '@mui/icons-material/Close';
// import Image from "next/image";
// import { COLORS } from "@/styles/colors";
// import AccountMenu from "./buttons/AccountMenu";
// import { useTranslations } from "next-intl";
// import { useRouter, usePathname } from "next/navigation";
// import { useEffect, useMemo, useState } from "react";
// import  LoginAndRegisterSection from "@/components/LoginAndRegisterSection";
// import LanguageSwitcher from "@/components/LanguageSwitcher";
// import { useSession } from "next-auth/react";

// export default function Header() {
//   const t = useTranslations("menu");
//   const [selectedLink, setSelectedLink] = useState(null);
//   const [mobileOpen, setMobileOpen] = useState(false);
//   const [isLoading, setIsLoading] = useState(false);
//   const { data: session , status } = useSession();
//   // const [isLoggedIn, setIsLoggedIn] = useState(false);
//   const [isLoaded, setIsLoaded] = useState(false);
//   const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'));
//   const pathname = usePathname();
//   const router = useRouter();
//   const isLoggedIn = useMemo(() => status === "authenticated", [status]);

//   // useEffect(() => {
//   //   const timer = setTimeout(() => {
//   //     setIsLoaded(true); // 設置為 true，顯示導航項目
//   //   }, 100); // 可以根據需要調整延遲

//   //   return () => clearTimeout(timer); // 清理計時器
//   // }, []);

//   const navItems = [
//     { key: "featured_events", path: "/featured-events" },
//     { key: "virtual_room", path: "/virtual-room" },
//     { key: "about_incutix", path: "/about-incutix" },
//     { key: "faq", path: "/faqs" }
//   ];

//   // 响应式文字样式
//   const textStyles = (isSelected: boolean) => ({
//     color: isSelected ? 'rgba(79, 183, 71, 1)' : 'black',
//     textDecoration: 'none',
//     fontSize: isMobile ? '1rem' : '1.1rem',
//     '&:hover': {
//       color: 'rgba(79, 183, 71, 1)',
//     },
//     '&:focus': {
//       color: 'rgba(79, 183, 71, 1)',
//     },
//   });

//   const handleLinkClick = async (link: any) => {
//     setIsLoading(true);
//     setSelectedLink(link);
//     if (isMobile) setMobileOpen(false); // 移动端点击后关闭菜单

//     // 模擬數據加載過程
//     await new Promise((resolve) => setTimeout(resolve, 2000)); // 這裡可以替換為實際的數據獲取

//     setIsLoading(false);
//     router.push(navItems.find(item => item.key === link)?.path || '/'); // 導航到選定的路徑
//   };

//   const handleDrawerToggle = () => {
//     setMobileOpen(!mobileOpen);
//   };

//   const handleBackHome = () => {
//     router.push('/home');
//     if(isMobile) setMobileOpen(false);
//   };

//   // useEffect(() => {

//   //     if(session){
//   //       setIsLoggedIn(true);
//   //     }

//   //   }, []);

//   // 移动端抽屉菜单内容

//   const renderProfileLogo = () =>{
//     if(status === "loading"){
//       return <CircularProgress />;
//     }else if(status === "authenticated"){
//       return <AccountMenu />;
//     }else if(status === "unauthenticated"){
//       return <LoginAndRegisterSection />;
//     }
//   }

//   const drawerContent = (
//     <Box
//       sx={{
//         width: 250,
//         padding: 2,
//         height: '100%',
//         backgroundColor: COLORS.WHITE
//       }}
//     >
//       <Box sx={{
//         display: 'flex',
//         justifyContent: 'flex-end',
//         mb: 2
//       }}>
//         <IconButton onClick={handleDrawerToggle}>
//           <CloseIcon />
//         </IconButton>
//       </Box>

//       <List>
//         {navItems.map((item) => (
//           <ListItem
//             key={item.key}
//             sx={{
//               justifyContent: 'center',
//               py: 1
//             }}
//           >
//             <Link
//               href={item.path}
//               sx={textStyles(selectedLink === item.key)}
//               onClick={() => handleLinkClick(item.key)}
//             >
//               {t(item.key)}
//             </Link>
//           </ListItem>
//         ))}
//       </List>
//     </Box>
//   );

//   return (
//     <Box
//       component="header"
//       sx={{
//         background: "rgba(252, 252, 253, 1)",
//         height: { xs: 70, md: 90 },
//         paddingX: { xs: 2, md: '27px' },
//       }}
//     >
//       <Box
//         display="flex"
//         justifyContent="space-between"
//         alignItems="center"
//         height="100%"
//       >
//         {/* 左侧Logo */}
//         <Box
//           onClick={handleBackHome}
//           sx={{
//             cursor: "pointer",
//             flexShrink: 0,
//             '& img': {
//               width: { xs: 120, md: 152 },
//               height: 'auto'
//             }
//           }}
//         >
//           <Image
//             src="/images/IncutixLogo.png"
//             height={30}
//             width={152}
//             alt="incutix-logo"
//             priority
//           />
//         </Box>

//       {/* 移动端菜单按钮 */}
//         {isMobile && (
//           <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
//             <IconButton
//               color="inherit"
//               edge="end"
//               onClick={handleDrawerToggle}
//               sx={{ ml: 1 }}
//             >
//               <MenuIcon fontSize="medium" />
//             </IconButton>
//           </Box>
//         )}
//                 {/* 桌面导航 */}
//                 {!isMobile && (
//           <Box
//             sx={{
//               display: 'flex',
//               alignItems: "center",
//               gap: { md: 3, lg: 4 },
//               flexGrow: 1,
//               justifyContent: 'flex-end',
//               maxWidth: 800
//             }}
//           >
//               <>
//                 {navItems.map((item) => (
//                   <Link
//                     key={item.key}
//                     href={item.path}
//                     sx={textStyles(selectedLink === item.key)}
//                     onClick={() => handleLinkClick(item.key)}
//                   >
//                     {t(item.key)}
//                   </Link>
//                 ))}
//                 <LanguageSwitcher/>
//                 {/* {status === "authenticated" ? <AccountMenu /> : <LoginAndRegisterSection/>} */}
//                 {renderProfileLogo()}
//               </>
//           </Box>
//         )}

//         {/* 移动端抽屉菜单 */}
//         <Drawer
//           variant="temporary"
//           open={mobileOpen}
//           onClose={handleDrawerToggle}
//           ModalProps={{
//             keepMounted: true // 更好的移动端性能
//           }}
//           sx={{
//             '& .MuiDrawer-paper': {
//               boxSizing: 'border-box',
//               width: 250
//             }
//           }}
//         >
//           {drawerContent}
//           <LanguageSwitcher/>
//           {renderProfileLogo()}
//         </Drawer>
//       </Box>
//     </Box>
//   );
// }

import React from "react";
import { Box, styled } from "@mui/material";
import Image from "next/image";

const StyledBox = styled(Box)(({ theme }) => ({
  width: "100vw",
  height: "62px",
  border: "1px solid #F4F5F6",
  backgroundColor: "#FFF",
  display: "flex",
  alignItems: "center",
  justifyContent: "left",
  padding: "0 24px",
}));

const Header = () => {
  return (
    <StyledBox>
      <Image src="/images/IncutixLogo.png" height={30} width={152} alt="incutix-logo" priority />
    </StyledBox>
  );
};

export default Header;
