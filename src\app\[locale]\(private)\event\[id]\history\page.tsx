"use client"
import { useTranslations } from 'next-intl';
import { Box, Card, Checkbox, Icon, IconButton, Menu, MenuItem, Skeleton, styled, Tab, Tabs, Typography } from '@mui/material';
import React, { useState, useEffect, useMemo } from 'react';
import { keepPreviousData, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import xior from 'xior';
import { COLORS } from '@/styles/colors';
import { generateDisplayableDate } from '@/utils/date';
import Image from 'next/image';
import PaginationTable from '@/components/PaginationTable';
import { ColumnDef, getCoreRowModel, getExpandedRowModel, useReactTable } from '@tanstack/react-table';
import { ThirdParty } from '../section/page';
import dayjs from 'dayjs';

type TicketHistoryAdjustment = {
    after: number
    amount: number
    before: number
    created_at: number
    updated_at: number
    branch: string
    id: number
    ticket_distribution_id: number
}

type TicketHistoryTable = {
    [date: number|string]: {
        total: number,
        name: string,
        branchCode: string,
        ids: number[]
    }
}

const Container = styled(Box)(({}) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '32px'
}))

interface Props {
  params: { id: string };
}

const ExportHistory = ({ params: { id } }: Props) =>{
    const t = useTranslations('event');
    const tTicket = useTranslations('ticket');
    const tCommon = useTranslations('common');
    const queryClient = useQueryClient();
    const [exportRecord, setExportRecord] = useState<TicketHistoryTable>({})
    const [downloadTarget, setDownloadTarget] = useState();

    const {
        data: eventInfo,
        refetch,
        isLoading
    } = useQuery({
        queryKey: ["event"],
        queryFn: async () => {
            return xior
            .get(`/api/event/${id}`)
        },
        placeholderData: keepPreviousData,
        retry: 3,
    });

    const {
        ticket_setting: ticketSetting = {}
    } = (eventInfo?.data?.data?.event ?? {})

    const {
        ticket_type: ticketType = []
    } = (ticketSetting ?? {})

    useEffect(() => {
        if (ticketType?.length > 0) {
            const parsedExportHistory: TicketHistoryTable = {}
            ticketType.map(({
                ticket_type_distribution: ticketTypeDistribution,
                third_party
            }: {
                ticket_type_distribution: { ticket_distribution_adjustment: TicketHistoryAdjustment[] }[],
                third_party: ThirdParty
            }) => {
                if (!third_party) return;
                const {
                    id,
                    name
                } = third_party

                ticketTypeDistribution.map((distribution) => {
                    const {
                        ticket_distribution_adjustment: adjustment = []
                    } = distribution

                    adjustment.map((record) => {
                        const {
                            created_at: createdAt,
                            amount,
                            branch
                        } = record
                        if (parsedExportHistory[createdAt]) {
                            parsedExportHistory[createdAt] = {
                                ...parsedExportHistory[createdAt],
                                total: parsedExportHistory[createdAt].total + amount,
                                ids: [...parsedExportHistory[createdAt].ids, id]
                            }

                        } else {
                            parsedExportHistory[createdAt] = {
                                total: amount,
                                name,
                                ids: [id],
                                branchCode: branch
                            }
                        }
                    })
                })
            }).filter((item: any) => item)
            setExportRecord(parsedExportHistory)
        }
    }, [ticketType])

    const mutation = useMutation({
        mutationFn: async () => {
            const data = await xior.get(`/api/ticket/export/history/${downloadTarget}`)
            const blob = new Blob([data.data], { type: "text/csv;charset=utf-8;" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", `${downloadTarget}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            return true
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["event"]});
        },
        onSettled: () => {
            setDownloadTarget(undefined)
        }
    });

    useEffect(() => {
        if (downloadTarget) {
            mutation.mutate()
        }
    }, [mutation, downloadTarget])

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                accessorKey: "branch",
                header: tTicket("export_datetime"),
                cell: (data) => (
                    <>{data.row.original.branch}</>
                )
            },
            {
                accessorKey: "name",
                header: tTicket("third_party"),
                cell: (data) => (
                    <>{data.row.original.name}</>
                )
            },
            {
                accessorKey: "total",
                header: tTicket("quantity"),
                cell: (data) => (
                    <>{data.row.original.total}</>
                )
            },
            {
                accessorKey: "ids",
                header: "",
                cell: (data) => (
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "row",
                            gap: "4px",
                            padding: "8.5px 14px",
                            border: `2px solid ${COLORS.GREY_3}`,
                            borderRadius: '999px',
                            justifyContent: 'center'
                        }}
                        onClick={() => setDownloadTarget(data.row.original.branchCode)}
                    >
                        <Image
                            src="/images/icon-download.svg"
                            width={20}
                            height={20}
                            alt="download"
                        />
                        <Typography variant='body2' fontWeight='bold'>
                            {tTicket("redownload")}
                        </Typography>
                    </Box>
                )
            },
        ],
        [tTicket]
    );

    const table = useReactTable({
        data: Object.keys(exportRecord).map((key) => ({
            branch: dayjs.unix(Number(key)).format('YYYY/M/D HH:mm:ss'),
            ...exportRecord[key]
        })),
        columns,
        getSubRows: (row) => {
            return row.info
        },
        getCoreRowModel: getCoreRowModel(),
        manualPagination: true,
    }); 

    const renderTable = () => (
        <Box>
            <PaginationTable
                table={table}
                isLoading={isLoading}
                msg={t("title_empty")}
                skipPagination={true}
            />
        </Box>
    )


    return(
        <Container>
            <Typography variant="h3" fontWeight="bold">
                {tTicket("export_to_third_party_history")}
            </Typography>
            <Box display="flex" flexDirection={"row"} justifyContent={"space-between"}>
                {renderTable()}                
            </Box>
        </Container>
    )
}

export default ExportHistory;