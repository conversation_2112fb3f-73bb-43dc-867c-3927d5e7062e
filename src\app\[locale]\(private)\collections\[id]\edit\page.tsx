"use client";
import LabelValue from "@/components/LabelValue";
import PageHeader from "@/components/PageHeader";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import FileInput from "@/components/input/FileInput";
import TextField from "@/components/input/TextField";
import useFileUpload from "@/hooks/useFileUpload";
import { ICollection } from "@/interface/ICollection";
import { CollectionSchema } from "@/schemas/CollectionSchema";
import { ROUTES } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, CircularProgress, Typography } from "@mui/material";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";
import DeleteCollection from "../../components/DeleteCollection";

interface Props {
  params: { id: string };
}

type FormValue = z.infer<typeof CollectionSchema>;

const EditCollection = ({ params: { id } }: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const router = useRouter();
  const { uploadFile } = useFileUpload();

  const {
    handleSubmit,
    control,
    formState: { isDirty, isSubmitting, errors },
    setError,
    reset,
  } = useForm<FormValue>({
    resolver: zodResolver(CollectionSchema),
    defaultValues: {
      name: "",
      description: "",
      photoUrl: "",
    },
  });

  const { data, isLoading, isLoadingError } = useQuery({
    queryKey: ["collection", id],
    queryFn: () =>
      xior.get<ICollection>(`/api/collections/${id}`).then((res) => res.data),
  });

  useEffect(() => {
    if (data) {
      const { id, ...otherValues } = data;
      reset({ ...otherValues });
    }
  }, [data, reset]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const { photoUrl, ...otherData } = data;
      await xior.put(`/api/collections/${id}`, {
        ...otherData,
        photoUrl: await uploadFile(photoUrl, "collection"),
      });
      queryClient.invalidateQueries({
        queryKey: ["collections"],
      });
      router.push(ROUTES.COLLECTION);
    } catch (e) {
      setError("name", { message: "unknown_error" });
    }
  };

  useEffect(() => {
    if (isLoadingError) router.push(ROUTES.COLLECTION);
  }, [isLoadingError, router]);

  if (isLoading) {
    return (
      <Box
        sx={{ height: "100%" }}
        display={"flex"}
        justifyContent="center"
        alignItems={"center"}
      >
        <CircularProgress color="primary" />
      </Box>
    );
  }

  return (
    <Box
      sx={{ height: "100%" }}
      display={"flex"}
      flexDirection={"column"}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
    >
      <PageHeader
        title={[
          t("collection.label_title"),
          t("collection.title_edit_collection"),
        ]}
      >
        <>
          {data && <DeleteCollection collection={data} />}
          <CancelButton
            onAction={() => router.push(`${ROUTES.COLLECTION}`)}
          />
          <SaveButton disabled={isSubmitting || !isDirty} />
        </>
      </PageHeader>
      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
        maxWidth={450}
      >
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              disabled={isSubmitting}
              label={t("collection.label_collection_name")}
              error={errors?.name?.message}
            />
          )}
        />
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("collection.label_description")}
              error={errors?.description?.message}
              multiline
              rows={8}
            />
          )}
        />
        <Controller
          name="photoUrl"
          control={control}
          render={({ field }) => (
            <>
              <Typography fontSize={14}>
                {t("collection.label_thumbnail")}
              </Typography>
              <Typography fontSize={12} color={'grey'}>
                <i>
                  {
                    t("file_upload.image_metadata").split("\n").map((item: string) => (
                      <>{item}<br /></>
                    ))
                  }
                </i>
              </Typography>
              <FileInput
                value={field.value}
                onChange={field.onChange}
                metadata={t("file_upload.thumbnail_upload")}
                disabled={isSubmitting}
                error={errors.photoUrl?.message}
                type="image"
              />
            </>

          )}
        />
      </Box>
    </Box>
  );
};

export default EditCollection;
