"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const ProductCategoryIcon = createSvgIcon(
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="2" y="2" width="4.66667" height="4.66667" stroke="currentColor" strokeWidth="1.5" strokeLinejoin="round"/>
<rect x="2" y="9.33331" width="4.66667" height="4.66667" stroke="currentColor" strokeWidth="1.5" strokeLinejoin="round"/>
<rect x="9.3335" y="2" width="4.66667" height="4.66667" stroke="currentColor" strokeWidth="1.5" strokeLinejoin="round"/>
<rect x="9.3335" y="9.33331" width="4.66667" height="4.66667" stroke="currentColor" strokeWidth="1.5" strokeLinejoin="round"/>
</svg>

  ,
  "ProductCategory"
);

export default ProductCategoryIcon;