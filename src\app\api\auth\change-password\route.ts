import { authorizedPost, getAuthHeaders, handleApiError } from "@/utils/api";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";
import xior from "xior";

async function POST(req: NextRequest) {
  try {
    const data = await authorizedPost(
      "/auth/change-password",
      await getAuthHeaders(req),
      await req.json()
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { POST };
