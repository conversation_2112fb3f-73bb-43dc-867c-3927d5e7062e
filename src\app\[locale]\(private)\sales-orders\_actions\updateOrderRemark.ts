import {
  updateOrderRemarkRequestSchema,
  UpdateOrderRemarkRequestType,
} from "../_schema/saleOrder.schema";
import { fetchData } from "./api";

const URL = `${process.env.NEXT_PUBLIC_API_BASE}/api/admin/v1/order/remark`;

export const updateOrderRemark = async (data: any) => {
  const requestData = updateOrderRemarkRequestSchema.parse(data);

  await fetchData<UpdateOrderRemarkRequestType>(URL, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(requestData),
  });

  return true;
};
