import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import FileInput from "@/components/input/FileInput";
import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import TextField from "@/components/input/TextField";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { ProductDetailDto, ProductImage } from '@/interface/IProductDetailDto';
import { IconButton } from '@mui/material';
import Image from "next/image";
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import useFileUpload from '@/hooks/useFileUpload';


const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};

type Props = {
  updateProductDto: ProductDetailDto
  handleUpdateProductDtoChange:(updateProductDto:ProductDetailDto)=> void
}

const EditProductImageTable = ({ updateProductDto, handleUpdateProductDtoChange}: Props) => {
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const t = useTranslations();
  const [error, setError] = useState<string | undefined>(undefined);
  const [image, setImage] = useState<string | undefined>(undefined);
  const [productName, setProductName] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [rows, setRows] = useState<ProductImage[]>(updateProductDto.productImage); // 使用 ProductImage 作為行數據類型
  const isSmall = false;
  const { uploadFile } = useFileUpload();
  const [productImageUrl, setProductImageUrl] = useState<string>("");


  const removeFile = (rowToRemove: ProductImage) => {
    const updatedRows = rows.filter(row => row !== rowToRemove);
    setRows(updatedRows);

    handleUpdateProductDtoChange({
      ...updateProductDto,
      productImage: updatedRows.map(row => ({
        name: row.name,
        url: row.url,
        resourceType: 'image',
      })),
    });
  };

    const handleRemoveRow = (rowToRemove: ProductImage) => {
    removeFile(rowToRemove);
  };

  const handleProductNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setProductName(event.target.value);
  };

  const handleSave = () => {
    if (productName && image) {
      setIsSubmitting(true);

      console.log("productImageUrl first",productImageUrl)

      const resourceType = productImageUrl.endsWith('.mp4') || productImageUrl.endsWith('.mov') 
      ? 'video' 
      : 'image';
      
      console.log("media type",resourceType)
      console.log("productImageUrl",productImageUrl)

      // 創建新行數據
      const newRow: ProductImage = {
        name: productName,
        url: productImageUrl,
        resourceType: resourceType, // 假設這是圖片類型
      };


      const updatedRows = [...rows, newRow];

      setRows(updatedRows);

      handleUpdateProductDtoChange({
        ...updateProductDto,
        productImage: updatedRows.map(row => ({
          name: row.name,
          url: row.url,
          resourceType: row.resourceType,
        })),
      });


      // 重置輸入字段
      setProductName('');
      setImage(undefined);
      setIsSubmitting(false);
      handleClose();
    } else {
      setError("Please fill in all fields.");
    }
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      const reader = new FileReader();
      reader.onload = async () => {
        const fileUrl = reader.result as string;
        setImage(fileUrl); 
        setProductName(selectedFile.name)
        try {
          // 直接在文件选择后上传
          const thumbnailUrl = await uploadFile(selectedFile, "productThumbnail");
          console.log("Uploaded thumbnail URL:", thumbnailUrl);
          setProductImageUrl(thumbnailUrl);
        } catch (error) {
          console.error("Upload failed:", error);
        }
      };

      reader.readAsDataURL(selectedFile);
    }
  };

  return (
    <div>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell>File Name</TableCell>
              <TableCell>Picture</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map((row) => (
              <TableRow key={row.name}>
                <TableCell>{row.name}</TableCell>
                <TableCell>
                {row.resourceType === 'video' ? (
                  <Image 
                    src="https://s3.ap-southeast-1.amazonaws.com/portal-hkgt.fun-verse.io/productThumbnail/tmp/e29833ba2f5bc8906fca71835f0b3d7b0c3b8dfb.png" 
                    alt={row.name}
                    style={{ width: '100px', height: 'auto' }}
                  />
                ) : (
                  <Image src={row.url} alt={row.name} style={{ width: '100px', height: 'auto' }} />
                )}
                </TableCell>
                <TableCell>
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={() => handleRemoveRow(row)} // 点击时移除行
                  >
                    x
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Button onClick={handleOpen}>Add New +</Button>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("product.add_product_image")}
            </Typography>
          </Box>
          {/* <TextField
            required
            value={productName}
            onChange={handleProductNameChange}
            label={t("product.label_name")}
          /> */}
          {/* <FileInput
            metadata={t("file_upload.thumbnail_multimedia_upload")}
            onChange={handleImageChange}
            value={image}
            error={error}
            type="image_video"
            multiple={false}
            name="thumbnail"
          /> */}
          {
            image ? (
              <Box
                display="flex"
                flexDirection={"column"}
                alignItems={"center"}
                width="100%"
                mb={1.5}
                sx={{
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  height: '300px',
                  overflow: 'hidden',
                }}
              >
                {productImageUrl.endsWith('.mp4') || productImageUrl.endsWith('.mov') ? (
                  <video
                    controls
                    style={{ width: '100%', height: '100%' }}
                  >
                    <source src={productImageUrl} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                ) : (
                  <Box
                    sx={{
                      backgroundImage: `url(${image})!important`,
                      width: '100%',
                      height: '100%',
                    }}
                  />
                )}
                <IconButton
                  aria-label="delete"
                  size="small"
                  sx={{
                    background: "#ff3b36",
                    height: 18,
                    width: 18,
                    position: "absolute",
                    right: isSmall ? -8 : 20,
                  }}
                  onClick={() => {
                    setImage(undefined); // Reset the preview
                    setProductImageUrl(""); // Reset the uploaded URL
                  }}
                >
                  <CloseRoundedIcon sx={{ fill: "white", height: 16, width: 16 }} />
                </IconButton>
              </Box>
            ) : (
                  <Box
                  display="flex"
                  flexDirection={"column"}
                  alignItems={"center"}
                  width="100%"
                  mb={1.5}
                >
              <div>
              <label htmlFor="file-upload" style={{ cursor: 'pointer' }}>
                  <Image
                      height={36}
                      width={36}
                      src={image ? (image as string) : "/images/icon-upload.png"}
                      alt="Upload image"
                  />
              </label>
              <input
                  id="file-upload"
                  type="file"
                  onChange={handleImageChange}
                  style={{ display: 'none' }} // 隐藏文件输入
              />
          </div>
          <Typography>Upload your Thumbnail image/video</Typography>
          </Box>
            )
          }
          <CancelButton
            disabled={isSubmitting}
            onAction={handleClose}
          />
          <SaveButton disabled={isSubmitting} onAction={handleSave} />
        </Box>
      </Modal>
    </div>
  );
};

export default EditProductImageTable;