"use client"
import * as React from 'react';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import EditButton from '@/components/buttons/EditButton';
import SearchIcon from '@mui/icons-material/Search';
import {IconButton, Typography} from "@mui/material";
import { IInventory } from "@/interface/IInventory";
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';

type Props ={
  getInventoryDto:IInventory
}

const InventoryRow = ({getInventoryDto}:Props) =>{

  const router = useRouter();

  const handleQueryClick = () => {
    router.push(`/products/inventory/${getInventoryDto.id}`)
  }
  
  const updatedAtformattedDate = format(new Date(getInventoryDto.updatedAt * 1000), "yyyy-MM-dd HH:mm:ss");

  const renderProductType = () =>{
    if(getInventoryDto.productType === 1){
      return(
        <>
        <Typography>Physical</Typography>
        </>
      )
    }else if(getInventoryDto.productType === 2){
      return(
        <>
        <Typography>Digital</Typography>
        </>
      )
    }
  }

  const renderProductTypeCommited = () =>{
    if(getInventoryDto.productType === 1){
      return(
        <>
        {getInventoryDto.committed? getInventoryDto.currentCommitted : "-"}
        </>
      )
    }else if(getInventoryDto.productType === 2){
      return(
        <>
        <Typography>-</Typography>
        </>
      )
    }else{
      return(
        <>
        </>
      )
    }
  }

  const handleProductDetailsClick = () =>{
    router.push(`/products/product-list/${getInventoryDto.productId}/edit`)
  }

    return(
        <TableRow >
        <TableCell>
          <EditButton
          
          onClick={handleQueryClick}
          />
        </TableCell>
        <TableCell>
          <IconButton sx={{
                background: "#24378C",
                color:"white",
                '&:hover': {
                  background: "#24378C", // 懸停時的背景顏色保持不變
              },
              }}
        
              >
        <SearchIcon onClick={handleProductDetailsClick}/>
        </IconButton>
        </TableCell>
        <TableCell>
        {/* eslint-disable-next-line @next/next/no-img-element, jsx-a11y/alt-text */}
        <img src={getInventoryDto.thumbnail}
                width="100px"
                />
        </TableCell>
        <TableCell>{getInventoryDto.name}</TableCell>
        <TableCell>{getInventoryDto.sku? getInventoryDto.sku : "-"}</TableCell>
        <TableCell>{renderProductType()}</TableCell>
        <TableCell>{getInventoryDto.unavailable? getInventoryDto.unavailable : "-"}</TableCell>
        <TableCell>{renderProductTypeCommited()}</TableCell>
        <TableCell>{getInventoryDto.currentQuantity? getInventoryDto.currentQuantity: getInventoryDto.quantity}</TableCell>
        <TableCell>{getInventoryDto.committed? getInventoryDto.committed: "-"}</TableCell>
        <TableCell>{getInventoryDto.quantity? getInventoryDto.quantity: "-"}</TableCell>
        <TableCell>{updatedAtformattedDate}</TableCell>
      </TableRow>
    )
}

export default InventoryRow