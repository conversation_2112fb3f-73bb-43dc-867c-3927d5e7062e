import {
    Typography,
  } from "@mui/material";
import { useTranslations } from "next-intl";
import ImagesSlider from '@/components/ImagesSlider';
import FeaturedEvent from '@/components/featured-events/page';

const FeaturedEvents = () =>{
    // const t = useTranslations("featured_events");
    const images = [
        'https://s3.ap-southeast-1.amazonaws.com/portal-dev.fun-verse.io/productThumbnail/tmp/51656209e3952cbd393753000c3713b7a6cc9386.jpeg',
        'https://s3.ap-southeast-1.amazonaws.com/portal-dev.fun-verse.io/productThumbnail/tmp/1e0fdb9db6d47e726d9ea69a7a31476892c65c2c.jpeg',
        'https://assets.incutix.com/events/one_piece_tour_thailand/OPTH_KV_Horizontal.jpg',
        'https://assets.incutix.com/events/JuJuT<PERSON>_<PERSON><PERSON>_Exhibition_my/jjk_my_kv_20240813.jpg',
      ];

    return(
        <>
        <ImagesSlider
            images={images}
            interval={5000} 
            height="400px" 
            blurIntensity={100} 
        />
        <FeaturedEvent/>
        </>
    )
}

export default FeaturedEvents;