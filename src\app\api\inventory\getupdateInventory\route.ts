import {
    getAuthHeaders,
    authorizedPut,
    handleApiError,
  } from "@/utils/api";
import { NextRequest , NextResponse} from "next/server";


async function PUT(req: NextRequest) {

  let emptyBody;

  try {
    emptyBody = await req.json();
  } catch (error) {
    return NextResponse.json({ message: 'Invalid JSON' }, { status: 400 });
  }

  try {
    const data = await authorizedPut(
      '/inventory/getupdateInventory',
      await getAuthHeaders(req),
      emptyBody
    );

    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { PUT };

