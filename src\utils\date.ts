import dayjs from "dayjs"

export const generateDisplayableDate = (unixTimestamp: number, format?: string) => {
    return dayjs.unix(unixTimestamp).format(format || 'YYYY-MM-DD')
}

export const generateDisplayableDateTime = (unixTimestamp: number) => {
    return dayjs.unix(unixTimestamp).format('YYYY-MM-DD HH:mm A')
}

const splitTime = (timeInSec: number, requiredPad?: boolean) => {
    const day = Math.floor(timeInSec / 60 / 60 / 24)
    const hour = Math.floor((timeInSec - (day * 60 * 60 * 24)) / 60 / 60)
    const min = Math.floor((timeInSec - (day * 60 * 60 * 24) - (hour * 60 * 60 )) / 60)
    const sec = Math.floor(timeInSec - (day * 60 * 60 * 24) - (hour * 60 * 60 ) - (min * 60))

    return {
        day: requiredPad? `${day}`.padStart(2, '0'): day,
        hour: requiredPad? `${hour}`.padStart(2, '0'): hour,
        min: requiredPad? `${min}`.padStart(2, '0'): min,
        sec: requiredPad? `${sec}`.padStart(2, '0'): sec,
    }
}

export const generateDisplayableTime = (timeInSec: number, requiredPad?: boolean) => {
    return splitTime(timeInSec, requiredPad)
}

export const generateDisplayableDuration = (timeInSec: number) => {
    if (!timeInSec) return;
    const {
        day,
        hour,
        min,
        sec
    } = splitTime(timeInSec)
    let component = []
    if (day) component.push(`${day}d`)
    if (hour) component.push(`${hour}h`)
    if (min) component.push(`${min}m`)
    if (sec) component.push(`${sec}s`)
    return component.join(" ")
}

export const generateDisplayDateDiff = (unixTimestamp: number) => {
    const targetDate = dayjs.unix(unixTimestamp)
    const today = dayjs()
    return targetDate.diff(today, 'days')
}

const DAY_DICT: { [x: string|number]: string } = {
    1: 'Monday',
    2: 'Tuesday',
    3: 'Wednesday',
    4: 'Thursday',
    5: 'Friday',
    6: 'Saturday',
    7: 'Sunday'
}

export const getDay = (day: number) => {
    return DAY_DICT[day]
}