import { NextAuthOptions } from "next-auth";
import xior from "xior";
import { jwtDecode } from "jwt-decode";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";

export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: "/login",
    // signOut: "/auth/signout",
    // error: "/auth/error", // Error code passed in query string as ?error=
    // verifyRequest: "/auth/verify-request", // (used for check email message)
    // newUser: "/auth/new-user", // New users will be directed here on first sign in (leave the property out if not of interest)
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
        type: { lable:"type", type: "string" }
      },
      async authorize(credentials, req) {
        if (credentials?.type === "google") {
          return credentials
        }
        const res = await xior.post(
          `${process.env.NEXT_PUBLIC_API_BASE}/auth/login`,
          credentials
        );

        const user = res.data;
        const userRole = (jwtDecode(user.accessToken) as any).role;
        if (userRole !== "visitor" && userRole !== "creator") {
          throw new Response("Unauthorized", { status: 401 });
        }
        // if ((jwtDecode(user.accessToken) as any).role !== "visitor") {
        //   throw new Response("Unathorized", { status: 401 });
        // }

        // If no error and we have user data, return it
        if (user) {
          return { email: credentials?.email, ...user };
        }

        // Return null if user data could not be retrieved
        return null;
      },
    }),
  ],
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        return {
          email: user.email,
          accessToken: user.accessToken,
          refreshToken: user.refreshToken,
          expiredAt: jwtDecode(user.accessToken).exp!,
        };
      }

      return token;
    },
    session: async ({ session, token }) => {
      session.user = { email: token.email };
      session.accessToken = token.accessToken;
      return session;
    },
  },
};
