"use client"
import { useTranslations } from 'next-intl';
import { Box, Card, Checkbox, Icon, IconButton, Skeleton, styled, Tab, Tabs, Typography } from '@mui/material';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { generalPadding } from '@/styles/theme';
import { COLORS } from '@/styles/colors';
import { generateDisplayableDate, generateDisplayableDateTime, generateDisplayableDuration, generateDisplayableTime, getDay } from '@/utils/date';
import Image from 'next/image';
import { ColumnDef, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import PaginationTable from '@/components/PaginationTable';
import EditTicketSetting from './EditTicketSetting';
import EditTicketVariation from './EditTicketVariation';
import AddTicketVariation from './AddTicketVariation';
import EditTicketSection from './EditTicketSection';
import _ from 'lodash';
import AddTicketType from './AddTicketType';
import EditTicketType from './EditTicketType';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import xior from 'xior';
import { LANGUAGE_CODE } from '@/utils/constants';
import { EventTranslation, extractTranslationBody, TicketTranslation } from '@/utils/translation';

interface Props {
  eventInfo?: any,
  isLoading: boolean;
  isEdit: boolean;
  id: number;
  language: LANGUAGE_CODE;
}

export enum PRODUCT_TYPE_STATUS {
    ACTIVE='ACTIVE',
    INACTIVE='INACTIVE'
}

const TicketTab = ({ id, eventInfo, isLoading, language }: Props) =>{
    const t = useTranslations('event');
    const tTicket = useTranslations('ticket');
    const tCommon = useTranslations('common');
    const [selectedType, setSelectedType] = useState<number[]>([])
    const queryClient = useQueryClient();

    const {
        event_media: eventMedia = [],
        event_qaa: eventQAA = [],
        event_setting: eventSetting = {},
        event_terms: eventTerms = [],
        parent_event: parentEvent = {},
        ticket_setting: ticketSetting = {},
        products = [],
        ...baseInfo
    } = (eventInfo?.data?.data?.event ?? {})

    const {
        ticket_section: ticketSection = [],
        ticket_variation: ticketVariation = [],
        ticket_type: ticketType = []
    } = (ticketSetting ?? {})

    const mutation = useMutation({
        mutationFn:() => {
            return xior.put('/api/ticket/type/toggle', {
                ids: selectedType
            })
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["event"]});
            setSelectedType([])
        },
    })

    // ticket section
    const renderTicketDetail = () => (
        <Card sx={{ display: 'flex', flexDirection: 'column', width: '100%', padding: '20px', gap: '20px' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between'}}>
                <Typography variant='body1' fontWeight={'bold'}>
                    {tTicket("ticket_detail")}
                </Typography>
                <EditTicketSetting
                    id={ticketSetting?.id}
                    eventId={baseInfo.id}
                    sale_start_datetime={ticketSetting?.sale_start_datetime}
                    sale_end_datetime={ticketSetting?.sale_end_datetime}
                    currency={ticketSetting?.currency}
                    sale_time_thershold={ticketSetting?.sale_time_thershold}
                />
            </Box>
            
            <Box display={'flex'} flexDirection={'row'}>
                {
                    ticketSetting?.currency && (
                        <Box flex={1}>
                            <Typography variant="body2" color={COLORS.GREY_6}>{tTicket("currency")}</Typography>
                            <Typography variant="body2">{ticketSetting?.currency}</Typography>
                        </Box>
                    )
                }
                
                {
                    ticketSetting?.sale_start_datetime && ticketSetting?.sale_end_datetime && (<Box flex={1}>
                        <Typography variant="body2" color={COLORS.GREY_6}>{tTicket("ticket_sale_period")}</Typography>
                        <Typography variant="body2">
                            {
                                [ticketSetting?.sale_start_datetime, ticketSetting?.sale_end_datetime]
                                .map((unix) => generateDisplayableDateTime(unix))
                                .join(" - ")
                            }
                        </Typography>
                    </Box>)
                }
                
            </Box>
            {
                ticketSetting?.sale_time_thershold && (
                    <Box>
                        <Typography variant="body2" color={COLORS.GREY_6}>{tTicket("stop_selling_ticket")}</Typography>
                        <Typography variant="body2">
                            { generateDisplayableDuration(ticketSetting?.sale_time_thershold) }
                        </Typography>
                    </Box>
                )
            }
        </Card>
    )

    const renderSectionTimeslot = () => {
        const slotObject: {[x: string]: {
            day?: number[],
            date?: number[],
            startTime: number,
            endTime: number,
            quantity: number
        }[]} = {}

        ticketSection.forEach((section: {
            day?: number,
            date?: number,
            start_time: number,
            end_time: number,
            quantity: number,
            seq: number
        }) => {
            const {
                day,
                date,
                start_time: startTime,
                end_time: endTime,
                quantity,
                seq
            } = section

            const baseInfo = {
                startTime,
                endTime,
                quantity
            }
            if (slotObject[seq]) {
                const foundIdx = slotObject[seq].findIndex((item) => item.startTime === startTime && item.endTime === endTime)
                if (foundIdx > -1) {
                    slotObject[seq][foundIdx].quantity = slotObject[seq][foundIdx].quantity + quantity
                    if (day && !slotObject[seq][foundIdx].day?.includes(day)) slotObject[seq][foundIdx].day?.push(day)
                    if (date && !slotObject[seq][foundIdx].date?.includes(date)) slotObject[seq][foundIdx].day?.push(date)
                } else {
                    slotObject[seq].push({
                        ...baseInfo,
                        ...( day? {day: [day]} : {}),
                        ...( date? {date: [date]} : {}),
                    })
                }
                
            } else {
                slotObject[seq] = [{
                    ...baseInfo,
                    ...( day? {day: [day]} : {}),
                    ...( date? {date: [date]} : {}),
                }]
            }
        })

        return (
            <Box display={"flex"} flexDirection={"column"} gap={"8px"}>
                {
                    Object.values(slotObject).map((item, idx) => (
                        <Box
                            key={`slot_${idx}`}
                            display={'flex'}
                            flexDirection={'column'}
                            bgcolor={COLORS.GREY_3}
                            padding={"20px"}
                            gap={"20px"}
                            borderRadius={"8px"}
                        >
                            <Box>
                                <Typography variant='body2' color={COLORS.GREY_6}>
                                    {tTicket("available_day")}
                                </Typography>
                                <Typography variant='body2'>
                                    {
                                        item?.[0]?.day && 
                                        _.uniq(item[0].day).map((val) => getDay(val)).join(", ")
                                    }
                                    {
                                        item?.[0]?.date && 
                                        _.uniq(item[0].date).map((val) => generateDisplayableDate(val)).join(", ")
                                    }
                                </Typography>
                            </Box>
                            
                            <Box>
                                <Typography variant='body2' color={COLORS.GREY_6}>
                                    {tTicket("time_slot")}
                                </Typography>
                                <Typography variant='body2'>
                                    {
                                        item.map(({ startTime, endTime, quantity}: {
                                            startTime: number,
                                            endTime: number,
                                            quantity: number
                                        }) => {
                                            const {
                                                hour: startH,
                                                min: startM
                                            } = generateDisplayableTime(startTime, true)
                                            const {
                                                hour: endH,
                                                min: endM
                                            } = generateDisplayableTime(endTime, true)

                                            const startUnit = Number(startH) > 12? 'pm': 'am'
                                            const endUnit = Number(endH) > 12? 'pm': 'am'

                                            return `${startH}:${startM}${startUnit} - \
                                            ${endH}:${endM}${endUnit} (${quantity})`
                                        }).filter(item => item).join(", ")
                                    }
                                </Typography>
                            </Box>
                        </Box>
                    ))
                }
            </Box>
        )
    }

    const renderSectionDetail = () => (
        <Card sx={{ display: 'flex', flexDirection: 'column', width: '100%', padding: '20px', gap: '16px' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between'}}>
                <Box>
                    <Typography variant='body1' fontWeight={'bold'}>
                        {tTicket("section_and_quantity")}
                    </Typography>
                    <Typography variant='body3' fontWeight={'400'} color={COLORS.GREY_6}>
                        {tTicket("max_booking")}: {
                            ticketSection.length > 0?
                            ticketSection.reduce((acc: number, cur: { quantity: number}) => acc + cur.quantity ,0)
                            : 0
                        }
                    </Typography>
                </Box>
                <EditTicketSection id={id} />
            </Box>
            <Box display={'flex'} flexDirection={'column'} gap={'8px'}>
                    {
                        renderSectionTimeslot()
                    }
            </Box>
        </Card>
    )

    const renderVariation = () => (
        <Card
            sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                padding: '20px',
                gap: '16px'
            }}
        >   
            <Box sx={{ display: 'flex', justifyContent: 'space-between'}}>
                <Typography variant='body1' fontWeight={'bold'}>
                    {tTicket("variation")}
                </Typography>
                <AddTicketVariation language={language} id={ticketSetting?.id} />
            </Box>
            {
                ticketVariation.map(({ 
                    id,
                    name,
                    translation,
                    ticket_variation_option: variationOption
                }: {
                    id: number,
                    name: string,
                    translation: {
                        translation: {
                            content: string,
                            fields: TicketTranslation,
                            language_code: LANGUAGE_CODE
                        }
                    }[],
                    ticket_variation_option: {
                        id: number,
                        value: string,
                        translation: {
                            translation: {
                                content: string,
                                fields: TicketTranslation,
                                language_code: LANGUAGE_CODE
                            }
                        }[]
                    }[]
                }, idx: number) => (
                    <Box
                        key={`variation_${idx}`}
                        sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            gap: '20px',
                            padding: generalPadding,
                            borderRadius: '8px'
                        }}
                        bgcolor={COLORS.GREY_3}
                    >
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '4px'
                            }}
                        >
                            <Typography variant='body2' fontWeight={'400'} color={COLORS.GREY_6}>
                                {tTicket("variation_label")}
                            </Typography>
                            <Typography variant='body2' fontWeight={'400'}>
                                {extractTranslationBody(language, EventTranslation.NAME, translation) || name}
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '4px'
                            }}
                        >
                            <Typography variant='body2' fontWeight={'400'} color={COLORS.GREY_6}>
                                {tTicket("values")}
                            </Typography>
                            <Typography variant='body2' fontWeight={'400'}>
                                {
                                    variationOption.map(
                                        (option) => 
                                            extractTranslationBody(language, EventTranslation.VALUE, option.translation)
                                            || option.value
                                    ).join(", ")
                                }
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: '20px',
                                paddingLeft: '20px',
                                paddingRight: '20px',
                                marginLeft: 'auto'
                            }}
                        >
                            <EditTicketVariation
                                id={id}
                                name={extractTranslationBody(language, EventTranslation.NAME, translation) || name}
                                language={language}
                                ticket_variation_option={variationOption}
                            />
                            <Box>
                                <Image
                                    src={'/images/icon-option.svg'}
                                    width={24}
                                    height={24}
                                    alt={'option'}
                                />
                            </Box>
                        </Box>
                    </Box>
                ))
            }
        </Card>
    )

    const selectType = useCallback((event: any) => {
        const {
            value,
            checked
        } = event.target

        if (selectedType.includes(Number(value))) {
            setSelectedType(selectedType.filter((id) => id !== Number(value)))
        } else {
            setSelectedType([...selectedType, Number(value)])
        }
    }, [selectedType])

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                accessorKey: "id",
                header: "",
                cell: (data) => (
                    <Checkbox
                        value={data.row.original.id}
                        aria-label={`event-id-${data.row.original.id}-actions`}
                        onChange={selectType}
                        checked={selectedType.includes(data.row.original.id)}
                    />
                ),
            },
            {
                accessorKey: "name",
                header: tTicket("ticket_name"),
                cell: (data) => (
                    <>
                        {
                            extractTranslationBody(language, TicketTranslation.NAME, data.row.original.translation) ||
                            data.row.original.name
                        }
                    </>
            
                ),
            },
            {
                accessorKey: "description",
                header: tTicket("description"),
                cell: (data) => (
                    <>
                        {
                            extractTranslationBody(language, TicketTranslation.DESCRIPTIONG, data.row.original.translation) ||
                            data.row.original.description
                        }
                    </>
                ),
            },
            {
                accessorKey: "price",
                header: tTicket("price"),
                cell: (data) => {
                    const displayableTxt = []
                    if (!data.row.original.price) return "-"
                    if (ticketSetting?.currency) displayableTxt.push(ticketSetting?.currency)
                    if (data.row.original.price) displayableTxt.push(data.row.original.price)
                    if (data.row.original.priceUnit) displayableTxt.push(data.row.original.price_unit)
                    return displayableTxt.join("")
                }
            },
            {
                accessorKey: "variation",
                header: tTicket("variation"),
                cell: (data) => {
                    return data.row.original?.ticket_type_to_variation.length || ""
                }
            },
            {
                accessorKey: "status",
                header: tTicket("status"),
                cell: (data) => {
                    if (!data.row.original.status) return;
                    const {
                        status
                    } = data.row.original
                    return (
                        <Box
                            padding="3px 10px 5px 10px"
                            borderRadius="999px"
                            bgcolor={
                                status === PRODUCT_TYPE_STATUS.ACTIVE?
                                COLORS.PRIMARY_3: COLORS.GREY_3
                            }
                        >
                            <Typography
                                variant='body2'
                                fontWeight='bold'
                                color={
                                    status === PRODUCT_TYPE_STATUS.ACTIVE?
                                    COLORS.PRIMARY_1: COLORS.GREY_6
                                }
                            >
                                {status}
                            </Typography>
                        </Box>
                    )
                }
            },
            {
                accessorKey: "pre_sale_start_time",
                header: tTicket("pre_sale"),
                cell: (data) => {
                    if (data.row.original.pre_sale_start_time) {
                        return (
                            <Image
                                src="/images/icon-true.svg"
                                width={24}
                                height={24}
                                alt="have"
                            />
                        )
                    }
                }
            },
            {
                accessorKey: "id",
                header: () => null,
                cell: (data) => {
                    if (data.row.original.id) {
                        return (
                            <EditTicketType
                                id={data.row.original.id}
                                language={language}
                                info={data.row.original}
                                day={_.uniq(ticketSection.map((section: { day: number| undefined }) => section.day))}
                                date={_.uniq(ticketSection.map((section: { date: number| undefined }) => section.date))}
                                variation={
                                    ticketVariation.map(({name, id}: { name: string, id: number }) => ({ label: name, value: id }))
                                }
                                products={products}
                            />
                        )
                    }
                }
            }
        ],
        [
            selectedType,
            language,
            products,
            selectType,
            tTicket,
            ticketSection,
            ticketSetting,
            ticketVariation
        ]
    );
    const defaultData = useMemo<any[]>(() => [], []);
    const table = useReactTable({
        data: ticketType ?? defaultData,
        columns,
        getCoreRowModel: getCoreRowModel(),
        manualPagination: true,
    }); 

    const toggleTicketTypeStatus = () => {
        mutation.mutate()
    }

    const renderButtonGroup = () => (
        <Box
            sx={{
                display: 'flex',
                gap: '12px'
            }}
        >
            <Box
                sx={{ padding: "15px 20px", border: `2px solid ${COLORS.GREY_3}`, borderRadius: '999px'}}
                display={selectedType.length > 0 ? undefined: 'none'}
                onClick={() => setSelectedType([])}
            >
                <Typography variant='body2' fontWeight='700'>
                    {`${tTicket("selected")} ${selectedType.length}`}
                </Typography>
            </Box>
            <Box
                sx={{ padding: "15px 20px", border: `2px solid ${COLORS.GREY_3}`, borderRadius: '999px'}}
                onClick={toggleTicketTypeStatus}
                display={
                    selectedType.length > 0 ? undefined: 'none'
                }
            >
                <Typography variant='body2' fontWeight='700'>
                    {tTicket("toggle")}
                </Typography>
            </Box>
        </Box>
    )

    const renderTicketType = () => (
        <Card
            sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                padding: '20px',
                gap: '16px'
            }}
        >
            <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
                <Typography variant='body1' fontWeight={'bold'}>
                    {tTicket("ticket_type")}
                </Typography>
                <AddTicketType
                    id={ticketSetting?.id}
                    day={_.uniq(ticketSection.map((section: { day: number| undefined }) => section.day))}
                    date={_.uniq(ticketSection.map((section: { date: number| undefined }) => section.date))}
                    variation={
                        ticketVariation.map(({name, id}: { name: string, id: number }) => ({ label: name, value: id }))
                    }
                    products={products}
                    language={language}
                />
            </Box>
            
            <Box>
                <PaginationTable
                    table={table}
                    isLoading={isLoading}
                    msg={t("title_empty")}
                />
            </Box>

            {
                renderButtonGroup()
            }
        </Card>
    )
    //

    return(
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                paddingRight: '32px',
                gap: '32px',
                height: 'fit-content'
            }}
        >   
            {renderTicketDetail()}
            {renderSectionDetail()}
            {renderVariation()}
            {renderTicketType()}
        </Box>
    )
}

export default TicketTab;