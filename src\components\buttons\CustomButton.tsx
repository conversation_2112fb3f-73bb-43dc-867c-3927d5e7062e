import React from 'react';
import { Button, styled } from '@mui/material';
import { useTranslations } from 'next-intl';

const CustomButton = styled(Button)({
  backgroundColor: 'rgba(79, 183, 71, 1)',
  color: '#fff',
  '&:hover': {
    backgroundColor: 'rgba(79, 183, 71, 0.8)',
  },
  borderRadius: '999px'
});

interface MyButtonProps {
  label: string;
  namespace?: string;
  disabled?:boolean;
  icon?: React.ReactNode;
  onClick?: (event?:any) => void;
  buttonStyle?: {[x: string]: any}
}

const MyButton: React.FC<MyButtonProps> = ({ label,disabled, namespace ,icon, onClick, buttonStyle }) => {
  const t = useTranslations(namespace);

  return (
    <CustomButton
      variant="contained"
      startIcon={icon}
      onClick={onClick}
      disabled={disabled}
      sx={buttonStyle}
    >
      {t(label)}
    </CustomButton>
  );
};

export default MyButton;