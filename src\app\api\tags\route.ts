import { IListResponse } from "@/interface/IListResponse";
import { ITag } from "@/interface/ITag";
import {
  authorizedGet,
  authorizedPost,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";

async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const data = await authorizedGet<IListResponse<ITag>>(
      "/tags",
      await getAuthHeaders(req),
      {
        page: searchParams.get("page"),
        size: searchParams.get("size"),
        name: searchParams.get("keyword"),
      }
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function POST(req: NextRequest) {
  try {
    const data = await authorizedPost<ITag>(
      "/tags",
      await getAuthHeaders(req),
      await req.json()
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET, POST };
