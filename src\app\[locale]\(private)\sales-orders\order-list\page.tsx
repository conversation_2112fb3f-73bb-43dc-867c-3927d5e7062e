import type { NextPage } from "next";
import { Box } from "@mui/material";

import SearchSection from "./_components/SearchSection";
import SalesOrderTitle from "./_components/SalesOrderTitle";
import OrderListTabs from "./_components/Tabs/OrderListTabs";
import { retrieveOrderList } from "../_actions/retrieveOrderList.action";

type SalesOrderListProps = {};

const SalesOrderList: NextPage = async (props: SalesOrderListProps) => {
  const { items, meta } = await retrieveOrderList();

  const data = items ?? [];
  const totalRowCount = meta?.total ?? 0;

  return (
    <Box marginLeft={4} marginTop={3} display={"flex"} flexDirection={"column"}>
      <Box marginBottom={3}>
        <SalesOrderTitle />
      </Box>
      <SearchSection data={data}/>
      <Box marginTop={2}>
        <OrderListTabs data={data} totalRowCount={totalRowCount} />
      </Box>
    </Box>
  );
};

export default SalesOrderList;
