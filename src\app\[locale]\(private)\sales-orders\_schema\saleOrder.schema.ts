import { z } from "zod";
import {
  orderShippingStatus,
  orderShippingType,
  orderStatus,
  orderType,
} from "../_enums/order.enum";

// Basic response
export const baseApiResponseSchema = <T extends z.ZodTypeAny>(schema: T) => {
  return z.object({
    success: z.boolean(),
    data: schema,
    error: z
      .object({
        code: z.string(),
        details: z.unknown().optional(),
      })
      .optional(),
    meta: z
      .object({
        page: z.number().optional(),
        size: z.number().optional(),
        total: z.number().optional(),
      })
      .optional(),
  });
};

const PaginatedDataSchema = <T extends z.ZodTypeAny>(schema: T) => {
  return z.object({
    items: z.array(schema),
    meta: z.object({
      page: z.number(),
      size: z.number(),
      total: z.number(),
    }),
  });
};

const orderListSchema = z.object({
  orderId: z.number(),
  orderNo: z.string(),
  orderStatus: z.string(),
  userEmail: z.string(),
  userName: z.string(),
  orderAmount: z.number(),
  currency: z.string(),
  shippingType: z.string().optional(),
  shippingStatus: z.string().optional(),
  create_at: z.number(),
});

export const retrieveOrderListResponseSchema = baseApiResponseSchema(
  PaginatedDataSchema(orderListSchema)
);
export type RetrieveOrderListResponseType = z.infer<typeof retrieveOrderListResponseSchema>;
export type OrderListType = z.infer<typeof orderListSchema>;

// REGION: Batch update order delivery status
export const batchUpdateOrderShippingStatusRequestSchema = z.object({
  orderIds: z.number().array(),
  shippingStatus: z.enum([orderShippingStatus.delivered, orderShippingStatus.pending]),
});
export type BatchUpdateOrderShippingStatusRequestType = z.infer<
  typeof batchUpdateOrderShippingStatusRequestSchema
>;

const batchUpdateShippingStatusResponseSchema = baseApiResponseSchema(z.any());
export type BatchUpdateShippingStatusResponseType = z.infer<
  typeof batchUpdateShippingStatusResponseSchema
>;

// REGION: Batch update order delivery status
export const updateOrderShippingStatusRequestSchema = z.object({
  orderId: z.number(),
  shippingStatus: z.enum([orderShippingStatus.delivered, orderShippingStatus.pending]),
});
export type UpdateOrderShippingStatusRequestType = z.infer<
  typeof updateOrderShippingStatusRequestSchema
>;

// REGION: Batch update order status
export const updateOrderStatusRequestSchema = z.object({
  orderId: z.number(),
  orderStatus: z.enum([orderStatus.cancel, orderStatus.inProgress, orderStatus.complete]),
});
export type UpdateOrderStatusRequestType = z.infer<typeof updateOrderStatusRequestSchema>;

// REGION: Batch update order status
export const updateOrderRemarkRequestSchema = z.object({
  orderId: z.number(),
  remark: z.string(),
});
export type UpdateOrderRemarkRequestType = z.infer<typeof updateOrderRemarkRequestSchema>;

// REGION: Retrieve order by id
const orderSummary = z.object({
  orderTotalQuantity: z.number(),
  orderTotalAmount: z.number(),
  orderSubTotal: z.number(),
  orderShippingFee: z.number(),
  orderTaxAmount: z.number(),
  orderDiscountAmount: z.number(),
});
const orderItem = z.object({
  orderItemId: z.number(),
  productId: z.number(),
  skuId: z.number(),
  productName: z.string(),
  thumbnail: z.string(),
  price: z.object({
    unitPrice: z.number(),
    originalPrice: z.number(),
    totalPrice: z.number(),
  }),
  skuAttribute: z.array(z.object({ category: z.string(), value: z.string() })),
  quantity: z.number(),
});
const orderItemEvent = z.object({
  event_id: z.number(),
  eventName: z.string(),
});
const contract = z.object({
  name: z.string(),
  countryCode: z.string(),
  tel: z.string(),
});
const orderContact = contract.merge(z.object({ email: z.string() }));
const orderShipping = contract.merge(
  z.object({
    shippingType: z.enum([orderShippingType.express, orderShippingType.storePickup]).nullish(),
    address: z.string(),
  })
);
const saleOrderExtraInfo = z.object({
  paymentMethod: z.string(),
  paymentPlatform: z.string(),
});
const saleOrderShipping = orderShipping.merge(
  z.object({
    shippingStatus: z.enum([orderShippingStatus.pending, orderShippingStatus.delivered]),
  })
);
const paymentMethod = z.object({
  paymentCode: z.string(),
  paymentPlatform: z.string(),
  paymentType: z.string(),
});
const orderBilling = contract.merge(z.object({ address: z.string() }));
export const getOrderByIdResponseSchema = z.object({
  orderId: z.number(),
  userId: z.number(),
  orderType: z.enum([orderType.product, orderType.ticket]),
  region: z.string(),
  currency: z.string(),
  orderStatus: z.enum([orderStatus.inProgress, orderStatus.complete, orderStatus.cancel]),
  orderAdminNote: z.string(),
  orderUserNote: z.string(),
  orderExpireAt: z.number(),
  orderSummary: orderSummary,
  paymentMethod: paymentMethod.array(),
  events: orderItemEvent.array(),
  orderItems: orderItem.array(),
  orderContact: orderContact,
  orderShipping: orderShipping,
  orderBilling: orderBilling,
});

export const getSaleOrderByIdSchema = getOrderByIdResponseSchema
  .omit({ paymentMethod: true, orderShipping: true })
  .merge(saleOrderExtraInfo)
  .merge(
    z.object({
      orderNo: z.string(),
      txnRefNo: z.string(),
      orderShipping: saleOrderShipping,
      createAt: z.number(),
    })
  );
export const getSaleOrderByIdResponseSchema = baseApiResponseSchema(getSaleOrderByIdSchema);

export type SaleOrderDetailType = z.infer<typeof getSaleOrderByIdSchema>;
export type GetSaleOrderByIdResponseType = z.infer<typeof getSaleOrderByIdResponseSchema>;
