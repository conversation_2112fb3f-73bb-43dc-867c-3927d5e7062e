import CustomButton from "@/components/buttons/CustomButton";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import { IconButton, Link } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import CustomTextField from "@/components/input/CustomTextField";
import { ROUTES } from "@/utils/constants";
import {showErrorPopUp,showSuccessPopUp} from "@/utils/toast";
import xior from "xior";

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 4,
};

const textStyles = {
    color:"rgba(189, 189, 189, 1)",
  }

const linkStyles = {
color:"rgba(79, 183, 71, 0.8)",
}


type Props = {
    openForgetPassoword:boolean
    forgotPasswordEmail:string
    handleCloseForgetPassword:() => void
    handleForgotPasswordEmail:(event:any) => void
    handleForgotPasswordApi:() => void
    sentEmail:boolean
    handleRefreshForgotPasswordApi:()=> void
    countdown:number
}

const ForgetPasswordModal = ({openForgetPassoword,
                                forgotPasswordEmail,
                                handleCloseForgetPassword,
                                handleForgotPasswordEmail,
                                handleForgotPasswordApi,
                                sentEmail,
                                handleRefreshForgotPasswordApi,
                                countdown
                            }:Props) =>{

    // const [countdown, setCountdown] = useState<number>(0);
    // const { showToast: showSuccess } = showSuccessPopUp();
    // const { showToast: showError } = showErrorPopUp();

    // const getCurrentLocale = () => {
    //     return window.location.pathname.startsWith('/zh') ? 'zh' : 'en';
    //   }; 

    // const handleRefreshForgotPasswordApi = async () =>{
    //     const locale = getCurrentLocale();

    //     try{
    //         const result = await xior.post('/api/auth/forgot-password', {
    //             email: forgotPasswordEmail ,
    //             language:locale
    //         });

    //         console.log("ForgotPassword Refresh successful", result.data);
    //         showSuccess("Password reset email sent successfully!");

    //         if (countdown === 0) {
    //             // Start the countdown
    //             setCountdown(30);
    //         }

    //     }catch(err){
    //         console.error("ForgotPassword failed", err);
    //         showError ("Failed to send password reset email. Please try again."); 
    //     }
    // } 

    const renderSentEmailStatus = () =>{
        if(sentEmail){
            return(
                <>
                <Box sx={{ ...style, paddingTop: 2, paddingRight: 2, borderRadius:"24px" }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end'}}>
                        <IconButton onClick={handleCloseForgetPassword}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                    <Typography id="register-modal-title" variant="h2" component="h2" sx={{mb:1}}>
                        Check your inbox
                    </Typography>
                    <Typography id="register-modal-title" variant="body1" component="h2" sx={{color:"rgb(172, 172, 172)"}}>
                        We just emailed a reset link to<Typography id="register-modal-title" variant="body1" component="h2" sx={{color:"black"}}>{forgotPasswordEmail}</Typography>. Click the link and you’ll be prompted to choose a new password.
                    </Typography>
                    <Typography variant="subtitle1" sx={{
                        ...textStyles,
                        fontSize: '0.75rem', 
                        mt:1
                    }}>
                        <Box sx={{
                            display:"ruby-text"
                        }}>

                        <Typography sx={{
                            color:"black"
                        }}>Didn&apos;t receive the link?</Typography>
                        &nbsp;&nbsp; {/* 保留空格 */}

                                <Link 
                                href="#" 
                                variant="subtitle1" 
                                sx={{
                                    ...linkStyles,
                                    fontSize: '1rem',
                                    pointerEvents: countdown > 0 ? 'none' : 'auto',
                                }} 
                                onClick={handleRefreshForgotPasswordApi}
                            >
                    {countdown > 0 ? `Resend (${countdown}s)` : "Resend"}
                </Link>
                </Box>
                </Typography>
                </Box>
                </>
            )
        }else{
            return(
                <>
                <Box sx={{ ...style, paddingTop: 2, paddingRight: 2, borderRadius:"24px"  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end'}}>
                        <IconButton onClick={handleCloseForgetPassword}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                    <Typography id="register-modal-title" variant="h2" component="h2">
                        忘記密碼
                    </Typography>
                    <Box sx={{
                        display:'flex',
                        flexDirection:"column",
                        minWidth: 120,
                        gap: 2,
                        mt:2
                    }}>
                    <CustomTextField
                        namespace="login"
                        label="login_email"
                        placeholder="placeholder_email"
                        value={forgotPasswordEmail}
                        onChange={handleForgotPasswordEmail}
                    />
                    <CustomButton onClick={handleForgotPasswordApi} namespace="forgot_password" label="button_send_email"/>
                    </Box>
                </Box>
                </>
            )
        }
    } 
    

    return(
        <>
                <Modal
                open={openForgetPassoword}
                aria-labelledby="register-modal-title"
                aria-describedby="register-modal-description"
                sx={{
                    backdropFilter: 'blur(4px)', 
                    backgroundColor: 'rgba(122, 122, 123, 0.5)',
                }}
            >
                {/* <Box sx={{ ...style, paddingTop: 2, paddingRight: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end'}}>
                        <IconButton onClick={handleCloseForgetPassword}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                    <Typography id="register-modal-title" variant="h2" component="h2">
                        忘記密碼
                    </Typography>
                    <Box sx={{
                        display:'flex',
                        flexDirection:"column",
                        minWidth: 120,
                        gap: 2,
                        mt:2
                    }}>
                    <CustomTextField
                        namespace="login"
                        label="login_email"
                        placeholder="placeholder_email"
                        value={forgotPasswordEmail}
                        onChange={handleForgotPasswordEmail}
                    />
                    <CustomButton onClick={handleForgotPasswordApi} namespace="forgot_password" label="button_send_email"/>
                    </Box>
                </Box> */}
                {renderSentEmailStatus()}
            </Modal>
        </>
    )
}

export default ForgetPasswordModal;