import {Box, FormControl, InputLabel, Select, SelectChangeEvent} from "@mui/material";
import MenuItem from "@mui/material/MenuItem";

type Props = {
  category:string
  handleCategoryChange:(category:string) => void
  sortOrder:string
  handleSortOrderChange:(sortOrder:string)=> void

}

const CategoryFilter = ({category,
                        handleCategoryChange,
                        sortOrder,
                        handleSortOrderChange
                          }:Props) =>{

   const handleCategorySelectChange = (event: SelectChangeEvent) =>{
    handleCategoryChange(event.target.value)
   }

   const handleSortSelectChange = (event: SelectChangeEvent) =>{
    handleSortOrderChange(event.target.value)
   }

    return(
        <>
        <Box >
        <FormControl sx={{
          width:"208px",
          mb:3
        }}>
          <InputLabel id="demo-simple-select-label">所有類型</InputLabel>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            label="Category"
            value={category}
            onChange={handleCategorySelectChange}
          >
            <MenuItem value="">All</MenuItem>
            <MenuItem value="parents-children">Parents-Children</MenuItem>
            <MenuItem value="Anime">Anime</MenuItem>
            <MenuItem value="cosplay">Cosplay</MenuItem>
          </Select>
        </FormControl>

        <FormControl sx={{
          width:"208px",
          mb:3,
          ml:1
        }}>
          <InputLabel id="demo-simple-select-label">排列</InputLabel>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            label="排列"
            value={sortOrder}
            onChange={handleSortSelectChange}
          >
            {/* <MenuItem value="">排列</MenuItem> */}
            <MenuItem value="desc">從新到舊</MenuItem>
            <MenuItem value="asc">從舊到新</MenuItem>
          </Select>
        </FormControl>
      </Box>
        </>
    )

}

export default CategoryFilter;