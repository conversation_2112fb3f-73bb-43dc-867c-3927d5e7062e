import React from "react";
import {
  Box,
  Select,
  SelectChangeEvent,
  Typography,
  MenuItem,
} from "@mui/material";
import ExpandMoreRoundedIcon from "@mui/icons-material/ExpandMoreRounded";
import { useTranslations } from "next-intl";

export interface SortOption {
  labelKey: string;
  id: string;
  order: "asc" | "desc";
}

interface Props {
  value: SortOption;
  onChange: (value: SortOption) => void;
  options: SortOption[];
}

const SortBySelector = ({ value, onChange, options }: Props) => {
  const t = useTranslations();

  return (
    <Box display={"flex"} flexDirection={"row"} alignItems={"center"}>
      <Typography fontSize={13} fontWeight={400}>
        {t("common.label_sort_by")}
      </Typography>
      <Select
        size="small"
        sx={{ mx: 2, minWidth:'136px', textAlign:'center', fontSize: '13px', fontWeight:400 }}
        IconComponent={ExpandMoreRoundedIcon}
        value={value.labelKey}
        onChange={(event: SelectChangeEvent) => {
          const selected = options.find(
            (item) => item.labelKey === event.target.value
          );
          if (selected) {
            onChange(selected);
          }
        }}
      >
        {options.map(({ labelKey }) => (
          <MenuItem key={`sortBy:${labelKey}`} value={labelKey}>
            {t(labelKey)}
          </MenuItem>
        ))}
      </Select>
    </Box>
  );
};

export default SortBySelector;
