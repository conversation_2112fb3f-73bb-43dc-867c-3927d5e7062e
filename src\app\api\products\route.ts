import {
  authorizedGet,
  authorizedPost,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest,NextResponse } from "next/server";
import { IProduct } from "@/interface/IProduct";

async function GET(req: NextRequest) {
  try {
      const data = await authorizedGet<IProduct[]>(
      "/products",
        await getAuthHeaders(req)
      );
  
      return Response.json(data, { status: 200 });
    } catch (error) {
      return handleApiError(error);
    }

}

async function POST(req: NextRequest,res:NextResponse ) {
  try {
    const productData: IProduct = await req.json();

    const data = await authorizedPost<IProduct>(
      "/products",
      await getAuthHeaders(req),
      productData
    );

    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET,POST };