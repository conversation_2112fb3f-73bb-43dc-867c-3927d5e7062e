import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

export interface ICommonState {
    toast: {
        open: boolean,
        message: string,
        color: 'green'|'red'
    }
}

const initialState: ICommonState = {
    toast: {
        open: false,
        message: '',
        color: 'green'
    }
};

export const commonSlice = createSlice({
  name: "common",
  initialState,
  reducers: {
    setToast: (state, action: { payload: {message: string, type: 'warn'|'info'} }) => {
        let color: 'green'|'red' = action.payload?.type === 'warn'? 'red' : 'green'
        state.toast = {
            ...state.toast,
            ...action.payload,
            open: true,
            color
        }
    },
    closeToast: (state) => {
        state.toast = {
            ...state.toast,
            open: false
        }
    }
  },
});

export const { setToast, closeToast } = commonSlice.actions;
export const commonReducer = commonSlice.reducer;