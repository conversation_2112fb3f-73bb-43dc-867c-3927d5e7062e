import * as React from 'react';
import RecordRow from './record-row';
import {ClientOrderDetails} from "@/interface/IClientOrderDetails";
import Box from '@mui/material/Box';

type Props ={
    clientOrderDtoList: ClientOrderDetails
}

const RecordContainer = ({clientOrderDtoList}:Props) =>{

    // const recordRows = Array.from({ length: 5 }).map((_, index) => (
    //     <RecordRow key={index} />
    // ));

    return(
        <>
        <Box 
        sx={{
            maxHeight: "70vh", 
            overflowY: "auto", 
        }}>
         {/* {recordRows} */}
         {
            clientOrderDtoList.orderDetails
            .sort((a, b) => b.deliveryDetails.orderedTime - a.deliveryDetails.orderedTime)
            .filter((value)=> value.deliveryStatus !== null) 
            .map((value)=>(
                <RecordRow key={value.productId} orderDetails={value}/>
            ))
         }
        </Box>
        </>
    )
}

export default RecordContainer;
