import Image from "next/image";
import { Grid, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { EventList } from '@/interface/IEventList';
import { Event } from '@/interface/IEventList';
import * as React from 'react';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Button from '@mui/material/Button';
import { useRouter } from 'next/navigation';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import { format } from 'date-fns';

type Props = {
    eventList: EventList
}

const AllTab = ({ eventList }: Props) => {

     const router = useRouter();

    const accessFeaturedEventById = (eventId:any) =>{
        router.push(`/featured-events/${eventId}`)
    }

    console.log("check  eventList", eventList)
const regionMap:any = {
      hk: '香港',
      bk: '曼谷',
      mo: '澳門',
      my: '馬來西亞',
      sel: '首爾',
      sg: '新加坡'
    };

    return (
        <>
            <Grid container spacing={2} >
            {eventList.events.map((event) => {
                    // 在循環內部格式化日期
                    const eventformattedStartDate = event.eventStartDate
                        ? format(new Date(event.eventStartDate * 1000), "yyyy/MM/dd")
                        : "N/A";

                    const eventformattedEndDate = event.eventEndDate
                        ? format(new Date(event.eventEndDate * 1000), "yyyy/MM/dd")
                        : "N/A";

                    return (
                        <Grid item xs={12} sm={6} md={4} lg={3} xl={2} key={event.id}>
                            <Box sx={{ mb: 2, cursor: "pointer" }} onClick={() => accessFeaturedEventById(event.id)}>
                                <Card sx={{ maxWidth: 345 }}>
                                    <CardMedia
                                        sx={{ height: 140 }}
                                        image={event.thumbnail}
                                        title={event.eventName}
                                    />
                                    <CardContent>
                                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                            {eventformattedStartDate} - {eventformattedEndDate}
                                        </Typography>
                                        <Typography gutterBottom variant="h5" component="div">
                                            {event.eventName}
                                        </Typography>
                                    </CardContent>
                                    <Box sx={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        paddingRight: 2,
                                        paddingLeft: 2,
                                        paddingBottom: 2
                                    }}>
                                        <Typography gutterBottom variant="h5" component="div">
                                            HKD$100 起
                                        </Typography>
                                        <Stack direction="row" spacing={1}>
                                            <Chip label={regionMap[event.region]} sx={{
                                                backgroundColor: "rgba(240, 251, 239, 1)",
                                                borderColor: "white",
                                                color: "rgba(91, 170, 100, 1)"
                                            }} />
                                        </Stack>
                                    </Box>
                                </Card>
                            </Box>
                        </Grid>
                    );
                })}
            </Grid>
        </>
    )
}

export default AllTab;