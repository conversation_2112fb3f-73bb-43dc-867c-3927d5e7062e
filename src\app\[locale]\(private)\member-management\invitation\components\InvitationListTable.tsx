"use client";
import React, { useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  PaginationState,
  ColumnDef,
} from "@tanstack/react-table";
import { Box, Checkbox, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import { keepPreviousData, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import xior from "xior";
import { IMember } from "@/interface/IMember";
import { IListResponse } from "@/interface/IListResponse";
import PageSelector from "@/components/PageSelector";
import ProfileButton from "@/components/buttons/ProfileButton";
import PaginationTable from "@/components/PaginationTable";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ROUTES } from "@/utils/constants";
import AddToMemberGroup from "./AddToMemberGroup";
import RemoveFromMemberGroup from "./RemoveFromMemberGroup";
import SendButton from "@/components/buttons/SentButton";
import EditButton from "@/components/buttons/EditButton";
import DeleteButton from "@/components/buttons/DeleteButton";
import DeleteInvitation from "./DeleteInvitation";
import EditInvitation from "./EditInvitation";
import { IInvitation } from "@/interface/IInvitation";

const InvitationListTable = () => {
  const queryClient = useQueryClient();
  const t = useTranslations("invitation");
  const router = useRouter();
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [selectedMember, setSelectedMember] = React.useState<Array<any>>([])

  const {
    data: members,
    refetch,
    isLoading
  } = useQuery({
    queryKey: ["members", { pagination }],
    queryFn: async () => {
      return xior
        .get("/api/members/invitation", {
          params: {
            page: pagination.pageIndex + 1,
            size: pagination.pageSize
          },
        })
    },
    placeholderData: keepPreviousData,
    retry: 3,
  });

  const mutation = useMutation({
    mutationFn: (members: Array<{ id: number, email: string }>) => xior.post(`/api/members/invitation/email`, { members }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["invitations"] });
      setTimeout(() => {
        refetch()
      }, 1000)
    },
  });

  const columns = useMemo<ColumnDef<IInvitation>[]>(
    () => [
      {
        id: 'select-col',
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect() || row.original.status !== 0}
            onChange={row.getToggleSelectedHandler()}
          />
        ),
      },
      {
        accessorKey: "id",
        header: "",
        cell: (data) => {
          return data.row.original.status === 0 ? (
            <Box
              display={"flex"}
              flexDirection={"row"}
              justifyContent={"center"}
              gap={1}
              mx={1}
              key={`member-list-${data.row.original.id}-actions`}
            >
              <SendButton onClick={() => mutation.mutate([{ id: data.row.original.id, email: data.row.original.email }])} />
              <EditInvitation invitation={data.row.original} refetch={refetch} />
              <DeleteInvitation invitation={data.row.original} refetch={refetch} />
            </Box>
          ) : null
        },
      },
      {
        accessorKey: "name",
        header: t("label_name"),
      },
      {
        accessorKey: "email",
        header: t("label_email"),
      },
      {
        accessorKey: "memberGroups",
        header: t("label_group"),
      },
      {
        accessorKey: "status",
        header: t("label_status"),
        cell: (data) => {
          const status = data.getValue()
          switch (status) {
            case 0:
              return t("pending")
              break;
            case 1:
              return (
                <Typography fontWeight={300}>
                  {`${t("registered")}`} <br />
                  {format(data.row.original.updatedAt as string, "yyyy-MM-dd HH:mm:ss")}
                </Typography>
              )
            default:
              return t("pending")
          }
        }
      },
      {
        accessorKey: "emailSentDate",
        header: t("label_email_sent"),
        cell: (data) => {
          const datetime = data.getValue()
          if (!datetime) return "-"
          if (datetime) {
            return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
          }
        }
      },
      {
        accessorKey: "updatedAt",
        header: t("label_last_update"),
        cell: (data) => {
          const datetime = data.getValue()
          if (!datetime) return "-"
          if (datetime) {
            return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
          }
        }
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [router, t]
  );

  const updateContents = async () => {
    await refetch()
  }

  const defaultData = React.useMemo<IMember[]>(() => [], []);
  const table = useReactTable({
    data: members?.data?.items ?? defaultData,
    columns,
    rowCount: members?.data?.count,
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    enableMultiRowSelection: true,
    manualPagination: true,
  });

  React.useEffect(() => {
    const rows = table.getSelectedRowModel().rows
    setSelectedMember(rows.map((row) => row.original));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [table.getSelectedRowModel().rows])

  return (
    <Box display={"flex"} flexDirection={"column"} height={"100%"}>
      <Box display={"flex"} flexDirection={"row"} mb={3}>
        <Box flex={1} />
        <PageSelector
          value={pagination.pageSize}
          onChange={(newPageSize) => {
            table.setPageSize(newPageSize);
          }}
        />
      </Box>
      <Box flex={1}>
        <PaginationTable table={table} button={(
          <Box display={"flex"} flexDirection={"row"} gap={1} sx={{ marginTop: 3 }} >
            <SendButton onClick={() => mutation.mutate(selectedMember)} />
            <AddToMemberGroup target={selectedMember} updateContents={updateContents} />
            <RemoveFromMemberGroup target={selectedMember} updateContents={updateContents} />
          </Box>
        )} isLoading={isLoading} msg={t("title_empty")} />
      </Box>
    </Box>
  );
};

export default InvitationListTable;
