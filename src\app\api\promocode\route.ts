import { NextRequest } from "next/server";
import { IPromoCodeRecords } from "@/interface/IPromoCodeRecords";
import {
    authorizedPost,
    authorizedPut,
    getAuthHeaders,
    handleApiError,
} from "@/utils/api";


async function POST(req: NextRequest) {
    try{

        const promoRecords : IPromoCodeRecords = await req.json();


        const data = await authorizedPost<IPromoCodeRecords>(
            "/promocode/records",
            await getAuthHeaders(req),
            promoRecords
  
        );
        return Response.json(data, { status: 200 });
    }catch(error){
        console.error('Error fetching promo code:', error);
        return handleApiError(error); 
    }
}

async function PUT(req: NextRequest) {
    try{

    const promoRecords : IPromoCodeRecords = await req.json();

    const data = await authorizedPut<IPromoCodeRecords>(
        "/promocode/updateQuantity",
        await getAuthHeaders(req),
        promoRecords
    )

    }catch(error){
        console.error('Error promo code quantity:', error);
        return handleApiError(error); 
    }
}

export {POST,PUT};