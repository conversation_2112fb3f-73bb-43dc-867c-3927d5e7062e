import {
  Box, Checkbox,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography
} from "@mui/material";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { ROUTES, SUPPORT_CURRENCY, SUPPORT_WEIGTH, UNITY_ENDPOINT } from "@/utils/constants";
import Select from "@/components/input/Select";
import SelectMemberGroup from "@/components/input/SelectMemberGroup";
import {IMemberGroup} from "@/interface/IMemberGroup";
import useFileUpload from "@/hooks/useFileUpload";
import {IProduct} from "@/interface/IProduct";

type Props ={
  addProductDto:IProduct
  handleAddProductDtoChange:(addProductDto:IProduct)=>void
}


const Selling = ({addProductDto,handleAddProductDtoChange}:Props) =>{

    const [inventory, setInventory] = useState<number>(0); 
    const t = useTranslations();
    const [currency, setCurrency] = useState<string>('HKD');
    const [price, setPrice] = useState<number>(0);
    const [compareAt, setCompareAtPrice] = useState<number>(0);
    const [weight,setWeight]= useState<number>(0);
    const [supportWeight,setSupportWeight]= useState<string>("");
    const [includeTax, setIncludeTax] = useState<boolean>(false);
    const [tax,setTax] = useState<number>(0);
    const [isPublic, setIsPublic] = useState<boolean>(false);
    const [memberGroup, setMemberGroup] = useState<IMemberGroup[]>([]);
    const [status,setStatus] = useState<number>(1);
    const [type,setType] = useState<number>(1);

  //   const handleCategoryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  //     // handleCategoryChange
  //     const categoryChange = Number(event.target.value); 
  //     setSelectedSubCategory(categoryChange);
  //     handleAddProductDtoChange({
  //         ...addProductDto,
  //         category: categoryChange // 直接更新 owner
  //     });
  // };

  console.log("check member group",memberGroup)
 
    const handleMemberGroupChange = (memberGroup: IMemberGroup[]) => {
      setMemberGroup(memberGroup); 
      handleAddProductDtoChange({
        ...addProductDto,
        memberGroup: memberGroup  
    });
    };

    console.log(memberGroup)
    const handleIsPublicChange = (event: React.ChangeEvent<HTMLInputElement>) =>{
      const isPublicChange = event.target.checked
      setIsPublic(isPublicChange)
      handleAddProductDtoChange({
        ...addProductDto,
        openToAllMembers: isPublicChange 
    });
    }

    const handleTaxChange = (event: React.ChangeEvent<HTMLInputElement>) =>{
        const taxChange = event.target.value;
        const taxNumber = Number(taxChange);
        setTax(parseFloat(taxChange))
        handleAddProductDtoChange({
          ...addProductDto,
          tax: taxNumber
      });
      }

    const handleIncludeTaxChange = (event: React.ChangeEvent<HTMLInputElement>) =>{
      const includeTaxChange = event.target.checked
      setIncludeTax(includeTaxChange)
      handleAddProductDtoChange({
        ...addProductDto,
        taxIncluded: includeTaxChange 
    });
    }

    const handleSupportWeightChange = (event:any) =>{
      const supportWeightChange = event.target.value;
      setSupportWeight(supportWeightChange)
      handleAddProductDtoChange({
        ...addProductDto,
        weightUnit: supportWeightChange 
    });
    }

    const handleWeightChange = (event:any) =>{
      const weightChange = event.target.value;
      setWeight(weightChange)
      handleAddProductDtoChange({
        ...addProductDto,
        weight: weightChange 
    });
    }
    const handleCurrencyChange = (event:any) =>{
      const currentChange = event.target.value;
      setCurrency(currentChange)
      handleAddProductDtoChange({
        ...addProductDto,
        currency: currentChange 
    });
    }

    const handlePriceChange = (event:any) =>{
      const priceChange = event.target.value;
      setPrice(priceChange)
      handleAddProductDtoChange({
        ...addProductDto,
        price: priceChange
    });
    }

    const handleCompareAtPrice = (event:any) =>{
      const compareAtChange = event.target.value;
      const compareAtNumber = Number(compareAtChange);
      setCompareAtPrice(compareAtChange)
      handleAddProductDtoChange({
        ...addProductDto,
        compareAt: compareAtNumber
    });
    }

  // console.log(currency)
  // console.log(price)
    const handleStatusChange = (event: React.ChangeEvent<HTMLInputElement>) =>{
      const statusChange = Number(event.target.value);
      setStatus(statusChange);
            handleAddProductDtoChange({
          ...addProductDto,
          status: statusChange 
      });
    }

    const handleTypeChange = (event: React.ChangeEvent<HTMLInputElement>) =>{
      const typeChange = Number(event.target.value);
      setType(typeChange);
      handleAddProductDtoChange({
        ...addProductDto,
        type: typeChange 
    });
    }

    const handleInventoryChange = (event:any) => {
        const inventoryChange = Number(event.target.value);
        setInventory(inventoryChange); 
        handleAddProductDtoChange({
          ...addProductDto,
          quantity: inventoryChange 
      });
      };

      useEffect(() => {
          if(addProductDto.status){
            setStatus(addProductDto.status)
          }
          if(addProductDto.quantity){
            setInventory(addProductDto.quantity)
          }
          if(addProductDto.currency){
            setCurrency(addProductDto.currency)
          }
          if(addProductDto.price){
            setPrice(addProductDto.price)
          }
          if(addProductDto.compareAt){
            setCompareAtPrice(addProductDto.compareAt)
          }
          if(addProductDto.weight){
            setWeight(addProductDto.weight)
          }
          if(addProductDto.weightUnit){
            setSupportWeight(addProductDto.weightUnit)
          }
          if(addProductDto.taxIncluded){
            setIncludeTax(addProductDto.taxIncluded)
          }
          if(addProductDto.tax){
            setTax(addProductDto.tax)
          }
          if(addProductDto.openToAllMembers){
            setIsPublic(addProductDto.openToAllMembers)
          }
          if(addProductDto.memberGroup){
            setMemberGroup(addProductDto.memberGroup)
          }
      }, [addProductDto]);
    return(
        <>
            <Typography variant="subtitle1" component="h2">
            {t("product.label_status")}
            </Typography>
            <FormControl>
                <RadioGroup
                    aria-labelledby="demo-radio-buttons-group-label"
                    value={status}
                    onChange={handleStatusChange}
                    name="status"
                    sx={{ display: "block" }}
                >
                    <FormControlLabel value={1} control={<Radio />} label={t('product.label_draft')} />
                    <FormControlLabel value={2} control={<Radio />} label={t('product.label_listed')} />
                    <FormControlLabel value={3} control={<Radio />} label={t('product.label_unlisted')} />
                </RadioGroup>
            </FormControl>
            <Typography variant="subtitle1" component="h2">
            {t("product.label_selling_type")}
            </Typography>
            <Typography fontSize={12} color={"grey"}>
                        {t("product.selling_type_description")}
            </Typography>
            <FormControl>
                <RadioGroup
                    aria-labelledby="demo-radio-buttons-group-label"
                    value={type}
                    onChange={handleTypeChange}
                    name="type"
                    sx={{ display: "block" }}
                >
                    <FormControlLabel value={1} control={<Radio />} label={t('product.label_retail')} />
                </RadioGroup>
            </FormControl>
            <Typography variant="subtitle1" component="h2">
            {t("product.label_inventory")}
            </Typography>
            <TextField
                value={inventory} 
                onChange={handleInventoryChange} 
                required
                placeholder={"0"}
            />

          <Typography variant="subtitle1" component="h2">
            {t("product.label_price")}
          </Typography>

          <Box display={'flex'} flexDirection={"row"} gap={1}>
            <Select
              sx={{ width: 100 }}
              data={SUPPORT_CURRENCY}
              value={currency}
              onChange={handleCurrencyChange}
            />
            <TextField
              value={price}
              onChange={handlePriceChange}
              required
              placeholder={"0.00"}
            />
           </Box>
          <Typography fontSize={14}>
            {t("product.label_compare_at_price")}
          </Typography>
          <Typography fontSize={12} color={"grey"}>
            {t("product.compare_at_price_description")}
          </Typography>
          <TextField
            value={compareAt}
            onChange={handleCompareAtPrice}
            placeholder={"0.00"}
          />
          <Typography fontSize={14}>
            {t("product.label_weight")}
          </Typography>
          <Typography fontSize={12} color={"grey"}>
            {t("product.weight_description")}
          </Typography>
          <Box display={'flex'} flexDirection={"row"} gap={1}>
            <TextField
              value={weight}
              onChange={handleWeightChange}
              placeholder={"0.0"}
            />
            <Select
              sx={{ width: 80 }}
              data={SUPPORT_WEIGTH}
              value={supportWeight}
              onChange={handleSupportWeightChange}
            />
          </Box>
          <FormControlLabel
            control={
              <Checkbox
                checked={includeTax}
                onChange={handleIncludeTaxChange}
              />
            }
            label={t("product.label_charge_tax")}
          />
          {
            includeTax && (
              <>
              <Typography>{t("product.label_tax_rate")}</Typography>
              <TextField
                value={tax}
                onChange={handleTaxChange}
              />
              </>
            )
          }<br/>
          <FormControlLabel
            control={
              <Checkbox
                checked={isPublic}
                onChange={handleIsPublicChange}
              />
            }
            label={t("product.label_open_to_public")}
          />
          {
            !isPublic && (
              <>
                <Typography fontSize={14}>
                  {t("product.label_room_exclusive_to")}
                </Typography>
                <SelectMemberGroup
                  value={memberGroup}
                  onChange={handleMemberGroupChange}
                />
              </>
            )
          }
        </>
    )
}

export default Selling