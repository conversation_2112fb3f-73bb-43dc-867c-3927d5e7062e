"use client";
import * as React from "react";
import { Autocomplete, MenuItem, Select as MuiSelect, SelectProps, TextField } from "@mui/material";
import { useTranslations } from "next-intl";
import InputContainer, { InputContainerProps } from "./InputContainer";
import { AVAILABLE_DAY } from "@/utils/constants";
import { DatePicker } from "@mui/x-date-pickers";
import { CalendarIcon } from '@mui/x-date-pickers/icons';
import dayjs from "dayjs";
import Image from "next/image";

interface Props extends InputContainerProps {
  value: (number|string|undefined)[];
  seq: number;
  customChange: (val: any) => void;
  removeDate: () => void;
}

export default function SelectDate({
  label,
  description,
  placeholder,
  variant = "outlined",
  fullWidth = true,
  value = [],
  error,
  required,
  multiple = false,
  seq,
  customChange,
  removeDate,
  ...otherProps
}: Props & Omit<SelectProps, "error">) {
  const t = useTranslations("error");
  const [selected, setSelected] = React.useState<{ label: string, value: string|number }[]>([])
  const [input, setInput] = React.useState('');

  React.useEffect(() => {
    if (!selected) return
    customChange(selected.map(item => item.value))
  }, [selected, customChange])

  const handleOnChange = (event: any, data: any) => {
    setSelected(data)
  }

  React.useEffect(() => {
    if (!value) return;
    const parsed = value.map((item) => {
        if (!item) return;
        return {
            label: dayjs.unix(Number(item)).format('YYYY-MM-DD'),
            value: item
        }
    }).filter(item => item !== undefined)
    setSelected(parsed as { label: string, value: number }[])
  }, [value])

  const handleDateChange = (val: string) => {
    const regex = /^\d{4}-\d{2}-\d{2}$/; // Example: YYYY-MM-DD
    const isCompleteStr = regex.test(val)
    const date = new Date(val)
    if (isCompleteStr && !isNaN(date.getTime())) {
        const day = dayjs(val).startOf('day').unix()
        setSelected([
            ...selected,
            {
                label: val,
                value: day
            }
        ])
        setInput("")
    }
  }

  return (
    <InputContainer
      required={required}
      blockStyle={{ display: 'flex', flexDirection: 'row', gap: '12px' }}
    >
        <Autocomplete
            multiple
            id="tags-outlined"
            options={[]}
            getOptionLabel={(option: any) => option.label}
            filterSelectedOptions
            renderInput={(params: any) => (
                <TextField
                    {...params}
                    label={label}
                    placeholder={placeholder}
                    value={input}
                    onChange={(event) => handleDateChange(event.target.value)}
                />
            )}
            value={selected}
            onChange={handleOnChange}
        />
        <Image
          src="/images/icon-delete.svg"
          width={48}
          height={48}
          alt={`delete?`}
          onClick={() => removeDate()}
        />
    </InputContainer>
  );
}
