import {
  updateOrderShippingStatusRequestSchema,
  UpdateOrderShippingStatusRequestType,
} from "../_schema/saleOrder.schema";
import { fetchData } from "./api";

const URL = `${process.env.NEXT_PUBLIC_API_BASE}/api/admin/v1/order/shipping-status`;

export const updateShippingStatus = async (data: any) => {
  const requestData = updateOrderShippingStatusRequestSchema.parse(data);

  await fetchData<UpdateOrderShippingStatusRequestType>(URL, {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(requestData),
  });

  return true;
};
