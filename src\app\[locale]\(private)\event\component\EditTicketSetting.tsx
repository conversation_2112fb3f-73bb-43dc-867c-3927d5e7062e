"use client";
import * as React from "react";
import { Box, Button, Modal, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { ICollection } from "@/interface/ICollection";
import { useRouter } from "next/navigation";
import { CURRENCY, ROUTES, SALES_THRESHOLD, SUPPORT_CURRENCY } from "@/utils/constants";
import EditButton from "@/components/buttons/EditButton";
import TextEditor from "@/components/RichEditor";
import CancelButton from "@/components/buttons/CancelButton";
import UpdateButton from "@/components/buttons/UpdateButton";
import { DatePicker, DateTimePicker } from "@mui/x-date-pickers";
import Select from "@/components/input/Select";

interface Props {
    id?: number;
    eventId?: number;
    sale_start_datetime?: number;
    sale_end_datetime?: number;
    currency?: CURRENCY;
    sale_time_thershold?: number;
}

const buttonStyle = {
  flex: 1,
  fontSize: "1rem",
  fontWeight: 700,
  minWidth: "132px",
  minHeight: "46px",
};

const EditTicketSetting = ({ 
    id,
    eventId,
    sale_start_datetime,
    sale_end_datetime,
    currency,
    sale_time_thershold
}: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const t = useTranslations("ticket");
  const [open, setOpen] = React.useState(false);
  const [ticketSetting, setTicketSetting] = React.useState<{
    id?: number,
    eventId?: number,
    saleStartDatetime?: number,
    saleEndDatetime?: number,
    currency?: CURRENCY,
    saleTimeThershold?: number,
  }>({});

  React.useEffect(() => {
    setTicketSetting({
        id,
        saleStartDatetime: Number(sale_start_datetime) * 1000,
        saleEndDatetime: Number(sale_end_datetime) * 1000,
        currency,
        saleTimeThershold: sale_time_thershold
    })
  }, [
    id,
    sale_start_datetime,
    sale_end_datetime,
    currency,
    sale_time_thershold
  ])

  const mutation = useMutation({
    mutationFn: () => {
        const clone = { ...ticketSetting }
        if (clone.saleStartDatetime) clone.saleStartDatetime = Math.round(clone.saleStartDatetime/1000)
        if (clone.saleEndDatetime) clone.saleEndDatetime = Math.round(clone.saleEndDatetime/1000)
        if (eventId) {
          clone.eventId = eventId
        }
        const url = ['/api/ticket']
        if (id) {
            url.push(`/${id}`)
            return xior.put(url.join(""), clone)
        }
        return xior.post(url.join(""), clone)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["event"]});
      setOpen(false);
    },
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (mutation.isPending) return;
    setOpen(false);
  }, [mutation.isPending]);

  const handleUpdate = () => {
    mutation.mutate()
  }

  const handleDateChange = (key: "saleStartDatetime"|"saleEndDatetime", date: Date | null) => {
    if (!date) return;
    setTicketSetting({
        ...ticketSetting,
        [key]: date.valueOf()
    })
  }

  const handleFieldChange = (key: "currency"|"saleTimeThershold", value: any) => {
    if (!value) return;
    setTicketSetting({
        ...ticketSetting,
        [key]: value
    })
  }

  return (
    <Box>
      <EditButton onClick={handleOpen} isPrimay={false} isSmall={true} label={t("edit_ticket")}/>
      <Modal open={open} onClose={handleClose}>
        <ModalContainer sx={{ padding: '40px', gap: '32px' }}>
            <Typography variant="h3">{t("edit_ticket")}</Typography>

            <DateTimePicker
                label={t("sale_start_datetime")}
                value={
                    ticketSetting.saleStartDatetime ?
                    new Date(ticketSetting.saleStartDatetime)
                    : null
                }
                onChange={(date) => handleDateChange("saleStartDatetime", date)} 
            />

            <DateTimePicker
                label={t("sale_end_datetime")}
                value={
                    ticketSetting.saleEndDatetime ?
                    new Date(ticketSetting.saleEndDatetime)
                    : null
                }
                onChange={(date) => handleDateChange("saleEndDatetime", date)} 
            />

            <Select
                name="currency"
                value={ticketSetting.currency}
                label={t("currency")}
                handleOnChange={(event) => handleFieldChange("currency", event)}
                data={SUPPORT_CURRENCY}
            />

            <Select
                name="sale_time_thershold"
                value={ticketSetting.saleTimeThershold}
                label={t("sale_time_thershold")}
                handleOnChange={(event) => handleFieldChange("saleTimeThershold", event)}
                data={SALES_THRESHOLD}
            />


            <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginTop: "30px" }}>
                <CancelButton onAction={handleClose} />
                <UpdateButton onAction={handleUpdate} />
            </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default EditTicketSetting;
