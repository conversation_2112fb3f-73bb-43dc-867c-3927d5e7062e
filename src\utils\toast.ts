import { toast, ToastContent } from "react-toastify";
import { error } from "./error";

export const showToast = (
  content: ToastContent,
) => {
    toast.success(content)
};

export const showErrorToast = ( content: keyof typeof error ) => {
    toast.error(error[content])
}

export const showErrorPopUp = () =>{
  const showToast = (message:string) =>{
    toast.error(message, {

      progressStyle: { backgroundColor: 'red' },
  });
  };
  return {showToast}
};

export const showSuccessPopUp = () => {
  const showToast = (message: string) => {
      toast.success(message, {

          progressStyle: { backgroundColor: 'green' },
      });
  };

  return { showToast };
};
