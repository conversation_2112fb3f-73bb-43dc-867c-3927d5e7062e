"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const DashboardIcon = createSvgIcon(
  // <svg
  //   width="13"
  //   height="13"
  //   viewBox="0 0 13 13"
  //   fill="none"
  //   xmlns="http://www.w3.org/2000/svg"
  // >
  //   <rect
  //     x="0.5"
  //     y="2.5"
  //     width="10"
  //     height="10"
  //     rx="2.5"
  //     stroke="currentColor"
  //   />
  //   <circle cx="10" cy="3" r="2.5" fill="currentColor" stroke="currentColor" />
  // </svg>
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M2.85693 6.28575H13.1426M6.28551 13.1429V6.28575M3.99979 2.85718H11.9998C12.631 2.85718 13.1426 3.36885 13.1426 4.00003V12C13.1426 12.6312 12.631 13.1429 11.9998 13.1429H3.99979C3.36861 13.1429 2.85693 12.6312 2.85693 12V4.00003C2.85693 3.36885 3.36861 2.85718 3.99979 2.85718Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

  ,
  "Dashboard"
);

export default DashboardIcon;
