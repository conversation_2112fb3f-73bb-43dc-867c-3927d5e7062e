"use client"
import {
    Typography,
} from "@mui/material";
import { useTranslations } from "next-intl";
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import RecordContainer from "./record-container";
import {ClientOrderDetails} from "@/interface/IClientOrderDetails";
import { useEffect, useState } from "react";
import mockData from "./response.json";
import xior from "xior";


interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
  }
  
  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;
  
    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
      </div>
    );
  }
  
  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }     

const PurchaseRecord = () => {
    const t = useTranslations("purchase_record");
    const [value, setValue] = React.useState(0);
    const [clientOrderDto,setClientOrderDto] = useState<ClientOrderDetails | undefined>(undefined);

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
      setValue(newValue);
    };



    const handleClientOrderDetailsApi = async () =>{
      //setClientOrderDto(mockData)
      try{

        const response = await xior.get('/api/clientorder')

        setClientOrderDto(response.data);

         console.log("get client order list successfully", response.data)
      }catch(err){
        console.log("get client order list ",err);
      }
    }
  
    console.log("client order details",clientOrderDto)
      
    useEffect(() => {
      handleClientOrderDetailsApi()
      }, []); 

    return (
        <>
            <Typography variant="h2">
                {t('purchase_record')}
            </Typography>
            <Box sx={{ width: '100%',mt:2 }}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={value} onChange={handleChange} aria-label="basic tabs example"
                      TabIndicatorProps={{
                        sx: {
                            backgroundColor: 'rgba(79, 183, 71, 1)', 
                        }
                    }}
                    >
                    <Tab label={t(`event_tickets`)} {...a11yProps(0)} 
                      sx={{
                        '&.Mui-selected': {
                          color: 'rgba(79, 183, 71, 1)', 
                          borderColor: 'rgba(79, 183, 71, 1)',
                        },
                      }}
                    />
                    {/* <Tab label={t(`product_pre-order`)} {...a11yProps(1)} 
                      sx={{
                        '&.Mui-selected': {
                          color: 'rgba(79, 183, 71, 1)', 
                          borderColor: 'rgba(79, 183, 71, 1)',
                        },
                      }}
                    /> */}
                    {/* <Tab label="Item Three" {...a11yProps(2)} /> */}
                    </Tabs>
                </Box>
                <CustomTabPanel value={value} index={0}>
                  {
                    clientOrderDto &&
                    <RecordContainer clientOrderDtoList={clientOrderDto}/>
                  }
                </CustomTabPanel>
                {/* <CustomTabPanel value={value} index={1}>
                    Item Two
                </CustomTabPanel> */}
                {/* <CustomTabPanel value={value} index={2}>
                    Item Three
                </CustomTabPanel> */}
            </Box>
        </>
    );
}

export default PurchaseRecord;