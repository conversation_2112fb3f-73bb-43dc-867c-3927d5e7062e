import { z } from "zod";
import { SCHEMA_ERRORS } from "@/utils/constants";
import { ImageFileSchema } from "./ImageFileSchema";

export const ProfileSchema = z.object({
  identity: z.string(),
  photoUrl: z.union([ImageFileSchema, z.string()]),
  name: z
    .string({ required_error: SCHEMA_ERRORS.required })
    .min(1, SCHEMA_ERRORS.required),
  gender: z.string().optional(),
  phoneNumber: z.string(),
  dateOfBirth: z.date().nullable().optional(),
  addressLine1: z.string().optional(),
  addressLine2: z.string().optional(),
  addressLine3: z.string().optional(),
  countryCode: z.string(),
  contacts: z.object({
    whatsapp: z.string().optional(),
    telegram: z.string().optional(),
    other: z.string().optional(),
  }),
  socialMedias: z.object({
    instagram: z.string().optional(),
    facebook: z.string().optional(),
    youtube: z.string().optional(),
    x: z.string().optional(),
    linkedin: z.string().optional(),
    tiktok: z.string().optional(),
    pinterest: z.string().optional(),
    other: z.string().optional(),
  }),
  receivePromotions: z.boolean(),
});
