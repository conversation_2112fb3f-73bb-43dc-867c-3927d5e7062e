"use server";
import { fetchData } from "./api";

const URL = `${process.env.NEXT_PUBLIC_API_BASE}/api/admin/v1/country`;

export const retrieveCountryByCode = async (
  countryCode: string,
  language: string = "EN"
): Promise<any | undefined> => {
  const requestUrl = `${URL}/${countryCode}?language=${language}`;

  const resultResponse = await fetchData<any>(requestUrl, {
    next: { revalidate: 0 },
  });

  return resultResponse?.data;
};
