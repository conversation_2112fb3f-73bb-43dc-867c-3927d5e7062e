"use client";
import * as React from "react";
import { Box, Button, Modal, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ModalContainer from "@/components/ModalContainer";
import { ICollection } from "@/interface/ICollection";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";

import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TextField from "@/components/input/TextField";
import { TimePicker } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import Image from "next/image";

interface Props {
    id?: number;
    rows: TimeSlotData[];
    handleOnTimeSlotChange: (seq: number, idx: number, key: string, val: any) => void;
    removeRow: (seq: number, idx: number, ids: number[]) => void;
}

export type TimeSlotData = {
    startTime: number,
    endTime: number,
    quantity: number,
    id: any,
    idx: number,
    seq: number
}

const TimeSlotTable = ({ id, rows, handleOnTimeSlotChange, removeRow }: Props) => {
    const t = useTranslations("ticket");
    const tCommon = useTranslations("common");

    const columns: GridColDef<(TimeSlotData[])[number]>[] = [
        {
            field: 'startTime',
            headerName: t("start_time"),
            renderCell: (params) => (
                <TimePicker
                    sx={{ marginTop: '10px' }}
                    label={t("start_time")}
                    value={dayjs('2000-01-01').add(params.value, 's')}
                    onChange={(val) => 
                        handleOnTimeSlotChange(params.row.seq, params.row.idx, 'startTime', val)
                    }
                />
            ),
            flex: 1
        },
        {
            field: 'endTime',
            headerName: t("end_time"),
            renderCell: (params) => (
                <TimePicker
                    sx={{ marginTop: '10px' }}
                    label={t("end_time")}
                    value={dayjs('2000-01-01').add(params.value, 's')}
                    onChange={(val) => 
                        handleOnTimeSlotChange(params.row.seq, params.row.idx, 'endTime', val)
                    }
                />
            ),
            flex: 1
        },
        {
            field: 'quantity',
            headerName: t("quantity"),
            renderCell: (params) => (
                <TextField
                    sx={{ marginTop: '10px' }}
                    label={t("quantity")}
                    value={params.value}
                    onChange={(event) => 
                        handleOnTimeSlotChange(params.row.seq, params.row.idx, 'quantity', event.target.value)
                    }
                />
            ),
            flex: 1
        },
        {
            field: 'idx',
            renderCell: (params) => (
                <Image
                    src={'/images/icon-delete.svg'}
                    width={48}
                    height={48}
                    alt={`delete?`}
                    onClick={() => { removeRow(params.row.seq, params.row.idx, params.row.id) }}
                />
            )
        }
    ];

    return (
        <Box>
            <DataGrid
                rows={rows}
                getRowId={(row) => {
                    return `${row.seq}_${row.idx}`
                }}
                columns={columns}
                checkboxSelection
                disableRowSelectionOnClick
                hideFooterPagination={true}
                hideFooter={true}
                columnHeaderHeight={0}
            />
        </Box>
    );
};

export default TimeSlotTable;
