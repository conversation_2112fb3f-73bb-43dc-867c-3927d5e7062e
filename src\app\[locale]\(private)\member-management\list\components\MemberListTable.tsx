"use client";
import React, { useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  PaginationState,
  ColumnDef,
} from "@tanstack/react-table";
import { Box, Checkbox } from "@mui/material";
import { useTranslations } from "next-intl";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IMember } from "@/interface/IMember";
import { IListResponse } from "@/interface/IListResponse";
import PageSelector from "@/components/PageSelector";
import ProfileButton from "@/components/buttons/ProfileButton";
import EditMemberList from "./EditMemberList";
import PaginationTable from "@/components/PaginationTable";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ROUTES } from "@/utils/constants";
import AddToMemberGroup from "./AddToMemberGroup";
import RemoveFromMemberGroup from "./RemoveFromMemberGroup";

const MemberListTable = () => {
  const t = useTranslations("member_management_list");
  const router = useRouter();
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [selectedMember, setSelectedMember] = React.useState<IMember[] | []>([]);

  const {
    data: members,
    refetch,
    isLoading
  } = useQuery({
    queryKey: ["members", { pagination }],
    queryFn: async () => {
      return xior
        .get("/api/members", {
          params: {
            page: pagination.pageIndex + 1,
            size: pagination.pageSize
          },
        })
    },
    placeholderData: keepPreviousData,
    retry: 3,
  });

  const columns = useMemo<ColumnDef<IMember>[]>(
    () => [
      {
        id: 'select-col',
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            onChange={row.getToggleSelectedHandler()}
          />
        ),
      },
      {
        accessorKey: "userId",
        header: "",
        cell: (data) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"center"}
            gap={1}
            mx={1}
            key={`member-list-${data.row.original.userId}-actions`}
          >
            {/*<EditMemberList target={data.row.original} updateContents={refetch}/>*/}
            <ProfileButton
              
              onClick={() => {
                router.push(`${ROUTES.PROFILE}/${data.row.original.userId}`)
              }}
            />
          </Box>
        ),
      },
      {
        accessorKey: "name",
        header: t("label_name"),
      },
      {
        accessorKey: "email",
        header: t("label_email"),
      },
      {
        accessorKey: "memberGroups",
        header: t("label_group"),
      },
      {
        accessorKey: "platform",
        header: t("label_platform"),
      },
      {
        accessorKey: "updatedAt",
        header: t("label_updated_at"),
        cell: (data) => {
          const datetime = data.getValue()
          if (!datetime) return "-"
          if (datetime) {
            return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
          }
        }
      },
      {
        accessorKey: "createdAt",
        header: t("label_registered_at"),
        cell: (data) => {
          const datetime = data.getValue()
          if (!datetime) return "-"
          if (datetime) {
            return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
          }
        }
      },
    ],
    [router, t]
  );

  const updateContents = async () => {
    await refetch()
  }

  const defaultData = React.useMemo<IMember[]>(() => [], []);
  const table = useReactTable({
    data: members?.data?.items ?? defaultData,
    columns,
    rowCount: members?.data?.count,
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    enableMultiRowSelection: true,
    manualPagination: true,
  });

  React.useEffect(() => {
    const rows = table.getSelectedRowModel().rows
    setSelectedMember(rows.map((row) => row.original))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [table.getSelectedRowModel().rows])

  return (
    <Box display={"flex"} flexDirection={"column"} height={"100%"}>
      <Box display={"flex"} flexDirection={"row"} mb={3}>
        <Box flex={1} />
        <PageSelector
          value={pagination.pageSize}
          onChange={(newPageSize) => {
            table.setPageSize(newPageSize);
          }}
        />
      </Box>
      <Box flex={1}>
        <PaginationTable table={table} button={(
          <Box display={"flex"} flexDirection={"row"} gap={1}>
            <AddToMemberGroup target={selectedMember} updateContents={updateContents} />
            <RemoveFromMemberGroup target={selectedMember} updateContents={updateContents} />
          </Box>
        )} isLoading={isLoading} msg={t("title_empty")} />
      </Box>

    </Box>
  );
};

export default MemberListTable;
