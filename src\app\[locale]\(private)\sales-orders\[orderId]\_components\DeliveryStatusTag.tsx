"use client";
import { Chip, styled } from "@mui/material";
import {
  orderShippingStatus,
  OrderShippingType,
  orderShippingType,
  OrderStatusType,
} from "@/app/[locale]/(private)/sales-orders/_enums/order.enum";
import { useTranslations } from "next-intl";

type DeliveryStatusTagProps = {
  status: OrderStatusType;
  shippingType: OrderShippingType;
};

// NOTE: Delivery Status
const PendingChip = styled(Chip)(({ theme }) => ({
  backgroundColor: theme.palette.incutix.secondary[300],
  color: theme.palette.incutix.secondary[100],
  border: 0,
  padding: "0px 0px",
  "& span.MuiChip-label": {
    fontWeight: 700,
    fontSize: 14,
    padding: "0px 12px",
  },
}));
const DeliveryCompleteChip = styled(Chip)(({ theme }) => ({
  backgroundColor: theme.palette.incutix.primary[400],
  color: theme.palette.incutix.primary[200],
  border: 0,
  padding: "0px 0px",
  "& span.MuiChip-label": {
    fontWeight: 700,
    fontSize: 14,
    padding: "0px 12px",
  },
}));

const useDeliveryStatusTagText = () => {
  const t = useTranslations("sales_orders.table.tag.delivery_status");

  return {
    [orderShippingStatus.pending]: t("pending"),
    [`${orderShippingType.express}_${orderShippingStatus.delivered}`]: t("express_delivered"),
    [`${orderShippingType.storePickup}_${orderShippingStatus.delivered}`]: t("store_delivered"),
  };
};

const OrderStatusTag = ({ status, shippingType }: DeliveryStatusTagProps) => {
  const deliveryStatusTagMapper: Record<string, string> = useDeliveryStatusTagText();

  const deliveryStatusText =
    status === orderShippingStatus.delivered ? `${shippingType}_${status}` : status;
  const label = deliveryStatusTagMapper[deliveryStatusText] ?? "";

  return (
    {
      [orderShippingStatus.pending]: <PendingChip label={label} />,
      [orderShippingStatus.delivered]: <DeliveryCompleteChip label={label} />,
    }[status] ?? <></>
  );
};

export default OrderStatusTag;
