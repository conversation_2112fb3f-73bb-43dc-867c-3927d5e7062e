import { z } from "zod";
import { SCHEMA_ERRORS } from "@/utils/constants";

const password = z.string().refine((value) => 
  /^(?=.*[A-Z])(?=.*[!@#$&*])(?=.*[0-9])(?=.*[a-z]).{8,20}$/.test(value ?? ""), {
    message: "Password should be 8-20 length and contains a combination of uppercase and lowercase letters, numbers and symbols",
  }
)

export const LoginSchema = z.object({
  email: z
    .string({ required_error: SCHEMA_ERRORS.required })
    .email(SCHEMA_ERRORS.invalidEmail),
  password,
});
