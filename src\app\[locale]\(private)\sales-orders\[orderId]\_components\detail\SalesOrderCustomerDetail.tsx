"use client";
import { Box, Paper, Stack, styled } from "@mui/material";
import { useTranslations } from "next-intl";
import { SaleOrderDetailType } from "../../../_schema/saleOrder.schema";

type Props = {
  saleOrderDetail?: SaleOrderDetailType;
  country?: string;
};

const StyledBox = styled(Paper)(({ theme }) => ({
  width: 320,
  borderRadius: 16,
  border: `1px solid ${theme.palette.incutix.grey[400]}`,
  padding: "20px",
  backgroundColor: theme.palette.incutix.white,
}));
const DetailTitle = styled(Box)(({ theme }) => ({
  marginBottom: "24px",
  fontSize: 18,
  fontWeight: 700,
  color: "#000000",
}));
const DetailItemLabel = styled(Box)(({ theme }) => ({
  height: 20,
  display: "flex",
  width: "100%",
  justifyContent: "left",
  alignItems: "center",
  fontSize: 14,
  fontWeight: 400,
  color: theme.palette.incutix.grey[600],
}));
const DetailItemValue = styled(Box)(({ theme }) => ({
  height: 20,
  display: "flex",
  width: "100%",
  justifyContent: "right",
  alignItems: "center",
  fontSize: 14,
  fontWeight: 400,
  color: "#000000",
}));

const SalesOrderCustomerDetail = ({ saleOrderDetail, country }: Props) => {
  const t = useTranslations("sales_orders.detail.customer_detail");

  const orderContact = saleOrderDetail?.orderContact;
  const region = country;

  return (
    <StyledBox elevation={0}>
      <DetailTitle>{t("title")}</DetailTitle>
      <Stack spacing={1}>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("name")}</DetailItemLabel>
          <DetailItemValue>{orderContact?.name ?? ""}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("email")}</DetailItemLabel>
          <DetailItemValue>{orderContact?.email ?? ""}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("phone_no")}</DetailItemLabel>
          <DetailItemValue>{`${orderContact?.countryCode ?? ""} ${
            orderContact?.tel ?? ""
          }`}</DetailItemValue>
        </Box>
        <Box display={"flex"} flexDirection={"row"}>
          <DetailItemLabel>{t("region")}</DetailItemLabel>
          <DetailItemValue>{region ?? ""}</DetailItemValue>
        </Box>
      </Stack>
    </StyledBox>
  );
};

export default SalesOrderCustomerDetail;
