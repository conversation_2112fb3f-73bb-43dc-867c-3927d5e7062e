import { Box, FormControl, FormControlLabel, FormLabel, Radio, RadioGroup, TextField, Typography } from "@mui/material";
import FileInput from "@/components/input/FileInput";
import { useState } from "react";
import { ICollection } from "@/interface/ICollection";
import SelectCollection from "@/components/input/SelectCollection";
import { useTranslations } from "next-intl";
import { IOwner } from "@/interface/IOwner";
import SelectOwner from "@/components/input/SelectOwner";
import SelectCategory from "@/components/input/SelectCategory";
import { ITag } from "@/interface/ITag";
import SelectTag from "@/components/input/SelectTag";
import * as React from "react";
import EditProductImageTable from "./EditProductImageTable";
import {ProductDetailDto} from '@/interface/IProductDetailDto'
import useFileUpload from "@/hooks/useFileUpload";
import Image from "next/image";
import { IconButton } from '@mui/material';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';

type Props = {
    updateProductDto: ProductDetailDto
    handleUpdateProductDtoChange:(updateProductDto:ProductDetailDto)=> void
}

const EditInformation = ( {updateProductDto,handleUpdateProductDtoChange}:Props) => {
    const [file, setFile] = useState<File | string | undefined>(updateProductDto.audio);
    const [error, setError] = useState<string | undefined>(undefined);
    const [selectedCollections, setSelectedCollections] = useState<ICollection[]>(updateProductDto.collections);
    const [selectedOwner, setSelectedOwner] = useState<IOwner | null>(updateProductDto.ownerInfo);
    const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
    const [selectedSubCategory, setSelectedSubCategory] = useState<number | null>(updateProductDto.category);
    const [tags, setTags] = useState<ITag[]>(updateProductDto.tags); // Initialize with an empty array
    const [image, setImage] = useState<File | string | undefined>(updateProductDto.thumbnail);
    const [productName,setProductName] = useState<string>(updateProductDto.name);
    const [description, setDescription] = useState<string>(updateProductDto.description);
    const [productType, setProductType] = useState<number>(updateProductDto.productType);
    const [audioUrl, setAudioUrl] = useState<string>(updateProductDto.audio);
    const { uploadFile } = useFileUpload();
    const isSmall = false;

    console.log('check audioUrl',audioUrl)    

    const handleDescriptionChange = (event:any) =>{
        const newValue = event.target.value
        setDescription(newValue);
        handleUpdateProductDtoChange({
            ...updateProductDto,
            description: newValue,
        })
    }
    console.log('check owner',selectedOwner)
    const handldeProductName = (event:any) =>{
        const newValue = event.target.value
        setProductName(newValue)
        handleUpdateProductDtoChange({
            ...updateProductDto,
            name: newValue,
        })
    }

    const hanldeProductTypeChange = (event:any) =>{
        const newValue = event.target.value
        setProductType(newValue)
        handleUpdateProductDtoChange({
            ...updateProductDto,
            productType: newValue,
        })
    }

    const handleTagsChange = (newTags: ITag[]) => {
      setTags(newTags);
      handleUpdateProductDtoChange({
        ...updateProductDto,
        tags: newTags,
    })
    };

    // const handleImageChange = (newFiles: File | string | undefined) => {
    //     setImage(newFiles); 
    //   };

    const handleImageChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFile = event.target.files?.[0];
        if (selectedFile) {
            const reader = new FileReader();
            reader.onload = () =>{
                setImage(reader.result as string);
            }
            reader.readAsDataURL(selectedFile);
            try {
                // 直接在文件选择后上传
                const thumbnailUrl = await uploadFile(selectedFile, "productThumbnail");
                console.log("Uploaded thumbnail URL:", thumbnailUrl);
                                handleUpdateProductDtoChange({
                        ...updateProductDto,
                        thumbnail: thumbnailUrl, // 将返回的 URL 设置为 thumbnail
                    });
            } catch (error) {
                console.error("Upload failed:", error);
            }
        }
    };

    const removeFile = () => {
        setImage("");
        // 其他需要执行的操作
    };


    // const handleFileChange = (newFile: File | undefined) => {
    //     if (newFile) {
    //         setFile(newFile);
    //         setError(undefined); 
    //     } else {
    //         setFile(undefined); 
    //     }
    // };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) =>{
        const selectedFile = event.target.files?.[0];
        if (selectedFile) {
            const reader = new FileReader();
            reader.onload = () =>{
                setFile(reader.result as string);
            }
            reader.readAsDataURL(selectedFile);
            try {
                // 直接在文件选择后上传
                const audioUrl = await uploadFile(selectedFile, "audio file");
                console.log("Uploaded audio URL:", audioUrl);
                        handleUpdateProductDtoChange({
                        ...updateProductDto,
                        audio: audioUrl, 
                    });
                    setAudioUrl(audioUrl);
            } catch (error) {
                console.error("Upload failed:", error);
            }
        }
      }

    const handleCollectionChange = (newCollections: ICollection[]) => {
        setSelectedCollections(newCollections); 
        handleUpdateProductDtoChange({
            ...updateProductDto,
            collections: newCollections,
        })
    };


    // const hanldeProductTypeChange = (event:any) =>{
    //     const newValue = event.target.value
    //     setProductType(newValue)
    //     handleUpdateProductDtoChange({
    //         ...updateProductDto,
    //         productType: newValue,
    //     })
    // }

    // const handleOwnerChange = (newOwner: IOwner | null) => {
    //     setSelectedOwner(newOwner); 
    //     handleAddProductDtoChange({
    //         ...addProductDto,
    //         owner: newOwner ? newOwner.id.toString() : '' // 直接更新 owner
    //     });
    // };


    const handleOwnerChange = (newOwner: IOwner | null) => {
        setSelectedOwner(newOwner); 
                handleUpdateProductDtoChange({
            ...updateProductDto,
            owner: newOwner ? newOwner.id.toString() : '' // 直接更新 owner
        });
    };

    const handleCategoryChange = (event: any) => {
        const newValue = event.target.value
        console.log('check SubCategory', newValue)
        setSelectedSubCategory(newValue);
        handleUpdateProductDtoChange({
            ...updateProductDto,
            category: newValue
        });
    };
    
    const removeAudioFile = () => {
        setFile(undefined);
        handleUpdateProductDtoChange({
            ...updateProductDto,
            audio: ""
        });
    };

    const t = useTranslations();

    return (
        <>
            <Typography variant="subtitle1">
                {t("product.label_name")}
            </Typography>
            <TextField
                name="name"
                id="outlined-size-small"
                size="small"
                value={productName}
                onChange={handldeProductName}
                placeholder={t("product.placeholder_name")}
                sx={{ width: '500px' }}
            />
            <Typography variant="subtitle1">
                {t("product.label_description")}
            </Typography>
            <TextField
                name="description"
                id="outlined-multiline-static"
                multiline
                rows={8}
                variant="outlined"
                value={description}
                onChange={handleDescriptionChange}
                placeholder={t("product.label_placeholder_description")}
                sx={{ width: '500px' }}
            />
            <Typography variant="subtitle1">
                {t("product.label_product_type")}
            </Typography>
            <FormControl>
                <RadioGroup
                    aria-labelledby="demo-radio-buttons-group-label"
                    value={productType}
                    onChange={hanldeProductTypeChange}
                    name="productType"
                    sx={{ display: "block" }}
                >
                    <FormControlLabel value={1} control={<Radio />} label={t('product.label_physical_product')} />
                    <FormControlLabel value={2} control={<Radio />} label={t('product.label_digital_product')} />
                </RadioGroup>
            </FormControl>
            <Typography variant="subtitle1">
                {t("product.label_product_audio")}
            </Typography>
            <Typography fontSize={12} color={'grey'}>
                {t("file_upload.audio_metadata").split("\n").map((item: string, index: number) => (
                    <span key={index}>{item}<br /></span> // 添加 key 属性
                ))}
            </Typography>
            {/* <FileInput
                metadata={t("file_upload.background_music_upload")}
                onChange={handleFileChange}
                value={file}
                error={error}
                type="audio"
                multiple={true}
                name="audio"
            /> */}
                                   <Box
            display="flex"
            flexDirection={"column"}
            alignItems={"center"}
            width="50%"
            mb={1.5}
            >
            {
                file ? (
                    <Box
                        display="flex"
                        flexDirection={"column"}
                        alignItems={"center"}
                        width="100%"
                        mb={1.5}
                        sx={{
                            overflow: 'hidden',
                            mt:"10px"
                        }}
                    >
                        <audio controls src={audioUrl} style={{ width: '100%' }}>
                            Your browser does not support the audio element.
                        </audio>
                        <IconButton
                            aria-label="delete"
                            size="small"
                            sx={{
                                background: "#ff3b36",
                                height: 18,
                                width: 18,
                                position: "absolute",
                                right: isSmall ? -8 : 20,
                            }}
                            onClick={removeAudioFile}
                        >
                            <CloseRoundedIcon sx={{ fill: "white", height: 16, width: 16 }} />
                        </IconButton>
                    </Box>
                ) : (
                    <Box
                        display="flex"
                        flexDirection={"column"}
                        alignItems={"center"}
                        width="100%"
                        mb={1.5}
                    >
                        <div>
                            <label htmlFor="file-upload-listAudio" style={{ cursor: 'pointer' }}>
                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                <img
                                    height={36}
                                    width={36}
                                    src={file ? (file as string) : "/images/icon-upload.png"}
                                    alt="Upload audio"
                                />
                            </label>
                            <input
                                id="file-upload-listAudio"
                                type="file"
                                accept="audio/*" // Accept only audio files
                                onChange={handleFileChange}
                                style={{ display: 'none' }} // Hide file input
                            />
                        </div>
                        <Typography>Upload your audio</Typography>
                    </Box>
                )
            }
                                
           </Box>  
            <Typography variant="subtitle1">
                {t("product.label_collection")}
            </Typography>
            <SelectCollection
                placeholder={t("product.collection_description")}
                value={selectedCollections}
                onChange={handleCollectionChange}
                disabled={false}
            />
            <Typography variant="subtitle1">
                {t("product.label_owner")}
            </Typography>
            <SelectOwner
                placeholder={t("product.owner_description")}
                value={selectedOwner}
                onChange={handleOwnerChange}
                disabled={false}
            />
            <Typography variant="subtitle1">
                {t("product.label_category")}
            </Typography>
            <SelectCategory
                placeholder="选择类别"
                value={selectedSubCategory} 
                onChange={handleCategoryChange} 
                displayEmpty={true} 
                disabled={false} 
            />
            <Typography variant="subtitle1">
             {t("product.label_tag")}
            </Typography>
            <SelectTag
                value={tags}
                onChange={handleTagsChange}
                placeholder="Select or add tags"
                disabled={false}
            />
            <Typography variant="subtitle1">
            {t("product.label_list_of_thumbnail")}
            </Typography>
            <Typography fontSize={12} color={'grey'}>
                <i>
                  {
                    t("file_upload.image_video_metadata").split("\n").map((item: string) => (
                      <>{item}<br /></>
                    ))
                  }
                </i>
              </Typography>
              {/* <FileInput
                metadata={t("file_upload.thumbnail_multimedia_upload")}
                onChange={handleImageChange}
                value={image}
                error={error}
                type="image_video"
                multiple={true}
                name="thumbnail"
            /> */}
                       <Box
            display="flex"
            flexDirection={"column"}
            alignItems={"center"}
            width="50%"
            mb={1.5}
            >
            {
               image? <Box
               display="flex"
               flexDirection={"column"}
               alignItems={"center"}
               width="100%"
               mb={1.5}
               sx={{
                backgroundImage: `url(${image})!important`, // 替换成你的图片路径
                backgroundSize: 'cover', // 适应盒子大小
                backgroundPosition: 'center', // 图片居中
                height: '300px',
                overflow: 'hidden',
            }}
               >             <IconButton
               aria-label="delete"
               size="small"
               sx={{
                   background: "#ff3b36",
                   height: 18,
                   width: 18,
                   position: "absolute",
                //    bottom: isSmall ? -4 : 14,
                   right: isSmall ? -8 : 20,
               }}
               onClick={removeFile}
           >
               <CloseRoundedIcon sx={{ fill: "white", height: 16, width: 16 }} />
           </IconButton>
               </Box>:         <Box
            display="flex"
            flexDirection={"column"}
            alignItems={"center"}
            width="100%"
            mb={1.5}
            >
            <Image
            height={36}
            width={36}
            src="/images/icon-upload.png"
            alt="Upload image"
        />
                <input type="file" onChange={handleImageChange} /></Box>
            }
                    
           </Box>          
          <Typography fontSize={14}>
            {t("product.label_product_asset")}
          </Typography>
          <Typography fontSize={12} color={'grey'}>
            <i>
              {
                t("file_upload.image_video_metadata").split("\n").map((item: string) => (
                  <>{item}<br /></>
                ))
              }
            </i>
          </Typography>
          <EditProductImageTable 
          updateProductDto={updateProductDto}
          handleUpdateProductDtoChange={handleUpdateProductDtoChange}
          />
        </>
    );
}

export default EditInformation;