"use client";
import { Box, Paper, Stack, styled } from "@mui/material";
import { useTranslations } from "next-intl";
import Grid from "@mui/material/Unstable_Grid2";
import Image from "next/image";
import { formatCurrency, getCurrencySymbol } from "@/utils/currency";
import { SaleOrderDetailType } from "../../../_schema/saleOrder.schema";
import { useMemo } from "react";

type Props = {
  saleOrderDetail?: SaleOrderDetailType;
};
interface ItemSummaryDataType {
  name: string;
  image: string;
  price: number;
  qty: number;
  total: number;
  currency: string;
  skuAttribute: { category: string; value: string }[];
}

const StyledBox = styled(Paper)(({ theme }) => ({
  borderRadius: 16,
  border: `1px solid ${theme.palette.incutix.grey[400]}`,
  backgroundColor: theme.palette.incutix.white,
}));
const DetailTitle = styled(Box)(({ theme }) => ({
  fontSize: 18,
  fontWeight: 700,
  color: "#000000",
  padding: 20,
}));
const DetailSubTitle = styled(Box)(({ theme }) => ({
  marginBottom: "24px",
  fontSize: 14,
  fontWeight: 700,
  color: theme.palette.incutix.grey[600],
  padding: "0 20px",
}));

const TableHeaderRow = styled(Grid)(({ theme }) => ({
  backgroundColor: theme.palette.incutix.grey[300],
  padding: "14px 20px",
  "& div.MuiGrid2-root:first-child div": {
    justifyContent: "start",
  },
}));
const TableRow = styled(Grid)(({ theme }) => ({
  backgroundColor: theme.palette.incutix.white,
  padding: "14px 20px",
  borderBottom: `1px solid ${theme.palette.incutix.grey[300]}`,
  "&:last-child": {
    border: 0,
  },
}));

const TableHeaderCell = styled(Box)(({ theme }) => ({
  fontSize: 15,
  fontWeight: 400,
  color: theme.palette.incutix.grey[600],
  cursor: "default",
  justifyContent: "end",
  display: "flex",
  alignItems: "center",
}));
const TableCell = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  fontSize: 14,
  fontWeight: 400,
  color: "#000000",
  justifyContent: "end",
  "& div.sku-attribute": {
    fontSize: 12,
    fontWeight: 400,
    color: theme.palette.incutix.grey[600],
  },
}));

const joinSkuAttributeText = (attributes: { value: string; category: string }[]) => {
  return attributes
    .map((x) => {
      const c = `${x.category.slice(0, 1).toUpperCase()}${x.category.slice(1)}`;
      return `${c}: ${x.value}`;
    })
    .join(", ");
};

const SalesOrderItemSummary = ({ saleOrderDetail }: Props) => {
  const t = useTranslations("sales_orders.detail.item_summary");

  const eventName = useMemo<string>(
    () => saleOrderDetail?.events?.[0]?.eventName ?? "",
    [saleOrderDetail]
  );
  const data: ItemSummaryDataType[] = useMemo<ItemSummaryDataType[]>(() => {
    const result = saleOrderDetail?.orderItems.reduce<ItemSummaryDataType[]>((a, v) => {
      const newData: ItemSummaryDataType = {
        name: v.productName,
        image: v.thumbnail,
        price: v.price.unitPrice,
        qty: v.quantity,
        total: v.price.totalPrice,
        currency: saleOrderDetail.currency,
        skuAttribute: v.skuAttribute,
      };

      return [...a, newData];
    }, []);
    return result ?? [];
  }, [saleOrderDetail]);

  return (
    <StyledBox elevation={0}>
      <DetailTitle>{t("title")}</DetailTitle>
      <DetailSubTitle>{eventName}</DetailSubTitle>
      <Grid container sx={{ borderRadius: 16, marginBottom: 1 }}>
        <TableHeaderRow container xs={12}>
          <Grid xs={7}>
            <TableHeaderCell>{t("table.header.items")}</TableHeaderCell>
          </Grid>
          <Grid xs>
            <TableHeaderCell>{t("table.header.unit_price")}</TableHeaderCell>
          </Grid>
          <Grid xs>
            <TableHeaderCell>{t("table.header.qty")}</TableHeaderCell>
          </Grid>
          <Grid xs>
            <TableHeaderCell>{t("table.header.total")}</TableHeaderCell>
          </Grid>
        </TableHeaderRow>
        {data.map((row) => (
          <TableRow container xs={12} key={row.name}>
            <Grid xs={7}>
              <Stack
                direction={"row"}
                spacing={2}
                sx={{ width: "100%", "& img": { borderRadius: 2 } }}
              >
                <Image src={row.image} width={40} height={40} alt={"item-summary-product"} />
                <TableCell>
                  <Stack direction={"column"}>
                    <Box>{row.name}</Box>
                    <Box className={"sku-attribute"}>{joinSkuAttributeText(row.skuAttribute)}</Box>
                  </Stack>
                </TableCell>
              </Stack>
            </Grid>
            <Grid xs>
              <TableCell
                sx={{ height: "100%", width: "100%", justifyItems: "flex-end" }}
              >{`${getCurrencySymbol(row.currency)} ${formatCurrency(row.price)}`}</TableCell>
            </Grid>
            <Grid xs>
              <TableCell sx={{ height: "100%", justifyItems: "flex-end" }}>{row.qty}</TableCell>
            </Grid>
            <Grid xs>
              <TableCell sx={{ height: "100%", justifyItems: "flex-end" }}>{`${getCurrencySymbol(
                row.currency
              )} ${formatCurrency(row.total)}`}</TableCell>
            </Grid>
          </TableRow>
        ))}
      </Grid>
    </StyledBox>
  );
};
// {`${currencySymbol} ${data.orderSummary.orderTotalAmount}`}
export default SalesOrderItemSummary;
