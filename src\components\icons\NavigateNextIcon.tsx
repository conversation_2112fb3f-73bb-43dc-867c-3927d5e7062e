"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const NavigateNextIcon = createSvgIcon(
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_6137_46667)">
      <path
        d="M8.33333 14.166L12.5 9.99935"
        stroke="#777E90"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 10L8.33333 5.83333"
        stroke="#777E90"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6137_46667">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>,
  "NavigateNextIcon"
);

export default NavigateNextIcon;
