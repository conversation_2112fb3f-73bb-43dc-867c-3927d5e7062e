"use client";
import * as React from "react";
import { TextField as MuiTextField, styled } from "@mui/material";
import { TextFieldProps } from "@mui/material/TextField";
import InputContainer, { InputContainerProps } from "./InputContainer";

interface Props extends InputContainerProps {}

const CustomTextField = styled(MuiTextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: '999px', // Customize the border radius
  },
});

export default function TextField({
  label,
  labelStyle,
  description,
  variant = "outlined",
  fullWidth = true,
  error,
  required,
  blockStyle,
  placeholder,
  ...otherProps
}: Props & Omit<TextFieldProps, "error">) {
  return (
      <CustomTextField
        variant={variant}
        fullWidth={fullWidth}
        error={!!error}
        label={label}
        InputLabelProps={{ shrink: true }}
        placeholder={placeholder}
        {...otherProps}
      />
  );
}
