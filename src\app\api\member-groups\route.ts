import { IListResponse } from "@/interface/IListResponse";
import { IMemberGroup } from "@/interface/IMemberGroup";
import {
  authorizedGet,
  authorizedPost,
  getAuthHeaders,
  handleApiError,
} from "@/utils/api";
import { NextRequest } from "next/server";

async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const data = await authorizedGet<IListResponse<IMemberGroup>>(
      "/member-groups",
      await getAuthHeaders(req),
      {
        page: searchParams.get("page"),
        // size: searchParams.get("size"),
      }
    );

    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

async function POST(req: NextRequest) {
  try {
    const groups = await req.json();
    const data = await authorizedPost<IMemberGroup>(
      "/member-groups",
      await getAuthHeaders(req),
      groups
    );
    return Response.json(data, { status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}

export { GET, POST };
