"use server";
import { revalidateTag } from "next/cache";
import { RetrieveOrderListResponseType } from "../_schema/saleOrder.schema";
import { fetchData } from "./api";

const URL = `${process.env.NEXT_PUBLIC_API_BASE}/api/admin/v1/order?page=1&size=9999&language=en`;

export const retrieveOrderList = async () => {
  const resultResponse = await fetchData<RetrieveOrderListResponseType>(URL, {
    next: { revalidate: 0, tags: ["retrieveOrderList"] },
  });

  const { items, meta } = resultResponse?.data ?? {};
  return { items, meta };
};

export const revalidateRetrieveOrderList = () => {
  revalidateTag("retrieveOrderList");
};
