"use client";
import * as React from "react";
import { Box, Button, Modal, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import DeleteButton from "@/components/buttons/DeleteButton";
import ModalContainer from "@/components/ModalContainer";
import { IRoom } from "@/interface/IRoom";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";

interface Props {
  room: IRoom;
}
const DeleteRoom = ({ room }: Props) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const mutation = useMutation({
    mutationFn: () => xior.delete(`/api/rooms/${room.id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["rooms"] });
      setOpen(false);
      router.push(ROUTES.ROOMS);
    },
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (mutation.isPending) return;
    setOpen(false);
  }, [mutation.isPending]);

  return (
    <Box>
      <DeleteButton onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer sx={{ alignItems: "center", minWidth: 510 }}>
          <Typography fontSize={20} fontWeight={400} my={5}>
            {t("common.title_confirm_delete")}
          </Typography>

          <Box display={"flex"} flexDirection={"row"} minWidth={300}>
            <Button
              variant="contained"
              color="secondary"
              disabled={mutation.isPending}
              sx={{ color: COLORS.BLACK, flex: 1 }}
              onClick={handleClose}
            >
              {t("common.button_stay_in_page")}
            </Button>
            <Box width={36} />
            <Button
              variant="contained"
              disabled={mutation.isPending}
              onClick={() => mutation.mutate()}
              sx={{ flex: 1 }}
            >
              {t("common.button_delete")}
            </Button>
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default DeleteRoom;
