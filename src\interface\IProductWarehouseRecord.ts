export interface IProductWarehouseRecords {
    id?: number;
    productWarehouseId: number;
    unavailableChanged?: number | null;
    currentUnavailable?: number | null;
    committedChanged?: number | null;
    currentCommitted?: number | null;
    returnedChanged?: number | null;
    currentReturned?: number | null;
    quantityChanged?: number | null;
    currentQuantity?: number | null;
    sku?:string;
    updatedAt?: number;
    createdAt?: number;
    userName?: string;
}