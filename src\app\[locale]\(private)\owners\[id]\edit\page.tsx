"use client";
import PageHeader from "@/components/PageHeader";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import FileInput from "@/components/input/FileInput";
import SelectCountry from "@/components/input/SelectCountry";
import SelectGender from "@/components/input/SelectGender";
import SocialMediaInput from "@/components/input/SocialMediaInput";
import TextField from "@/components/input/TextField";
import useFileUpload from "@/hooks/useFileUpload";
import { IOwner } from "@/interface/IOwner";
import { OwnerSchema } from "@/schemas/OwnerSchema";
import { ROUTES, SOCIAL_MEDIAS } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, CircularProgress, Typography } from "@mui/material";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior, { XiorError } from "xior";
import { z } from "zod";
import DeleteOwner from "../../components/DeleteOwner";
import AddNewButton from "@/components/buttons/AddNewButton";
import SocialNetworkInput from "@/components/input/SocialNetworkInput";

type FormValue = z.infer<typeof OwnerSchema>;
const defaultValues = {
  gender: "",
  countryCode: "hk",
  introduction: "",
  photoUrl: "",
  name: "",
  email: "",
  socialUrl: [],
};

interface Props {
  params: { id: string };
}

const EditOwner = ({ params: { id } }: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const router = useRouter();
  const { uploadFile } = useFileUpload();

  const [socialNetwork, setSocialNetwork] = useState<any[]>([]);

  const {
    handleSubmit,
    control,
    formState: { isDirty, isSubmitting, errors },
    setError,
    getValues,
    reset,
  } = useForm<FormValue>({
    // resolver: zodResolver(OwnerSchema),
    defaultValues,
  });

  const { data, isLoading, isLoadingError } = useQuery({
    queryKey: ["owner", { id }],
    queryFn: () =>
      xior.get<IOwner>(`/api/owners/${id}`).then((res) => res.data),
  });

  useEffect(() => {
    if (data) {
      const { countryCode, socialUrls, ...otherValues } = data;
      const parsedSocialUrl = socialUrls?.map((item) => ({ ...item, value: item.url }))
      setSocialNetwork(parsedSocialUrl ?? [])
      reset({
        ...otherValues,
        gender: otherValues.gender || "",
        countryCode: countryCode || "hk",
        socialMedias: parsedSocialUrl
      });
    }
  }, [data, reset]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const { photoUrl, socialMedias, ...otherData } = data;
      console.log("socialMedias >> ", socialMedias)
      await xior.put(`/api/owners/${id}`, {
        ...otherData,
        gender: otherData.gender || null,
        photoUrl: await uploadFile(photoUrl, "owners"),
        // socialMedias: socialMedias.map((item) => ({ method: item.method, url: item.url }))
        socialMedias: socialMedias.map((item) => ({ method: item.method, url: item.value }))
      });
      await queryClient.invalidateQueries({ queryKey: ["owner"] });
      router.push(`${ROUTES.OWNER}`);
    } catch (e) {
      console.log("err >> ", e)
      if (e instanceof XiorError) {
        switch (e.response?.data) {
          case "email_is_duplicated":
            setError("email", { message: t("email_is_duplicated") });
            break;
        };

        return;
      }

      setError("name", { message: "unknown_error" });
    }
  };

  useEffect(() => {
    if (isLoadingError) router.push(ROUTES.OWNER);
  }, [isLoadingError, router]);

  // const handleSocialNetworkOnChange = (value: any, field: any, idx: number, setter: Function) => {
  //   if (field === 'value') field = 'url'
  //   const cloneSocialNetwork: Array<any> = [...socialNetwork ?? []]

  //   cloneSocialNetwork[idx] = {
  //     ...cloneSocialNetwork[idx],
  //     [field]: value
  //   }

  //   setter(cloneSocialNetwork)
  //   setSocialNetwork(cloneSocialNetwork)
  // }
  const handleSocialNetworkOnChange = () => { }

  const removeSocialNetwork = (idx: number, setter: Function) => {
    const cloneSocialNetwork: Array<any> = [...socialNetwork ?? []]

    cloneSocialNetwork.splice(idx, 1)

    setter(cloneSocialNetwork)
    setSocialNetwork(cloneSocialNetwork)
  }

  const socialNetworkInputs = useMemo(() => {
    return (
      <>
        <label>{t("profile.label_social_media")}</label>
        <Controller
          key={`socialMedias`}
          name={`socialMedias` as any}
          control={control}
          render={({ field }) => (
            <>
              {
                socialNetwork?.map((item, index) => (
                  <SocialNetworkInput
                    // key={item.id}
                    key={index}
                    socialNetwork={item as any}
                    value={item}
                    index={index}
                    setter={field.onChange}
                    remove={removeSocialNetwork}
                    onChange={handleSocialNetworkOnChange}
                    disabled={isSubmitting}
                  // error={(errors?.socialMedias as any)?.[item]}
                  />
                ))
              }
            </>
          )}
        />
        <AddNewButton sx={{ width: 100 }} onClick={() => setSocialNetwork([...socialNetwork, {}])} />
      </>
    );
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [socialNetwork, getValues("socialMedias"), control, errors?.socialMedias, isSubmitting, t]);

  if (isLoading) {
    return (
      <Box
        sx={{ height: "100%" }}
        display={"flex"}
        justifyContent="center"
        alignItems={"center"}
      >
        <CircularProgress color="primary" />
      </Box>
    );
  }

  return (
    <Box
      sx={{ height: "100%" }}
      display={"flex"}
      flexDirection={"column"}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
    >
      <PageHeader title={[t("owner.label_title"), data?.name || ""]}>
        <>
          {data && <DeleteOwner owner={data} />}
          <CancelButton
            disabled={isSubmitting}
            onAction={() => router.push(`${ROUTES.OWNER}`)}
          />
          <SaveButton disabled={isSubmitting || !isDirty} />
        </>
      </PageHeader>
      <Box
        flex={1}
        padding="26px 34px"
        display={"flex"}
        flexDirection={"column"}
        maxWidth={450}
      >
        <Controller
          name="photoUrl"
          control={control}
          render={({ field }) => (
            <>
              <FileInput
                value={field.value}
                onChange={field.onChange}
                metadata={t("file_upload.icon_metadata")}
                disabled={isSubmitting}
                error={errors.photoUrl?.message}
                type="image"
                size="small"
              />
            </>
          )}
        />
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              required
              fullWidth={false}
              disabled={isSubmitting}
              label={t("owner.label_name")}
              error={errors?.name?.message}
              sx={{ width: 360 }}
            />
          )}
        />
        <Controller
          name="gender"
          control={control}
          render={({ field }) => (
            <SelectGender
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("owner.label_gender")}
              error={errors?.gender?.message}
              sx={{ width: 360 }}
            />
          )}
        />

        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              fullWidth={false}
              disabled={isSubmitting}
              label={t("owner.label_email")}
              error={errors?.email?.message}
              sx={{ width: 360 }}
            />
          )}
        />
        <Controller
          name="introduction"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("owner.label_owner_introduction")}
              error={errors?.introduction?.message}
              multiline
              rows={8}
            />
          )}
        />
        <Controller
          name="countryCode"
          control={control}
          render={({ field }) => (
            <SelectCountry
              value={field.value}
              onChange={field.onChange}
              disabled={isSubmitting}
              label={t("owner.label_country")}
              error={errors?.countryCode?.message}
              width={360}
              fullWidth
            />
          )}
        />
        {socialNetworkInputs}
      </Box>
    </Box>
  );
};

export default EditOwner;
