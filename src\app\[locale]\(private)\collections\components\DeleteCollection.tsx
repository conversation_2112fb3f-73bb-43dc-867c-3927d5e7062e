"use client";
import * as React from "react";
import { Box, Button, Modal, Typography } from "@mui/material";
import { COLORS } from "@/styles/colors";
import { useTranslations } from "next-intl";
import xior from "xior";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import DeleteButton from "@/components/buttons/DeleteButton";
import ModalContainer from "@/components/ModalContainer";
import { ICollection } from "@/interface/ICollection";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";

interface Props {
  collection: ICollection;
}

const buttonStyle = {
  flex: 1,
  fontSize: "1rem",
  fontWeight: 700,
  minWidth: "132px",
  minHeight: "46px",
};

const DeleteCollection = ({ collection }: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const mutation = useMutation({
    mutationFn: () => xior.delete(`/api/collections/${collection.id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["collections"]});
      router.push(`${ROUTES.COLLECTION}`);
      setOpen(false);
    },
  });

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (mutation.isPending) return;
    setOpen(false);
  }, [mutation.isPending]);

  return (
    <Box>
      <DeleteButton onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer
          sx={{
            alignItems: "center",
            minWidth: 510,
            minHeight: "300px",
            boxSizing: "border-box",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
          }}
        >
          <Box my={2} textAlign="center">
            <Typography fontWeight={700} fontSize={"1.25rem"}>
              {t("collection.title_confirm_delete")}
            </Typography>
            <Typography fontWeight={700} fontSize={"1.25rem"}>
              {t("collection.sub_title_confirm_delete")}
            </Typography>
          </Box>
          <Box display={"flex"} flexDirection={"row"} minWidth={300}>
            <Button
              variant="contained"
              color="secondary"
              disabled={mutation.isPending}
              sx={{ color: COLORS.BLACK, ...buttonStyle }}
              onClick={handleClose}
            >
              {t("common.button_stay_in_page")}
            </Button>
            <Box width={36} />
            <Button
              variant="contained"
              disabled={mutation.isPending}
              onClick={() => mutation.mutate()}
              sx={buttonStyle}
            >
              {t("common.button_delete")}
            </Button>
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default DeleteCollection;
