"use client"
import { useTranslations } from 'next-intl';
//   import LanguageSwitcher from '@/components/LanguageSwitcher';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { Box, IconButton, Typography } from '@mui/material';
import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import ImagesSlider from '@/components/ImagesSlider';
import FeaturedEvent from '@/components/featured-events/page';


const Home = () =>{
    return(
        <>
        {/* <ImagesSlider
            images={images}
            interval={5000} 
            height="400px" 
            blurIntensity={100} 
        />
        <FeaturedEvent/> */}
        </>
    )
}

export default Home;