import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import InventoryRow from './InventoryRow';
import { IInventory } from "@/interface/IInventory";

type Props ={
  getInventorylist:IInventory[]
}

const InventoryTable = ({getInventorylist}:Props) =>{
    return(
        <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell></TableCell>
              <TableCell ></TableCell>
              <TableCell>Thumbnail</TableCell>
              <TableCell>Product Name</TableCell>
              <TableCell>SKU</TableCell>
              <TableCell>Product Type</TableCell>
              <TableCell>Unavailable</TableCell>
              <TableCell>Committed</TableCell>
              <TableCell>Available</TableCell>
              <TableCell>Sold Products</TableCell>
              <TableCell>Total Inventory</TableCell>
              <TableCell>Updated At</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {/* {Array.from({ length: 5 }, (_, index) => (
                <InventoryRow key={index} />
            ))} */}

            {
              getInventorylist && 
              getInventorylist.map((value)=>(
                <InventoryRow key={value.id} getInventoryDto={value} />
              ))
            }
          </TableBody>
        </Table>
      </TableContainer>
    )
}

export default InventoryTable