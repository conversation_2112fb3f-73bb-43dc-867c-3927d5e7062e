export const checkPasswordLength = (str: string): boolean => {
    return str.length >= 8 && str.length <= 20;
  };
  

  export const checkUppercase = (str: string): boolean => {
    const uppercasePattern = /[A-Z]/;
    return uppercasePattern.test(str);
  };
  

  export const checkLowercase = (str: string): boolean => {
    const lowercasePattern = /[a-z]/;
    return lowercasePattern.test(str);
  };
  
  export const checkSpecialCharacter = (str: string): boolean => {
    const specialCharPattern = /[!@#$%^&*(),.?":{}|<>]/;
    return specialCharPattern.test(str);
  };
  
  export const checkNumber = (str: string): boolean => {
    const numberPattern = /\d/;
    return numberPattern.test(str);
  };
