import { NextRequest} from "next/server";
import {SalesOrderDNProduct} from "@/interface/ISaleOrderDNProduct"
import {
    authorizedGet,
    getAuthHeaders,
    handleApiError,
} from "@/utils/api";

async function GET(req: NextRequest, { params }: { params: { noteId: string } }){
    try{

        const data = await authorizedGet<SalesOrderDNProduct>(
            `/sales-orders/product/${params.noteId}`,
            await getAuthHeaders(req)
        );
        return Response.json(
            data,
            { status: 200 }
          );
    }catch(error){
        console.error(error); 
        return handleApiError(error);
    }
}

export { GET};