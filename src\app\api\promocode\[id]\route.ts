import { NextRequest } from "next/server";
import { IPromoCode } from "@/interface/IPromoCode";
import {
    authorizedPost,
    authorizedGet,
    getAuthHeaders,
    handleApiError,
} from "@/utils/api";

async function GET(req: NextRequest, { params }: { params: { id: string } }) {
    try {
        const data = await authorizedGet<IPromoCode>(
            `/promocode/${params.id}`,
            await getAuthHeaders(req)
        );
        return Response.json(data, { status: 200 });
    } catch (error) {
        console.error('Error fetching promo code:', error);
        return handleApiError(error); 
    }
}

async function POST(req: NextRequest, { params }: { params: { promoId: string; finalAmount: string } }) {

    const emptyBody = await req.json().catch(() => ({}));
    try {
        const response = await authorizedPost(
            `/promocode/calculate/${params.promoId}/${params.finalAmount}`,
            await getAuthHeaders(req),
            emptyBody
        );

        return Response.json(response, { status: 200 });
    } catch (error) {
        console.error('Error calculating amount:', error);
        return handleApiError(error);
    }
}

export { GET , POST };