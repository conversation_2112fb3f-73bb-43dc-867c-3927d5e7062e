"use client";
import { <PERSON>, Button, CircularProgress, Stack, styled } from "@mui/material";
import { useTranslations } from "next-intl";
import ExportToExcelIcon from "@/components/icons/ExportToExcelIcon";
import React, { useState } from "react";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

type SalesOrderTitleProps = {
  orderNo: string;
  contentRef: React.RefObject<HTMLDivElement>;
};

const ExportButton = styled(Button)(({ theme }) => {
  return {
    borderRadius: "999px",
    padding: "12px 24px",
    backgroundColor: theme.palette.incutix.primary[200],
    ":hover": {
      backgroundColor: theme.palette.incutix.primary[100],
    },
    "& span.MuiCircularProgress-root": {
      color: theme.palette.incutix.white,
    },
  };
});
const StyledText = styled(Box)(({ theme }) => ({
  fontSize: 24,
  fontWeight: 900,
  display: "flex",
  alignItems: "center",
}));

const SalesOrderTitle = ({ orderNo, contentRef }: SalesOrderTitleProps) => {
  const t = useTranslations("sales_orders.detail");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleExport = async () => {
    const element = contentRef.current;
    if (!element) return;
    setIsLoading(true);
    try {
      const canvas = await html2canvas(element, { scale: 4 });
      const imgData = canvas.toDataURL("image/png");

      const pdf = new jsPDF("p", "mm", "a4");
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

      pdf.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);
      pdf.save(`sales-order_${orderNo}.pdf`);
    } catch (error) {
      console.error("Failed to export PDF:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Stack
      spacing={{ xs: 2 }}
      sx={{ padding: "8px 0px" }}
      direction="row"
      useFlexGap
      flexWrap="wrap"
      justifyContent={"center"}
      alignContent={"center"}
    >
      <StyledText>{t("title", { orderNo })}</StyledText>
      <Box
        display={"flex"}
        flexDirection={"row"}
        justifyContent={"end"}
        flexGrow={1}
        sx={{ paddingRight: 4 }}
      >
        <ExportButton
          disableElevation
          disableRipple
          disableFocusRipple
          disableTouchRipple
          variant="contained"
          size="large"
          startIcon={isLoading ? <></> : <ExportToExcelIcon />}
          onClick={handleExport}
        >
          {!isLoading ? t("download_pdf") : <CircularProgress size={24} />}
        </ExportButton>
      </Box>
    </Stack>
  );
};

export default SalesOrderTitle;
