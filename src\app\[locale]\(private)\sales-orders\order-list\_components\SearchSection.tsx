"use client";
import {
  Box,
  InputAdornment,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  TextField,
  Typography,
  styled,
} from "@mui/material";
import { useTranslations } from "next-intl";
import SearchIcon from "@/components/icons/SearchIcon";
import { useSaleOrderListShowPerPage, useSaleOrderListSearch } from "../../_hooks";
import CSVExporter from "./CSVExporter/CSVExporter";
import { OrderListType } from "../../_schema/saleOrder.schema";

type SearchSectionProps = {
  data: OrderListType[];
};

const SearchTextField = styled(TextField)(({ theme }) => ({
  "& .MuiInputBase-root": {
    width: "300px",
    borderRadius: 999,
    border: `1px solid ${theme.palette.incutix.grey[400]} !important`,
    // padding: "15px 20px",
    paddingLeft: 20,
  },
  "& input.MuiInputBase-input": {
    padding: "16px 4px",
    fontSize: "14px",
    fontWeight: 700,
  },
  "& fieldset": {
    border: 0,
  },
}));

const ShowPerPageSelect = styled(Select, {
  shouldForwardProp: (propName: string) => propName !== "paddingText",
})<{ paddingText: string }>(({ theme, paddingText }) => ({
  "&.MuiInputBase-root": {
    width: "300px",
    borderRadius: 999,
    border: `1px solid ${theme.palette.incutix.grey[400]} !important`,
  },
  "& .MuiSelect-select": {
    padding: "12px 16px",
    fontSize: "14px",
    fontWeight: 700,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    "& p.ShowPerPageSelect-value": {
      fontSize: "14px",
      fontWeight: 700,
    },
    "&::before": {
      content: `'${paddingText}:'`,
      marginRight: theme.spacing(1),
    },
  },
  "& fieldset": {
    border: 0,
  },
}));

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  color: "#000000",
  "&.Mui-selected": {
    backgroundColor: theme.palette.incutix.primary[200],
    color: "#FFFFFF",
    "&:hover": {
      backgroundColor: theme.palette.incutix.primary[200],
      color: "#FFFFFF",
    },
  },
  "&:hover": {
    backgroundColor: theme.palette.incutix.primary[400],
  },
}));

const SearchSection = ({ data }: SearchSectionProps) => {
  const t = useTranslations("sales_orders");

  const [rowsPerPage, setRowsPerPage] = useSaleOrderListShowPerPage();
  const [searchText, setSearchText] = useSaleOrderListSearch();

  const handleChange = (event: SelectChangeEvent<unknown>) => {
    const value: string = event.target.value as string;
    setRowsPerPage(value);
  };

  const handleSearchTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(event.target.value);
  };

  return (
    <Stack
      spacing={{ xs: 2 }}
      sx={{ padding: "8px 0px" }}
      direction="row"
      useFlexGap
      flexWrap="wrap"
      justifyContent={"center"}
    >
      <SearchTextField
        variant="outlined"
        placeholder={t("search")}
        value={searchText}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
        onChange={handleSearchTextChange}
      />
      <ShowPerPageSelect
        value={rowsPerPage}
        onChange={handleChange}
        paddingText={t("showPerPage")}
        renderValue={(value: any) => (
          <Typography className="ShowPerPageSelect-value">{value}</Typography>
        )}
      >
        <StyledMenuItem value={10}>10</StyledMenuItem>
        <StyledMenuItem value={25}>25</StyledMenuItem>
        <StyledMenuItem value={50}>50</StyledMenuItem>
      </ShowPerPageSelect>
      <Box
        display={"flex"}
        flexDirection={"row"}
        justifyContent={"end"}
        flexGrow={1}
        sx={{ paddingRight: 4 }}
      >
        <CSVExporter data={data} />
      </Box>
    </Stack>
  );
};

export default SearchSection;
